using GlobalTrader2.Core.Constants;
using GlobalTrader2.Core.Exceptions;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.HubrfqExport.Queries.Dtos;

namespace GlobalTrader2.Orders.UserCases.Orders.Requirements.HubrfqExport.Queries
{
    public class GetCustomerRequirementHubrfqExportHandler : IRequestHandler<GetCustomerRequirementHubrfqExportQuery, BaseResponse<IEnumerable<CustomerRequirementHubrfqExportDto>>>
    {
        private readonly IBaseRepository<CustomerRequirementHubrfqExportReadModel> _repository;
        private readonly IMapper _mapper;

        public GetCustomerRequirementHubrfqExportHandler(IBaseRepository<CustomerRequirementHubrfqExportReadModel> repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }

        public async Task<BaseResponse<IEnumerable<CustomerRequirementHubrfqExportDto>>> Handle(GetCustomerRequirementHubrfqExportQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<IEnumerable<CustomerRequirementHubrfqExportDto>>();

            SqlParameter[] parameters =
            [
                new SqlParameter("@ClientId", SqlDbType.Int) { Value = request.ClientId ?? (object)DBNull.Value },
                new SqlParameter("@OrderBy", SqlDbType.Int) { Value = request.OrderBy ?? (object)DBNull.Value },
                new SqlParameter("@SortDir", SqlDbType.Int) { Value = request.SortDir ?? (object)DBNull.Value },
                new SqlParameter("@BOMId", SqlDbType.Int) { Value = DBNull.Value },
                new SqlParameter("@BOMCode", SqlDbType.NVarChar, 20) { Value = request.BomCode ?? (object)DBNull.Value },
                new SqlParameter("@BOMName", SqlDbType.NVarChar, 50) { Value = request.BomName ?? (object)DBNull.Value },
                new SqlParameter("@IsPoHub", SqlDbType.Bit) { Value = request.IsPOHub ?? (object)DBNull.Value },
                new SqlParameter("@ClientNo", SqlDbType.Int) { Value = request.SelectedClientNo ?? (object)DBNull.Value },
                new SqlParameter("@BomStatus", SqlDbType.Int) { Value = request.BomStatus ?? (object)DBNull.Value },
                new SqlParameter("@TeamId", SqlDbType.Int) { Value = request.TeamId ?? (object)DBNull.Value },
                new SqlParameter("@DivisionId", SqlDbType.Int) { Value = request.DivisionId ?? (object)DBNull.Value },
                new SqlParameter("@LoginId", SqlDbType.Int) { Value = request.LoginId ?? (object)DBNull.Value },
                new SqlParameter("@AssignedUser", SqlDbType.Int) { Value = request.AssignedUser ?? (object)DBNull.Value },
                new SqlParameter("@Manufacturer", SqlDbType.NVarChar, 50) { Value = request.Manufacturer ?? (object)DBNull.Value },
                new SqlParameter("@PartSearch", SqlDbType.NVarChar, 50) { Value = request.Part ?? (object)DBNull.Value },
                new SqlParameter("@ClientDivisionNo", SqlDbType.Int) { Value = request.IntDivision ?? (object)DBNull.Value },
                new SqlParameter("@StartDate", SqlDbType.DateTime) { Value = request.StartDate ?? (object)DBNull.Value },
                new SqlParameter("@EndDate", SqlDbType.DateTime) { Value = request.EndDate ?? (object)DBNull.Value },
                new SqlParameter("@SalesPerson", SqlDbType.Int) { Value = request.SalesPerson ?? (object)DBNull.Value }
            ];

            var queryStr = $"{StoredProcedures.DataListNugget_CustomerRequirementForHUBRFQ_Export} @ClientId, @OrderBy, @SortDir, @BOMId, @BOMCode, @BOMName, @IsPoHub, @ClientNo, @BomStatus, @TeamId, @DivisionId, @LoginId, @AssignedUser, @Manufacturer, @PartSearch, @ClientDivisionNo, @StartDate, @EndDate, @SalesPerson";
            try
            {
                var result = await _repository.SqlQueryRawAsync(queryStr, parameters, CommandTimeout.Timeout180Seconds);
                response.Success = true;
                response.Data = _mapper.Map<IEnumerable<CustomerRequirementHubrfqExportDto>>(result);

                return response;
            }
            catch (SqlException ex)
            {
                throw new GlobalTrader2.Core.Exceptions.ValidationException(new List<BaseError>() {
                    new BaseError()
                    {
                        ErrorMessage = $"Database timeout occurred."
                    }
                });
            }
            catch (OutOfMemoryException)
            {
                throw new GlobalTrader2.Core.Exceptions.ValidationException(new List<BaseError>() {
                    new BaseError()
                    {
                        ErrorMessage = $"Server out of memory."
                    }
                });
            }            
        }
    }
}
