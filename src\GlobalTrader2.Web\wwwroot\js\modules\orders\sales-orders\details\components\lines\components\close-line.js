﻿export class CloseLineManager {
    constructor() {
        this._selectedRow = null;
        this.$dialog = null;
        this._customerName = null;
    }

    async handleSubmit() {
        $("#close-line-dialog").dialog("setLoading", true)
        $("#yes-btn").prop("disabled", true)
        const resetQuantity = $("#reset-checkbox").is(":checked");
        const soLineId = this._selectedRow.lineId
        const request = {
            SalesOrderLineId: soLineId,
            ResetQuantity: resetQuantity
        };
        const response = await GlobalTrader.ApiClient.putAsync(`/orders/sales-order/so-lines/close-line`, request);

        $("#close-line-dialog").dialog("setLoading", false)
        $("#yes-btn").prop("disabled", false)
        if (!response.success) {
            return showToast("danger","An error occured.")
        }
        $("#close-line-dialog").dialog("close");
        showToast("success", window.localizedStrings.saveChangedMessage)
        this.$dialog.trigger('saveSuccess', soLineId)
    }
 
    setupDialog() {
        this.$dialog = $("#close-line-dialog").dialog({
            height: 'auto',
            width: 'auto',
            maxHeight: $(window).height(),
            buttons: [
                {
                    id: "yes-btn",
                    text: window.localizedStrings.yes,
                    click: async () => {
                        this.handleSubmit()
                    }
                },
                {
                    text: window.localizedStrings.no,
                    click: () => { 
                        $("#close-line-dialog").dialog("close")
                        $("#reset-checkbox").prop('checked', true);
                    }

                }
            ],
            open: () => {
                $('#close-line-customer').html(`${this._customerName}`);
                $('.ui-dialog-titlebar-close').remove();
            }
        })
        $(".ui-dialog-buttonpane .ui-dialog-buttonset button:contains('Yes')").addClass("btn btn-primary fw-normal").html('<img src="/img/icons/check.svg" alt="Yes"> Yes');
        $(".ui-dialog-buttonpane .ui-dialog-buttonset button:contains('No')").addClass("btn btn-danger fw-normal").html('<img src="/img/icons/xmark.svg" alt="No"> No');

        $('#lines-close-btn').button().on('click', () => {
            $("#close-line-dialog").dialog("open");
        });
    }

    updateSelectedRow(selectedRow) {
        this._selectedRow = selectedRow;
    }
}

