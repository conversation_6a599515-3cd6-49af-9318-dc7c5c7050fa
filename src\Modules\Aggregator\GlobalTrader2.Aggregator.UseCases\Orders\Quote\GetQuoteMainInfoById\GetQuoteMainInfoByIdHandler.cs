﻿using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Dto.Quote;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CompanyAdvisoryNote.CompanyAdvisoryNote.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Quotes.Queries.GetQuoteById;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetCurrencyRateCurrentAtDate;
using GlobalTrader2.UserAccount.UseCases.LoginManager.Queries.GetNameByLoginId;
using System.Globalization;

namespace GlobalTrader2.Aggregator.UseCases.Orders.Quote.GetQuoteMainInfoById
{
    public class GetQuoteMainInfoByIdHandler : IRequestHandler<GetQuoteMainInfoByIdQuery, BaseResponse<QuoteMainInfoDto>>
    {
        private readonly IMapper _mapper;
        private readonly ISender _sender;
        private readonly IBaseRepository<Currency> _currencyRepository;

        public GetQuoteMainInfoByIdHandler(IMapper mapper, 
            ISender sender,
            IBaseRepository<Currency> currencyRepository)
        {
            _mapper = mapper;
            _sender = sender;
            _currencyRepository = currencyRepository;
        }

        public async Task<BaseResponse<QuoteMainInfoDto>> Handle(GetQuoteMainInfoByIdQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<QuoteMainInfoDto>();

            var quoteResult = await _sender.Send(new GetQuoteByIdQuery() { QuoteId = request.quoteId }, cancellationToken);

            if (!quoteResult.Success) return response;
            var quoteDetails = _mapper.Map<QuoteMainInfoDto>(quoteResult.Data);

            string companyAdvisoryNotes = (await _sender.Send(
                new GetCompanyAdvisoryNoteQuery() { Id = quoteDetails.CompanyNo },
                cancellationToken)).Data ?? "";
            quoteDetails.CompanyAdvisoryNotes = Functions.ReplaceLineBreaks(companyAdvisoryNotes);

            decimal? freightInBaseCurrency;
            string freightStr = "";
            if (quoteDetails.CurrencyNo != request.ClientCurrencyID)
            {
                var currencyId = quoteDetails.CurrencyNo;
                if (quoteDetails.ClientNo != request.clientId)
                {
                    var baseCurrency = await _currencyRepository
                    .GetAsync(c => c.CurrencyId == quoteDetails.CurrencyNo);
                    var currency = await _currencyRepository.GetAsync(c => c.ClientNo == request.clientId && c.GlobalCurrencyNo == baseCurrency.GlobalCurrencyNo && c.Sell);
                    if (currency != null)
                    {
                        currencyId = currency.CurrencyId;
                    }
                }
                var currentRateAtDate = (await _sender.Send(
                   new GetCurrencyRateCurrentAtDateQuery(currencyId, quoteDetails.DateQuoted),
                   cancellationToken)).Data;
                freightInBaseCurrency = Functions.ConvertValueToBaseCurrencyDecimal(Convert.ToDecimal(quoteDetails.Freight), Convert.ToDecimal(currentRateAtDate));
                freightStr = string.Format("{0} ({1})",
                    Functions.FormatCurrency(quoteDetails.Freight, CultureInfo.InvariantCulture, quoteDetails.CurrencyCode, 2, true),
                    Functions.FormatCurrency(freightInBaseCurrency, CultureInfo.InvariantCulture, request.ClientCurrencyCode, 2, true)
                );
            }
            else
            {
                freightInBaseCurrency = Convert.ToDecimal(quoteDetails.Freight);
                freightStr = Functions.FormatCurrency(quoteDetails.Freight, CultureInfo.InvariantCulture, request.ClientCurrencyCode, 2, true);
            }
            quoteDetails.FreightStr = freightStr;
            quoteDetails.FreightRaw = Functions.FormatCurrency(quoteDetails.Freight, CultureInfo.InvariantCulture, null, 2, true);
            quoteDetails.FreightInBaseCurrency = Functions.FormatCurrency(freightInBaseCurrency, CultureInfo.InvariantCulture, null, 2, true);
            if (request.isAllowCheckedCompanyOnStop && quoteDetails.CompanyOnStop.GetValueOrDefault())
            {
                quoteDetails.CanCreateSO = quoteDetails.OpenLines.GetValueOrDefault() > 0
                    && quoteDetails.CompanySOApproved.GetValueOrDefault()
                    && quoteDetails.CompanyOnStop.GetValueOrDefault();
            }
            else
            {
                quoteDetails.CanCreateSO = quoteDetails.OpenLines.GetValueOrDefault() > 0
                    && quoteDetails.CompanySOApproved.GetValueOrDefault()
                    && !quoteDetails.CompanyOnStop.GetValueOrDefault();
            }
            if (quoteDetails.UpdatedBy.HasValue)
            {
                var loginInfo = await _sender.Send(new GetNameByLoginIdQuery(quoteDetails.UpdatedBy.Value));
                if (loginInfo != null)
                {
                    quoteDetails.UpdatedByName = loginInfo.Data;
                }
            }

            response.Data = quoteDetails;
            response.Success = true;
            return response;
        }
    }
}
