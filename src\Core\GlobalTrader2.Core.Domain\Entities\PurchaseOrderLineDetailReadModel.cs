using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace GlobalTrader2.Core.Domain.Entities
{
    public class PurchaseOrderLineDetailReadModel
    {
        public int PurchaseOrderLineId { get; set; }
        public int PurchaseOrderNo { get; set; }
        public string FullPart { get; set; } = string.Empty;
        public string Part { get; set; } = string.Empty;
        public int? ManufacturerNo { get; set; }
        public string? DateCode { get; set; }
        public int? PackageNo { get; set; }
        public int Quantity { get; set; }
        public double Price { get; set; }
        public DateTime DeliveryDate { get; set; }
        public string? ReceivingNotes { get; set; }
        public bool Taxable { get; set; }
        public int? ProductNo { get; set; }
        public bool Posted { get; set; }
        public double? ShipInCost { get; set; }
        public string? SupplierPart { get; set; }
        public bool Inactive { get; set; }
        public bool Closed { get; set; }
        public byte? ROHS { get; set; }
        public string LineNotes { get; set; } = string.Empty;
        public int? UpdatedBy { get; set; }
        public DateTime DLUP { get; set; }
        public int QuantityReceived { get; set; }
        public int QuantityAllocated { get; set; }
        public double GIShipInCost { get; set; }
        public string? ProductName { get; set; }
        public string? ProductDescription { get; set; }
        public string? ProductDutyCode { get; set; }
        public string? PackageName { get; set; }
        public string? PackageDescription { get; set; }
        public double? ImportCountryShippingCost { get; set; }
        public int PurchaseOrderNumber { get; set; }
        public string CurrencyCode { get; set; } = string.Empty;
        public int CurrencyNo { get; set; }
        public string CurrencyDescription { get; set; } = string.Empty;
        public string? ManufacturerName { get; set; }
        public string? ManufacturerCode { get; set; }
        public int CompanyNo { get; set; }
        public string CompanyName { get; set; } = string.Empty;
        public DateTime DateOrdered { get; set; }
        public double? TaxRate { get; set; }
        public int ClientNo { get; set; }
        public int? ImportCountryNo { get; set; }
        public DateTime? PromiseDate { get; set; }
        public int? POSerialNo { get; set; }
        public int PurchaseQuoteId { get; set; }
        public int PurchaseQuoteNumber { get; set; }
        public DateTime PurchaseRequestDate { get; set; }
        public int? BomNo { get; set; }
        public double? ClientPrice { get; set; }
        public int InternalPurchaseOrderId { get; set; }
        public int? ClientCurrencyNo { get; set; }
        public double? ClientEstShipCost { get; set; }
        public int? IPOClientNo { get; set; }
        public string? BuyerName { get; set; }
        public string? ContactName { get; set; }
        public int? TypeNo { get; set; }
        public int? InternalPurchaseOrderLineId { get; set; }
        public int? SalesOrderLineNo { get; set; }
        public int? InternalPurchaseOrderNumber { get; set; }
        public int? HubCurrencyNo { get; set; }
        public DateTime? CurrencyDate { get; set; }
        public bool ProductInactive { get; set; }
        public bool? ReqSerialNo { get; set; }
        public string? MSLLevel { get; set; }
        public int SupplierWarranty { get; set; }
        public bool IsProdHazardous { get; set; }
        public bool? PrintHazardous { get; set; }
        public int? ReleaseBy { get; set; }
        public DateTime? OriginalDeliveryDate { get; set; }
        public string? CountryOfOrigin { get; set; }
        public string? LifeCycleStage { get; set; }
        public string? HTSCode { get; set; }
        public double? AveragePrice { get; set; }
        public string? Packing { get; set; }
        public string? PackagingSize { get; set; }
        public string? IHSCountryOfOrigin { get; set; }
        public string? Descriptions { get; set; }
        public string? IHSProduct { get; set; }
        public string? ECCNCode { get; set; }
        public bool RepeatOrder { get; set; }
        public bool IsOrderViaIPOonly { get; set; }
        public bool IsRestrictedProduct { get; set; }
        public bool? ECCNClientNotify { get; set; }
        public string? ECCNSubject { get; set; }
        public string? ECCNMessage { get; set; }
        public string? WarningMessage { get; set; }
        public bool AS6081 { get; set; }
        public int? ServiceNo { get; set; }
        public double? ServiceCost { get; set; }
        public DateTime? ServiceDateRequired { get; set; } 
        public bool? IsAuthorised { get; set; }
        public bool? IsReleased { get; set; }
        public int? SupplierApprovalStatus { get; set; }
        public string SupplierApprovalStatusMessage { get; set; } = string.Empty;
    }
}