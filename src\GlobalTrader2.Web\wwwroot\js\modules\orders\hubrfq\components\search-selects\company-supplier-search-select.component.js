﻿import { CommonRequirementSearchSelectComponent } from '../../../requirements/components/search-selects/common-requirement-search-select.component.js?v=#{BuildVersion}#';

export class CompanySupplierSearchSelectComponent extends CommonRequirementSearchSelectComponent {
    constructor(searchInputId, inputHiddenValueId, mode, apiKeySearch, apiURL, charsToSearch = 2, filterSearchIndex = null) {
        super(searchInputId, inputHiddenValueId, mode, apiKeySearch, apiURL, charsToSearch, filterSearchIndex);
    }

    // add getCompanySalesInfo when select item
    async selectItem(item) {
        if (this.lastestItemSelected !== item.value && !item.skip) {
            await this.getPurchaseData(item);
        } 
        super.selectItem(item);
        this.lastestItemSelected = item.value;
    }

    // add item.value cause CommonRequirementSearchSelectComponent overide function removeItem
    async createSelectedItem(item) {
        const selectedItem = document.createElement("span");
        selectedItem.className = "selected-item";
        selectedItem.innerHTML = `<span class="selected-item-content">${item.label}</span> <button type="button" class="btn-close" aria-label="Close"></button>`;

        // Add event listener for remove button
        selectedItem.querySelector(".btn-close").addEventListener("click", () => {
            this.removeItem(selectedItem, item.value);
        });

        return selectedItem;
    }

    async getPurchaseData(item) {
        try {
            const response = await GlobalTrader.ApiClient.getAsync(`/orders/purchase-quote/${item.value}/get-default-purchasing-info`);
            if (response.success) {
                this.dataModel = response.data[0]; 
                this.trigger("companySupplierSelected", response.data[0]);
            } 
        } catch (error) {
            console.error("Error fetching part details:", error);
        }
    }
}