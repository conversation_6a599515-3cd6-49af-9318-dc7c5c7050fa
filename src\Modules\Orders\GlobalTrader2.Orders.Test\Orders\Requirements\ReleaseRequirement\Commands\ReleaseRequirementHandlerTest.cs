using GlobalTrader2.Orders.UserCases.Orders.Requirements.ReleaseRequirement.Commands;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Core.StoreName;

namespace GlobalTrader2.Orders.Test.Orders.Requirements.ReleaseRequirement.Commands;

public class ReleaseRequirementHandlerTest
{
    private readonly Mock<IBaseRepository<object>> _mockRepository;
    private readonly ReleaseRequirementHandler _handler;
    private readonly IFixture _fixture;

    public ReleaseRequirementHandlerTest()
    {
        _fixture = new Fixture();
        _mockRepository = new Mock<IBaseRepository<object>>();
        _handler = new ReleaseRequirementHandler(_mockRepository.Object);
    }

    [Fact]
    public async Task Handle_Should_Return_Success_When_Rows_Affected()
    {
        // Arrange
        var command = _fixture.Create<ReleaseRequirementCommand>();

        _mockRepository.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                      .Callback<string, object[]>((sql, parameters) =>
                      {
                          var sqlParams = parameters.Cast<SqlParameter>().ToArray();
                          var outputParam = sqlParams.FirstOrDefault(p => p.Direction == ParameterDirection.Output);
                          if (outputParam != null)
                          {
                              outputParam.Value = 1;
                          }
                      })
                      .ReturnsAsync(1);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Success.Should().BeTrue();
        result.Data.Should().BeTrue();
    }

    [Fact]
    public async Task Handle_Should_Return_Failure_When_No_Rows_Affected()
    {
        // Arrange
        var command = _fixture.Create<ReleaseRequirementCommand>();

        _mockRepository.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                      .Callback<string, object[]>((sql, parameters) =>
                      {
                          var sqlParams = parameters.Cast<SqlParameter>().ToArray();
                          var outputParam = sqlParams.FirstOrDefault(p => p.Direction == ParameterDirection.Output);
                          if (outputParam != null)
                          {
                              outputParam.Value = 0;
                          }
                      })
                      .ReturnsAsync(0);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Success.Should().BeFalse();
        result.Data.Should().BeFalse();
    }


    [Fact]
    public async Task Handle_Should_Use_Correct_Parameters()
    {
        // Arrange
        var command = new ReleaseRequirementCommand
        {
            CustomerRequirementId = 123,
            BomId = 456,
            LoginId = 789
        };

        _mockRepository.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                      .Callback<string, object[]>((sql, parameters) =>
                      {
                          var sqlParams = parameters.Cast<SqlParameter>().ToArray();
                          var outputParam = sqlParams.FirstOrDefault(p => p.Direction == ParameterDirection.Output);
                          if (outputParam != null)
                          {
                              outputParam.Value = 1;
                          }
                      })
                      .ReturnsAsync(1);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockRepository.Verify(x => x.ExecuteSqlRawAsync(
            It.IsAny<string>(),
            It.Is<object[]>(p =>
                p.OfType<SqlParameter>().Any(param => param.ParameterName == "@CustomerRequirementId" && (int)param.Value == 123) &&
                p.OfType<SqlParameter>().Any(param => param.ParameterName == "@BomId" && (int)param.Value == 456) &&
                p.OfType<SqlParameter>().Any(param => param.ParameterName == "@UpdatedBy" && (int)param.Value == 789)
            )), Times.Once);
    }

    [Theory]
    [InlineData(1, 1, 1)]
    [InlineData(0, 1, 1)]
    [InlineData(-1, 1, 1)]
    public async Task Handle_Should_Work_With_Different_Values(int customerRequirementId, int bomId, int loginId)
    {
        // Arrange
        var command = new ReleaseRequirementCommand
        {
            CustomerRequirementId = customerRequirementId,
            BomId = bomId,
            LoginId = loginId
        };

        _mockRepository.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                      .Callback<string, object[]>((sql, parameters) =>
                      {
                          var sqlParams = parameters.Cast<SqlParameter>().ToArray();
                          var outputParam = sqlParams.FirstOrDefault(p => p.Direction == ParameterDirection.Output);
                          if (outputParam != null)
                          {
                              outputParam.Value = 1;
                          }
                      })
                      .ReturnsAsync(1);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Success.Should().BeTrue();
    }
}
