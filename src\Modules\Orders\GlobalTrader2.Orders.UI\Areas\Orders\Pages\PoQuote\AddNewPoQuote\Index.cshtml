﻿@page
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.OrderSelectionMenu
@model GlobalTrader2.Orders.UI.Areas.Orders.Pages.PoQuote.AddNewPoQuote.IndexModel
@inject IViewLocalizer _localizer

@{
    ViewData["Title"] = _localizer["AddNewPriceRequest"];
}

@section LeftSidebar {
    @await Component.InvokeAsync(nameof(OrderSelectionMenu))
}

<h1>@_localizer["AddNewPriceRequest"]</h1>
