@inject SessionManager _sessionManager
@inject IViewLocalizer _localizer

<div id="allocations-container" class="fieldset-content-wrapper">
    <div class="d-flex w-100 border bg-light">
        <button id="deallocate-btn" type="button" class="btn btn-outline-danger" disabled>
            <i class="fa-solid fa-link-slash"></i>
            <span class="lh-base">@_localizer["Deallocate"]</span>
        </button>
    </div>

    <div id="allocations-table-wrapper" class="mt-2">
        <table id="allocations-table" class="table simple-table display responsive">
            <thead>
                <tr>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td></td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<script>
    const isPOHub = @(_sessionManager.IsPOHub ? "true" : "false");
</script>