namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseOrderQuote.Command
{
    public class UpdatePurchaseRequestLineDetailCommand : IRequest<BaseResponse<int>>
    {
        public int PurchaseRequestLineDetailId { get; set; }
        public int? PurchaseRequestLineNo { get; set; }
        public int CompanyNo { get; set; }
        public double? Price { get; set; }
        public string? SPQ { get; set; }
        public string? LeadTime { get; set; }
        public string? ROHSStatus { get; set; }
        public string? FactorySealed { get; set; }
        public string? ManufacturerName { get; set; }
        public string? DateCode { get; set; }
        public string? PackageType { get; set; }
        public string? ProductType { get; set; }
        public string? MOQ { get; set; }
        public string? TotalQSA { get; set; }
        public string? LTB { get; set; }
        public string? Notes { get; set; }
        public int? UpdatedBy { get; set; }
        public int CurrencyNo { get; set; }
        public int? MSLLevelNo { get; set; }
    }
}
