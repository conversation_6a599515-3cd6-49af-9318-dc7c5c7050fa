using System.Globalization;
using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Dto.PurchaseOrderLine;
using GlobalTrader2.Orders.UseCases.Orders.POLine.Queries.GetPurchaseOrderLineDetails;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CompanyAdvisoryNote.CompanyAdvisoryNote.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.CurrentAtDate.Queries;

namespace GlobalTrader2.Orders.UseCases.Orders.POLine.Queries.GetPurchaseOrderLineAllocations
{
    public class GetPurchaseOrderLineAllocationsHandler : IRequestHandler<GetPurchaseOrderLineAllocationsQuery, BaseResponse<IEnumerable<PurchaseOrderLineAllocationDto>>>
    {
        private readonly IBaseRepository<PurchaseOrderLineAllocationReadModel> _repository;
        private readonly IMediator _mediator;
        private readonly IMapper _mapper;

        public GetPurchaseOrderLineAllocationsHandler(IBaseRepository<PurchaseOrderLineAllocationReadModel> repository, IMapper mapper, IMediator mediator)
        {
            _repository = repository;
            _mapper = mapper;
            _mediator = mediator;
        }

        public async Task<BaseResponse<IEnumerable<PurchaseOrderLineAllocationDto>>> Handle(GetPurchaseOrderLineAllocationsQuery request, CancellationToken cancellationToken)
        {
            var parameters = new List<SqlParameter>
            {
                new("@PurchaseOrderLineId", request.PurchaseOrderLineId),
            };

            var allocations = await _repository.SqlQueryRawAsync($"{StoredProcedures.SelectAll_Allocation_for_PurchaseOrderLine} @PurchaseOrderLineId", [.. parameters]);
            var response = new BaseResponse<IEnumerable<PurchaseOrderLineAllocationDto>>();

            if (allocations is null || !allocations.Any())
            {
                response.Data = Enumerable.Empty<PurchaseOrderLineAllocationDto>();
                response.Success = true;
                return response;
            }

            var dtos = _mapper.Map<IEnumerable<PurchaseOrderLineAllocationDto>>(allocations).ToList();

            var poLine = request.IsPOHub ? await TryGetPOLine(request.PurchaseOrderLineId, cancellationToken) : null;

            foreach (var allocation in dtos)
            {
                // Format basic fields
                allocation.FormattedQuantity = Functions.FormatNumeric(allocation.Quantity, CultureInfo.CurrentCulture);
                allocation.LineNo = Functions.FormatNumeric(allocation.SOSerialNo, CultureInfo.CurrentCulture);
                allocation.FormattedDatePromised = Functions.FormatDate(allocation.DatePromised);
                allocation.FormattedReturnDate = Functions.FormatDate(allocation.ReturnDate);
                allocation.FormattedPrice = Functions.FormatCurrency(allocation.Price, CultureInfo.CurrentCulture, allocation.CurrencyCode, 5, false);
                allocation.FormattedDeliveryDate = Functions.FormatDate(allocation.DeliveryDate);
                allocation.IsPoHub = request.IsPOHub;

                // Set customer advisory notes
                allocation.CustomerAdvisoryNotes = await GetAdvisoryNotes(allocation.CustomerNo, cancellationToken);

                // Calculate margin and margin value
                decimal soValue, poValue, soMargin, soMarginPercentage;
                if (request.IsPOHub)
                {
                    // Step 1: Convert SO Currency to SO Base Client Currency
                    soValue = await ConvertValueToBaseCurrencyAsync(Convert.ToDecimal(allocation.CustomerTargetPrice), Convert.ToInt32(allocation.CustomerTargetCurrency), DateTime.Now);
                    // Step 2: Convert from Client Currency to DMCC Currency: Convert Base SO Client Currency to PO Currency
                    soValue = await ConvertValueFromBaseCurrencyAsync(soValue, Convert.ToInt32(allocation.SoCurrencyMarginNo), DateTime.Now);
                    // Step 3: Convert PO Currency to Base DMCC Client Currency
                    soValue = await ConvertValueToBaseCurrencyAsync(soValue, Convert.ToInt32(allocation.SourcingCurrency), DateTime.Now);

                    poValue = await ConvertValueToBaseCurrencyAsync(Convert.ToDecimal(allocation.SourcingPrice), Convert.ToInt32(allocation.SourcingCurrency), DateTime.Now);
                }
                else
                {
                    soValue = await ConvertValueToBaseCurrencyAsync(Convert.ToDecimal(allocation.CustomerTargetPrice), Convert.ToInt32(allocation.CustomerTargetCurrency), DateTime.Now);
                    poValue = await ConvertValueToBaseCurrencyAsync(Convert.ToDecimal(allocation.SourcingPrice), Convert.ToInt32(allocation.SourcingCurrency), DateTime.Now);
                }

                if (soValue > 0)
                {
                    soMargin = soValue - poValue;
                    soMarginPercentage = soMargin * 100 / soValue;
                    allocation.Margin = Functions.FormatPercentage(soMarginPercentage.ToString(), 2, true, CultureInfo.CurrentCulture);
                    allocation.MarginValue = Functions.FormatCurrency(soMargin, CultureInfo.CurrentCulture, request.ClientCurrencyCode, 5, false);
                }
                else
                {
                    allocation.Margin = Functions.FormatPercentage("0.00", 2, true, CultureInfo.CurrentCulture);
                    allocation.MarginValue = Functions.FormatCurrency(0d, request.ClientCurrencyCode);
                }

                // Set specific data for PO-Hub users
                if (request.IsPOHub && poLine != null)
                {
                    allocation.PriceToClient = Functions.FormatCurrency(poLine.IpoLineTotal, CultureInfo.CurrentCulture, poLine.ClientCurrencyCode, 2, false);

                    var formattedProfit = Functions.FormatCurrency(poLine.LineProfit, CultureInfo.CurrentCulture, poLine.CurrencyCode, 2, false);
                    var formattedProfitPercentage = Functions.FormatPercentage(poLine.LineProfitPercentage, 2, true, CultureInfo.CurrentCulture);
                    allocation.Profit = $"{formattedProfit} ({formattedProfitPercentage})";

                    allocation.ExchangeRate = await GetRate(poLine.CurrencyNo, poLine.DateOrdered, cancellationToken);
                    allocation.SellExchangeRate = await GetRate(poLine.HubCurrencyNo, poLine.DateOrdered, cancellationToken);
                }
            }

            response.Success = true;
            response.Data = dtos.OrderBy(d => d.Id);
            return response;
        }

        public async Task<PurchaseOrderLineDetailsDto?> TryGetPOLine(int id, CancellationToken cancellationToken)
        {
            var response = await _mediator.Send(new GetPurchaseOrderLineDetailsQuery(id), cancellationToken);
            return response.Success ? response.Data : null;
        }

        private async Task<string> GetRate(int currencyNo, DateTime datePoint, CancellationToken cancellationToken)
        {
            var response = await _mediator.Send(new GetCurrentAtDateQuery { CurrencyNo = currencyNo, DatePoint = datePoint }, cancellationToken);
            return response.Data?.ExchangeRate.ToString() ?? string.Empty;
        }

        private async Task<string> GetAdvisoryNotes(int customerNo, CancellationToken cancellationToken)
        {
            var response = await _mediator.Send(new GetCompanyAdvisoryNoteQuery { Id = customerNo }, cancellationToken);
            return (response.Success && !string.IsNullOrEmpty(response.Data))
                ? Functions.ReplaceLineBreaks(response.Data)
                : string.Empty;
        }

        private async Task<decimal> ConvertCurrencyAsync(
            decimal? amount,
            int currencyNo,
            DateTime? exchangeRateDate,
            Func<decimal, decimal, decimal> applyRate
        )
        {
            if (!amount.HasValue || Math.Abs(amount.Value) < 0.0001m)
                return 0;

            DateTime datePoint = exchangeRateDate ?? DateTime.MinValue;
            var rateResponse = await _mediator.Send(new GetCurrentAtDateQuery() { CurrencyNo = currencyNo, DatePoint = datePoint }, CancellationToken.None);

            if (rateResponse.Data?.ExchangeRate == null)
                return amount.Value;

            var rate = Convert.ToDecimal(rateResponse.Data.ExchangeRate);
            return applyRate(amount.Value, rate);
        }

        private async Task<decimal> ConvertValueToBaseCurrencyAsync(decimal? valueToConvert, int currencyNo, DateTime? exchangeRateDate)
        {
            return await ConvertCurrencyAsync(valueToConvert, currencyNo, exchangeRateDate,
                (value, rate) => value / rate);
        }

        private async Task<decimal> ConvertValueFromBaseCurrencyAsync(decimal? valueToConvert, int currencyNo, DateTime? exchangeRateDate)
        {
            return await ConvertCurrencyAsync(valueToConvert, currencyNo, exchangeRateDate,
                (value, rate) => value * rate);
        }
    }
}