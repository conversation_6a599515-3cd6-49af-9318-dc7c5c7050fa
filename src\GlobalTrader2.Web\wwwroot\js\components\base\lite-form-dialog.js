﻿import BindingService from "../../helper/binding.service.js?v=#{BuildVersion}#";

export class LiteFormDialog {
    constructor(selector, options = {
        closeWhenSuccess: true,
        bindScope: true
    }) {
        this.settings = options;
        let dialog = $(selector);
        this.$form = $(`${selector} form`);
        this.summaryDiv = this.$form.find('div.form-error-summary').first();
        this.$dialog = dialog.dialog({
            autoOpen: false,
            width: options.width || '50vw',
            modal: true,
            draggable: false,
            buttons: options.buttons ? this.createSaveButton(options.buttons) : [],
            maxHeight: options.maxHeight || null,
            close: () => this.clearForm(),
            open: (event, ui) => {
                $('.ui-dialog-titlebar-close').css('display', 'none');
                $('.ui-menu').addClass('ui-menu-custom');
                this.$form.removeClass('d-none');
            },
            ...options.dialogOptions,
        });

        this.apiOptions = {
            url: options.url || "",
            method: options.method || "POST",
            body: options.body || null
        };

        this.validator = null;
        this.processing = false; // Track form processing state

        if (options.validationRules) {
            this.initValidation(options.validationRules, options.validationMessages, options.errorPlacement);
        }

        this.registerEventsForm()
    }

    // [{name:'save', alt: '', display: ''}, {name:'cancel', alt: '', display: ''}]
    createSaveButton(buttonNames) {
        let buttons = [];
        let saveButton = buttonNames.find(btn => btn.name == "save");
        let cancelButton = buttonNames.find(btn => btn.name == "cancel");
        if (saveButton) {
            buttons.push({
                html: `<img src="/img/icons/${saveButton.icon}.svg" data-action-btn="${saveButton.name}" alt="${saveButton.alt}"> ${saveButton.display}`,
                class: "btn btn-save btn-primary fw-normal",
                click: function () {
                    if (typeof saveButton.onClick === 'function') {
                        saveButton.onClick();
                    }
                }
            })
        }
        if (cancelButton) {
            buttons.push({
                html: `<img src="/img/icons/${cancelButton.icon}.svg" data-action-btn="${cancelButton.name}" alt="${cancelButton.alt}"> ${cancelButton.display}`,
                class: "btn btn-cancel btn-danger fw-normal",
                click: function () {
                    if (typeof cancelButton.onClick === 'function') {
                        cancelButton.onClick();
                    }
                }
            })
        }
        return buttons;
    }

    registerEventsForm() {
        this.$form.on('submit', function (e) {
            e.preventDefault();
        });

        //button save should look like <button class="btn-save"></button>
        this.$dialog.next().find('button.btn-save').on('click', (e) => {
            this.preventMultipleClick(this.submitForm.bind(this))();
        })

        this.$dialog.next().find('button.btn-cancel').on('click', (e) => {
            this.close();
        })
    }

    open(data = {}) {
        this.populateForm(data);
        this.$dialog.dialog("open");
    }

    close() {
        this.summaryDiv.hide();
        this.$dialog.dialog("close");
    }

    initValidation(rules, messages, errorPlacement) {
        this.validator = this.$form.validate({
            ignore: [],
            rules: rules,
            messages: messages || {},
            submitHandler: (form) => {
                if (this.onSubmit) {
                    this._setProcessing(true);
                    this.onSubmit($(form).serializeArray(), () => this.setProcessing(false));
                }
            },
            errorPlacement: errorPlacement || function (error, element) {
                error.insertAfter(element);
            },
        });
    }

    on(eventType, handler) {
        if (eventType.endsWith('.mf')) {
            this.$form.on(eventType, handler);
        } else {
            this.$dialog.on(eventType, handler);
        }
    }

    resetForm() {
        this.$form[0].reset();
        if (this.validator) this.validator.resetForm();
    }

    clearForm() {
        BindingService.clearValue(this.$form);
        if (this.validator) this.validator.resetForm();
    }

    submitForm() {
        let validate = true;
        const currentDialog = this.$dialog.dialog("instance");
        if (this.validator) {
            validate = this.$form.valid()

            if (validate && typeof this.settings.customValidate === 'function') {
                const errors = this.settings.customValidate();

                if (errors && Object.keys(errors).length > 0) {
                    this.validator.showErrors(errors);
                    validate = false;
                }
            }
        }
        if (validate) {
            currentDialog.setLoading(true);

            return this.sendRequest().then((response) => {
                if (response.success) {
                    this.onSuccessResponse(response);
                } else {
                    this.onErrorResponse(response);
                }
            })
                .finally(() => {
                    currentDialog.setLoading(false);
                });
        } else {
            this.summaryDiv.show();
        }
    }

    updateApiOptions({ url, method, body }) {
        if (url) this.apiOptions.url = url;
        if (method) this.apiOptions.method = method;
        if (body) this.apiOptions.body = body;
    }

    onErrorResponse(response) {
        this.$form.trigger('errorResponse.mf', {
            response
        });
    }

    onSuccessResponse(response) {

        this.$form.trigger('successResponse.mf', {
            response
        });
        if (this.settings.closeWhenSuccess) {
            this.close();

            this.$form.trigger('closedWithSuccessedResponse.mf', {
                response
            });

        }

        showToast('success', window.localizedStrings.saveChangedMessage);
    }

    populateForm(data) {
        if (this.settings.bindScope) {
            BindingService.bindToElements(this.$form, data);
        } else {
            Object.keys(data).forEach((key) => {
                this.$form.find(`[name="${key}"]`).val(data[key]);
            });
        }
    }

    _setProcessing(state) {
        this.processing = state;
    }

    // this placeholder for apiservice
    // Send AJAX request
    sendRequest() {
        const headers = {};

        if (!this.apiOptions.url) {
            console.error("API URL is not set.");
            return Promise.reject("API URL is not set.");
        }
        let url = this.apiOptions.url;
        if (typeof (this.apiOptions.url) == "function") {
            url = this.apiOptions.url();
        }

        let body = this.apiOptions.body;
        if (typeof (this.apiOptions.body) == "function") {
            body = this.apiOptions.body();
        }

        let method = this.apiOptions.method;
        if (typeof (this.apiOptions.method) == "function") {
            method = this.apiOptions.method();
        }
        if (['POST', 'PUT', 'DELETE'].includes(method)) {
            headers['RequestVerificationToken'] = this.$form.find('input[name="__RequestVerificationToken"]').val();
        }
        return new Promise((resolve, reject) => {
            $.ajax({
                url: url,
                method: method,
                data: JSON.stringify(body || this.getFormData()),
                contentType: "application/json",
                headers: headers,
                success: (response) => {
                    resolve(response);
                },
                error: (xhr, status, error) => {
                    let responseJson = JSON.parse(xhr.responseText);
                    console.error("Request failed:", error);
                    this.$form.trigger('errorRequest.mf', responseJson);
                    reject(error);
                }
            });
        });
    }

    getFormData() {
        // for example
        let data = {};
        this.$form.serializeArray().forEach(({ name, value }) => {
            data[name] = value;
        });
        return data;
    }

    preventMultipleClick(action) {
        return async (...args) => {
            if (this.processing) {
                return;
            }

            this._setProcessing(true);

            try {
                const result = action(...args); // Call the provided function

                // Check if the result is a promise, if so await it
                if (result && typeof result.then === 'function') {
                    await result;
                }
            } catch (error) {
                console.error("Error in action:", error);
            }

            setTimeout(() => this._setProcessing(false), 200);
        };
    }
}
