﻿import { SectionBox } from '../../../../../../components/base/section-box.component.js?v=#{BuildVersion}#'
import { ResizeLiteDatatable } from '../../../../../../components/base/resize-lite-datatable/resize-lite-datatable.component.js?v=#{BuildVersion}#'
import { EventEmitter } from '../../../../../../components/base/event-emmiter.js?v=#{BuildVersion}#'
import { ROHSHelper } from "../../../../../../helper/rohs-helper.js?v=#{BuildVersion}#"
import { ButtonHelper } from '../../../../../../helper/button-helper.js?v=#{BuildVersion}#'
import { ResizeDatatableEvents } from '../../../../../../components/base/resize-lite-datatable/resize-lite-datatable-events.constanst.js?v=#{BuildVersion}#'
import { EprDropdownMenu } from '../epr-dropdown-menu.js?v=#{BuildVersion}#';
import { ROHS_STATUSES } from "../../../../../../config/rohs-config.js?v=#{BuildVersion}#";
import { POLineAllocationsManager } from './po-line-allocations.js?v=#{BuildVersion}#'

export const LinesTabLevel = {
    all: 0,
    open: 1,
    closed: 2,
}
export class POLinesManager extends EventEmitter {
    constructor({ purchaseOrderId }) {
        super();
        this.poLinesTable = null;
        this.datatableInstance = null;
        this.purchaseOrderId = purchaseOrderId;
        this.purchaseOrderLineDetail = null;
        this.$poDetailWrapper = $('#po-line-details-wrapper');
        this.$polLineSectionBox = $('#lines-box');
        this._sectionBox = new SectionBox('#lines-box', {
            loadingContentId: 'lines-wrapper',
            loading: false,
        });
        this.getLinesUrl = {
            all: `/api/orders/purchase-orders/details/${this.purchaseOrderId}/all-po-lines`,
            open: `/api/orders/purchase-orders/details/${this.purchaseOrderId}/open-po-lines`,
            closed: `/api/orders/purchase-orders/details/${this.purchaseOrderId}/closed-po-lines`,
        };
        this.lineTabLevel = LinesTabLevel.all;
        this._$previousButton = $(`#po-line-details-previous-button`);
        this._$nextButton = $(`#po-line-details-next-button`);
        this._$loading = $(`#po-line-details-loading-indicator`);
        this._$collapsedArea = $(`#po-line-details`);

        this.$eprDropdownMenu = new EprDropdownMenu(
            this.purchaseOrderId,
            "po-lines-epr-menu",
            "manage-epr-list-button",
            "po-lines-epr-menu-dropdown-container",
            "add-new-epr"
        );

        this.checkedPoLineIds = "";
        this.checkedPoLineNos = "";
        this.checkedRowsMap = new Map();

        this.allocationsManager = null;
    }

    async initialize() {
        this._setupSectionBox();
        this._setupDialogEvents();
        this._setupTable();
        this.$eprDropdownMenu.initialize();
    }

    _setupDialogEvents() {
        $('#lines-tabs button[data-bs-toggle="tab"]').on('shown.bs.tab', (e) => {
            this.lineTabLevel = $(e.target).data('view-level');
            let endpoint = "";
            switch (this.lineTabLevel) {
                case LinesTabLevel.all:
                    endpoint = this.getLinesUrl.all;
                    break;
                case LinesTabLevel.open:
                    endpoint = this.getLinesUrl.open;
                    break;
                case LinesTabLevel.closed:
                    endpoint = this.getLinesUrl.closed;
                    break;
            }

            if (this.datatableInstance != null) {
                this.datatableInstance.ajax.url(endpoint);
                this.refreshSectionBox();
            }
        });
    }

    _setupSectionBox() {
        this._sectionBox.init();
        this._sectionBox.on('onRefreshed.msb', async () => {
            this.refreshSectionBox();
        });
    }

    async refreshSectionBox() {
        let selectedRowIndex = this.datatableInstance.row({ selected: true }).index();
        this.poLinesTable.reload(selectedRowIndex);
    }

    _setupTable() {
        const baseColumnConfig = {
            data: null,
            type: 'string',
            className: 'text-wrap text-break',
        };

        const linesTableConfig = {
            ajax: {
                url: `/api/orders/purchase-orders/details/${this.purchaseOrderId}/all-po-lines`,
                dataSrc: (json) => {
                    if (json.success) {
                        $("#lines-subtotal-value").text(json.data.subTotal);
                        $("#lines-tax-value").text(json.data.tax);
                        $("#lines-total-value").text(json.data.total);
                    }
                    return json.data.lines;
                }
            },
            rowId: "lineID",
            columns: [
                {
                    ...baseColumnConfig,
                    width: '5%',
                    render: (data, type, row, meta) => {
                        const objExtraData = {
                            isPosted: row.posted,
                            isAllocated: row.isAllocated,
                            isReceived: row.quantityReceived > 0 && row.quantityOutstanding == 0,
                            isPartReceived: row.quantityReceived > 0 && row.quantityOutstanding > 0,
                            quantityReceived: row.quantityReceived,
                            isClosed: row.closed,
                            isIPO: row.isIPO
                        }
                        let img = "";
                        if (objExtraData.isPosted) img = "posted.gif";
                        if (objExtraData.isAllocated) img = "allocated.gif";
                        if (objExtraData.isPartReceived) img = "partshipped.gif";
                        if (objExtraData.IsReceived) img = "shipped.gif";
                        if (row.inactive) img = "inactive.gif";

                        let checkboxHtml = `<input id="" type="checkbox" class="row-checkbox" />`;

                        if (img.length === 0) {
                            return `
                            <div class="d-flex justify-content-between align-items-center">
                                <div></div>
                                ${checkboxHtml}
                            </div>
                            `
                        }
                        else {
                            return `
                            <div class="d-flex justify-content-between align-items-center">
                                <img src="/App_Themes/Original/images/tables/${img}" alt="${img}" />
                                ${checkboxHtml}
                            </div>
                            `
                        }
                    }
                },
                {
                    data: 'lineNo',
                    title: linesResource.lineNo,
                    type: 'string',
                },
                {
                    ...baseColumnConfig,
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${linesResource.partNo}</div>${linesResource.supplierPart}`,
                    width: '10%',
                    render: (data, type, row, meta) => {
                        return `
                        <div>${ROHSHelper.writePartNo(row.part, row.rohs)}</div>
                        <span>${GlobalTrader.StringHelper.setCleanTextValue(row.supplierPart)}</span>
                    `;
                    }
                },
                {
                    ...baseColumnConfig,
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${linesResource.mfr}</div>${linesResource.dc}`,
                    width: '7%',
                    render: (data, type, row, meta) => {
                        return `
                                    <div>${ButtonHelper.nubButton_Manufacturer(row.mfrNo, row.mfr, row.mfrAdvisoryNotes)}</div>
                                    <span>${GlobalTrader.StringHelper.setCleanTextValue(row.dateCode)}</span>
                                `;
                    }
                },
                {
                    ...baseColumnConfig,
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${linesResource.product}</div>${linesResource.package}`,
                    width: '15%',
                    render: (data, type, row, meta) => {
                        return `
                                    <div>${GlobalTrader.StringHelper.setCleanTextValue(row.product)}</div>
                                    <span>${GlobalTrader.StringHelper.setCleanTextValue(row.package)}</span>
                                `;
                    }
                },
                {
                    ...baseColumnConfig,
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${linesResource.qtyOrdered}</div>${linesResource.qtyAllocated}`,
                    render: (data, type, row, meta) => {
                        return `
                                <div>${row.quantityOrdered}</div>
                                <div>${row.quantityAllocated}</div>
                            `;
                    }
                },
                {
                    ...baseColumnConfig,
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${linesResource.qtyReceived}</div>${linesResource.qtyOutstanding}`,
                    render: (data, type, row, meta) => {
                        return `
                                <div>${row.quantityReceived}</div>
                                <div>${row.quantityOutstanding}</div>
                            `;
                    }
                },
                {
                    ...baseColumnConfig,
                    data: 'price',
                    title: `${linesResource.unitPrice}`,
                    width: '12%',
                },
                {
                    ...baseColumnConfig,
                    title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${linesResource.total}</div>${linesResource.tax}`,
                    width: '12%',
                    render: (data, type, row, meta) => {
                        return `
                                <div>${row.lineTotal}</div>
                                <div>${row.tax}</div>
                            `;
                    }
                },
                {
                    ...baseColumnConfig,
                    width: '10%',
                    title: `${linesResource.asRequired}`,
                    render: (data, type, row, meta) => {
                        return `
                                <span>${row.aS6081 ? 'Yes' : 'No'}</span>
                            `;
                    }
                },
            ],
            resizeConfig: {
                numberOfRowToShow: 4
            },
        }

        this.poLinesTable = new ResizeLiteDatatable("po-lines-tbl", "", linesTableConfig);
        this.poLinesTable.init();
        this.datatableInstance = this.poLinesTable.datatable;

        this.datatableInstance.on('init.dt', () => {
            $('#po-lines-tbl tbody').on('click', '.row-checkbox', (event) => {
                const $row = event.currentTarget.closest('tr');
                const rowIndex = $('#po-lines-tbl').DataTable().row($row).index();
                const rowData = $('#po-lines-tbl').DataTable().row($row).data();
                if (!rowData) return;
                
                const checkbox = event.currentTarget;

                if (checkbox.checked) {
                    this.checkedRowsMap.set(rowData.lineID, rowData.lineNo);
                } else {
                    this.checkedRowsMap.delete(rowData.lineID);
                }

                const checkedLineIDs = Array.from(this.checkedRowsMap.keys());
                const checkedLineNos = Array.from(this.checkedRowsMap.values());

                this.checkedPoLineNos = checkedLineNos.length > 0 ? checkedLineNos.join(",") : null;
                this.checkedPoLineIds = checkedLineIDs.length > 0 ? checkedLineIDs.join(",") : null;

                this.$eprDropdownMenu._bindDataFromSource({
                    purchaseOrderLines: this.checkedPoLineNos,
                    purchaseOrderLineIds: this.checkedPoLineIds
                });
            });
        });

        this.datatableInstance.on(ResizeDatatableEvents.SELECT, async (e, dt, type, indexes) => {
            const rowData = dt.row(indexes).data();
            this._setDetailLoading(true);
            await this.getPurchaseOrderLineDetail(rowData.lineID);
            this._initAllocationsCollapsibleSection(rowData);
        });

        this.datatableInstance.on('processing.dt', (e, settings, processing) => {
            if (processing) {
                this.$polLineSectionBox.section_box("option", "loading", true);
            }
        });

        this.datatableInstance.on(ResizeDatatableEvents.DRAW, () => {
            this.$polLineSectionBox.section_box("option", "loading", false);
        });
       
        GlobalTrader.DataTablesDetailHelper.setUpDetailPrevButton(this.datatableInstance, this._$previousButton);
        GlobalTrader.DataTablesDetailHelper.setUpDetailNextButton(this.datatableInstance, this._$nextButton);
    }

    _setDetailLoading(isLoading) {
        if (isLoading) {
            this._$loading.html(
                `<div class="d-flex">
                    <div class="spinner-loader me-1"></div>
                    <div class="text-loader"></div>
                </div>`);
        } else {
            this._$loading.html("");
        }
        this._$collapsedArea.toggleClass('d-none', isLoading);
    }

    async getPurchaseOrderLineDetail(purchaseOrderLineId) {
        let response = await GlobalTrader.ApiClient.getAsync(`/orders/purchase-orders/po-lines/${purchaseOrderLineId}`);
        if (response.success) {
            this.purchaseOrderLineDetail = response.data;
            this.bindingPurchaseOrderLineDetail();
            this._setDetailLoading(false);
        } else {
            console.log(response.errors);
        }
    }

    bindingPurchaseOrderLineDetail() {
        if (!this.purchaseOrderLineDetail) return;
        const htmlRawFieldNames = [
            "poLineNotes"
        ];

        $("#po_detail_rohs").html(this._writeRohs(this.purchaseOrderLineDetail.rohs));
        
        // Update check box value
        this.$poDetailWrapper.find("input[type=checkbox]").toArray().forEach(input => {
            const fieldName = $(input).data("field");
            $(input).prop("checked", this.getPropertyCaseInsensitive(this.purchaseOrderLineDetail, fieldName))
                .trigger('change');
        });

        // Update text value
        this.$poDetailWrapper.find("span[data-field]").toArray().forEach(element => {
            const fieldName = $(element).data("field");
            let fieldValue = this.getPropertyCaseInsensitive(this.purchaseOrderLineDetail, fieldName);

            if (htmlRawFieldNames.includes(fieldName)) {
                $(element).html(GlobalTrader.StringHelper.setCleanTextValue(fieldValue, true));
            } else {
                $(element).text(GlobalTrader.StringHelper.setCleanTextValue(fieldValue));
            }
        });

        const manufacturerButtonHtml = `${ButtonHelper.createNubButton(GlobalTrader.PageUrlHelper.Get_URL_Manufacturer(this.purchaseOrderLineDetail.manufacturerNo), this.purchaseOrderLineDetail.manufacturerName)}
                ${this.purchaseOrderLineDetail.mfrAdvisoryNotes.length > 0 ? ButtonHelper.createAdvisoryNotesIcon(Functions.ReplaceLineBreaks(this.purchaseOrderLineDetail.mfrAdvisoryNotes)) : ""}`
        $(`#po_detail_manufacturer`).html(manufacturerButtonHtml);
        $(`#po_detail_manufacturer`).text(`${this.purchaseOrderLineDetail.productDutyCode} (${this.purchaseOrderLineDetail.formatDutyRate})`);
        const supplierWarrantyDesc = this.purchaseOrderLineDetail.supplierWarranty != 0 ? `${this.purchaseOrderLineDetail.supplierWarranty} days` : "";
        $(`#po_detail_supplierWarranty`).text(supplierWarrantyDesc);

        let eccnDifferWarningMessage = "";
        if (this.purchaseOrderLineDetail.eccnCodeSOLine && this.purchaseOrderLineDetail.eccnCode && this.purchaseOrderLineDetail.eccnCodeSOLine != this.purchaseOrderLineDetail.eccnCode) {
            eccnDifferWarningMessage = localizedStrings.eccnDifferMessage;
        }
        const eccnCodeDesc = GlobalTrader.HtmlHelper.showIHSECCNCodeDefinition(this.purchaseOrderLineDetail.eccnCode, this.purchaseOrderLineDetail.ihseccnCodeDefination, eccnDifferWarningMessage);
        $(`#po_detail_eccnCode`).html(eccnCodeDesc);
    }

    getPropertyCaseInsensitive(obj, key) {
        key = key.toLowerCase();
        for (let prop in obj) {
            if (prop.toLowerCase() === key) {
                return obj[prop];
            }
        }
        return undefined; // not found
    }

    _initAllocationsCollapsibleSection(lineDetails) {
        if (lineDetails.serviceNo > 0) {
            this.allocationsManager?.toggleVisibility(false);
            return;
        }

        if (!this.allocationsManager) {
            this.allocationsManager = new POLineAllocationsManager(lineDetails.lineID, isPOHub);
            this.allocationsManager.initialize();
        } else {
            this.allocationsManager.reload(lineDetails.lineID);
        }
        this.allocationsManager.toggleVisibility();
    }

    _writeRohs(rohs) {
        const match = ROHS_STATUSES.find(entry => entry.status === rohs);
        const tooltip = match ? match.tooltip : "";
        return ROHSHelper.writePartNo(tooltip, rohs);
    }
}
