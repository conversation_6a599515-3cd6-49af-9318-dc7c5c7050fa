import { LiteFormDialog } from "../../../../components/base/lite-form-dialog.js?v=#{BuildVersion}#";

export class ReleaseRequirementHandler {
    constructor(dependencies = {}) {
        this.form = null;
        this.sourcingTable = null;
        
        this.bomData = dependencies.bomData;
        this.bomId = dependencies.bomId;
        this.localization = dependencies.localization;
        this.selectedRowData = null;
        this.selectedRequirementId = null;
        this.init();
    }

    init() {
        this.form = new LiteFormDialog("#release-requirement-dialog", {
            width: '1200px',
            closeWhenSuccess: true,
            url: "/api/orders/bom/release-requirement",
            buttons: [
                { name: "save", icon: "check", alt: "yes", display: this.localization.release},
                { name: "cancel", icon: "xmark", alt: "no", display: this.localization.cancel}
            ],
            dialogOptions: {
                open: (event, ui) => {
                    $('.ui-dialog-titlebar-close').css('display', 'none');
                    $('.ui-menu').addClass('ui-menu-custom');

                    this.initSourcingTable();
                }
            },
            body: () => {
                const selectedRowData = this.getSelectedRowData();
                if (!selectedRowData) {
                    throw new Error('No row selected');
                }
                return {
                    customerRequirementId: selectedRowData.customerRequirementId,
                    bomId: this.bomId,
                    bomCode: this.bomData.code,
                    bomName: this.bomData.name,
                    bomCompanyName: this.bomData.company,
                    bomCompanyNo: this.bomData.companyNo,
                    salesManNo: this.bomData.requestToPOHubBy || 0,
                    custReqNo: selectedRowData.custReqNo || 0,
                    reqSalesPerson: this.bomData.requestToPOHubBy ? this.bomData.requestToPOHubBy.toString() + "|" : "",
                    supportTeamMemberNo: this.bomData.contact2No ? this.bomData.contact2No.toString() + "|" : ""
                };
            }
        });

        this.form.$form.on('closedWithSuccessedResponse.mf', (e, { response }) => {
            $('#release-requirement-btn').prop('disabled', true);
            location.reload();
        });
    }

    getSelectedRowData() {
        return this.selectedRowData;
    }

    updateSelectedRowData(rowData) {
        this.selectedRowData = rowData;
        this.selectedRequirementId = rowData?.customerRequirementId || null;
    }

    async initSourcingTable() {
        try {
            if (this.sourcingTable?.table) {
                this.sourcingTable.table.ajax.reload();
                return;
            }

            // Get selected requirement ID
            const selectedRequirementId = this.getSelectedRequirementId();
            if (!selectedRequirementId) {
                console.warn('No requirement selected');
                return;
            }
            const localization = this.localization;

            this.sourcingTable = new GlobalTrader.Common.SearchTablePageBase({
                sectionBoxSelector: "#release-req-sourcing-box",
                tableSelector: "#release-req-sourcing-table",
                tableOptions: {
                    ajax: `/api/orders/bom/part-detail/${selectedRequirementId}/sourcing-results`,
                    language: {
                        emptyTable: `<i>${localization.noData}</i>`
                    },
                    searching: false,
                    select: false,
                    columnDefs: [
                        { type: 'string', targets: '_all' }
                    ],
                    resizeConfig: {
                        numberOfRowToShow: 5
                    },
                    columns: [
                        {
                            title: GlobalTrader.DataTablesHelper.createStackedHeader([
                                localization.supplier, 
                                localization.partNo
                            ]),
                            data: "supplier",
                            width: "20%",
                            render: function (data, type, row) {
                                const supplier = data || row.supplierName || '';
                                const partNo = row.part || '';
                                return `${supplier}<br><small class="text-muted">${partNo}</small>`;
                            }
                        },
                        {
                            title: localization.buyPrice,
                            data: "buyPrice",
                            width: "20%",
                            render: function (data, type, row) {
                                const buyPrice = data || row.price || '';
                                return buyPrice;
                            }
                        },
                        {
                            title: localization.unitSellPrice,
                            data: "actualPrice",
                            width: "20%",
                            render: function (data, type, row) {
                                const sellPrice = data || row.unitSellPrice || '';
                                return sellPrice;
                            }
                        },
                        {
                            title: localization.warningMessage,
                            data: null,
                            width: "65%",
                            render: function (data, type, row) {
                                const buyPrice = parseFloat(row.unitBuyPrice || 0);
                                const sellPrice = parseFloat(row.unitSellPrice || 0);
                                
                                if (buyPrice > 0 && sellPrice > 0) {
                                    if (buyPrice > sellPrice) {
                                        return `${localization.buyPriceGreaterText} (${buyPrice}) ${localization.ofSelectedPartGreaterThan} (${sellPrice})`;
                                    }
                                    if (buyPrice === sellPrice) {
                                        return `${localization.buyPriceEqualText} (${buyPrice}) ${localization.ofSelectedPartEqualTo} (${sellPrice})`;
                                    }
                                }
                                return '';
                            }
                        }
                    ],
                    rowId: "id",
                }
            });

            this.sourcingTable.init();
        } catch (error) {
            console.error("Error initializing Sourcing Table:", error);
            throw error;
        }
    }


    getSelectedRequirementId() {
        if (this.selectedRequirementId) {
            return this.selectedRequirementId;
        }
        
        if (this.selectedRowData?.customerRequirementId) {
            return this.selectedRowData.customerRequirementId;
        }
        
        return null;
    }

    open() {
        if (this.form) {
            this.form.open();
        }
    }
}
