using GlobalTrader2.Orders.UserCases.Orders.Requirements.ReleaseRequirement.Commands;
using FluentValidation.TestHelper;

namespace GlobalTrader2.Orders.Test.Orders.Requirements.ReleaseRequirement.Commands;

public class ReleaseRequirementValidatorTest
{
    private readonly ReleaseRequirementValidator _validator;

    public ReleaseRequirementValidatorTest()
    {
        _validator = new ReleaseRequirementValidator();
    }

    [Fact]
    public void Should_Have_Error_When_CustomerRequirementId_Is_Zero()
    {
        var command = new ReleaseRequirementCommand { CustomerRequirementId = 0 };
        var result = _validator.TestValidate(command);
        result.ShouldHaveValidationErrorFor(x => x.CustomerRequirementId);
    }

    [Fact]
    public void Should_Have_Error_When_BomId_Is_Zero()
    {
        var command = new ReleaseRequirementCommand { BomId = 0 };
        var result = _validator.TestValidate(command);
        result.ShouldHaveValidationErrorFor(x => x.BomId);
    }

    [Fact]
    public void Should_Have_Error_When_LoginId_Is_Zero()
    {
        var command = new ReleaseRequirementCommand { LoginId = 0 };
        var result = _validator.TestValidate(command);
        result.ShouldHaveValidationErrorFor(x => x.LoginId);
    }

    [Fact]
    public void Should_Not_Have_Error_When_All_Fields_Are_Valid()
    {
        var command = new ReleaseRequirementCommand 
        { 
            CustomerRequirementId = 1, 
            BomId = 1, 
            LoginId = 1 
        };
        var result = _validator.TestValidate(command);
        result.ShouldNotHaveAnyValidationErrors();
    }
}
