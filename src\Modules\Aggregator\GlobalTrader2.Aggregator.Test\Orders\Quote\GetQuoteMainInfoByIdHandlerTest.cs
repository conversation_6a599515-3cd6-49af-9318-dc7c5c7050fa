﻿using AutoFixture;
using AutoMapper;
using GlobalTrader2.Aggregator.UseCases.Orders.Quote.GetQuoteMainInfoById;
using GlobalTrader2.Core;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Dto.Quote;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CompanyAdvisoryNote.CompanyAdvisoryNote.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Quotes.Queries.GetQuoteById;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetCurrencyRateCurrentAtDate;
using GlobalTrader2.UserAccount.UseCases.LoginManager.Queries.GetNameByLoginId;
using MediatR;
using Moq;
using System.Linq.Expressions;

namespace GlobalTrader2.Aggregator.Test.Orders.Quote
{
    public class GetQuoteMainInfoByIdHandlerTest
    {
        private readonly Mock<IMapper> _mapperMock;
        private readonly Mock<ISender> _senderMock;
        private readonly Mock<IBaseRepository<Currency>> _currencyRepositoryMock;
        private readonly GetQuoteMainInfoByIdHandler _handler;
        private readonly Fixture _fixture;

        public GetQuoteMainInfoByIdHandlerTest()
        {
            _mapperMock = new Mock<IMapper>();
            _senderMock = new Mock<ISender>();
            _currencyRepositoryMock = new Mock<IBaseRepository<Currency>>();
            _handler = new GetQuoteMainInfoByIdHandler(_mapperMock.Object, _senderMock.Object, _currencyRepositoryMock.Object);
            _fixture = new Fixture();
        }

        [Fact]
        public async Task Handle_Success_ReturnsQuoteMainInfoDto()
        {
            // Arrange
            var request = _fixture.Build<GetQuoteMainInfoByIdQuery>()
                .With(x => x.quoteId, 1)
                .With(x => x.ClientCurrencyID, 2)
                .With(x => x.ClientCurrencyCode, "USD")
                .With(x => x.isAllowCheckedCompanyOnStop, false)
                .Create();

            var quoteDto = new QuoteReadModel
            {
                QuoteId = 1,
                CompanyNo = 10,
                CurrencyNo = 2,
                Freight = 100,
                CurrencyCode = "USD",
                UpdatedBy = 99
            };

            var mappedQuote = new QuoteMainInfoDto
            {
                QuoteId = 1,
                CompanyNo = 10,
                CurrencyNo = 2,
                Freight = 100,
                CurrencyCode = "USD",
                UpdatedBy = 99
            };
            var currency = new Currency { CurrencyId = 2, CurrencyCode = "USD" };
            _currencyRepositoryMock.Setup(r => r.GetAsync(It.IsAny<Expression<Func<Currency, bool>>>()))
                .ReturnsAsync(currency);

            _senderMock.Setup(s => s.Send(It.IsAny<GetQuoteByIdQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<QuoteReadModel>
                {
                    Success = true,
                    Data = quoteDto
                });

            _mapperMock.Setup(m => m.Map<QuoteMainInfoDto>(It.IsAny<QuoteReadModel>()))
                .Returns(mappedQuote);

            _senderMock.Setup(s => s.Send(It.IsAny<GetCompanyAdvisoryNoteQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<string> { Success = true, Data = "Advisory Note" });

            _senderMock.Setup(s => s.Send(It.IsAny<GetNameByLoginIdQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<string> { Success = true, Data = "John Doe" });

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            Assert.Equal(mappedQuote.QuoteId, result.Data.QuoteId);
            Assert.Equal("Advisory Note", result.Data.CompanyAdvisoryNotes);
            Assert.Equal("John Doe", result.Data.UpdatedByName);
        }

        [Fact]
        public async Task Handle_QuoteNotFound_ReturnsUnsuccessfulResponse()
        {
            // Arrange
            var request = _fixture.Create<GetQuoteMainInfoByIdQuery>();
            _senderMock.Setup(s => s.Send(It.IsAny<GetQuoteByIdQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<QuoteReadModel> { Success = false, Data = null });
            _currencyRepositoryMock.Setup(r => r.GetAsync(It.IsAny<Expression<Func<Currency, bool>>>()))
                .ReturnsAsync((Currency)null);
            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.False(result.Success);
            Assert.Null(result.Data);
        }

        [Fact]
        public async Task Handle_ConvertsFreight_WhenCurrencyDiffers()
        {
            // Arrange
            var request = _fixture.Build<GetQuoteMainInfoByIdQuery>()
                .With(x => x.quoteId, 1)
                .With(x => x.ClientCurrencyID, 99)
                .With(x => x.ClientCurrencyCode, "EUR")
                .With(x => x.isAllowCheckedCompanyOnStop, false)
                .Create();

            var quoteDto = new QuoteReadModel
            {
                QuoteId = 1,
                CompanyNo = 10,
                CurrencyNo = 2,
                Freight = 100,
                CurrencyCode = "USD",
                DateQuoted = DateTime.UtcNow,
                UpdatedBy = 99
            };

            var mappedQuote = new QuoteMainInfoDto
            {
                QuoteId = 1,
                CompanyNo = 10,
                CurrencyNo = 2,
                Freight = 100,
                CurrencyCode = "USD",
                DateQuoted = quoteDto.DateQuoted,
                UpdatedBy = 99
            };
            var currency = new Currency { CurrencyId = 2, CurrencyCode = "USD" };
            _currencyRepositoryMock.Setup(r => r.GetAsync(It.IsAny<Expression<Func<Currency, bool>>>()))
                .ReturnsAsync(currency);

            _senderMock.Setup(s => s.Send(It.IsAny<GetQuoteByIdQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<QuoteReadModel>
                {
                    Success = true,
                    Data = quoteDto
                });

            _mapperMock.Setup(m => m.Map<QuoteMainInfoDto>(It.IsAny<QuoteReadModel>()))
                .Returns(mappedQuote);

            _senderMock.Setup(s => s.Send(It.IsAny<GetCompanyAdvisoryNoteQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<string> { Success = true, Data = "Advisory Note" });

            _senderMock.Setup(s => s.Send(It.IsAny<GetCurrencyRateCurrentAtDateQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<double?> { Success = true, Data = 2.0 });

            _senderMock.Setup(s => s.Send(It.IsAny<GetNameByLoginIdQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<string> { Success = true, Data = "John Doe" });

            // Act
            var result = await _handler.Handle(request, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            Assert.Equal("Advisory Note", result.Data.CompanyAdvisoryNotes);
            Assert.Equal("John Doe", result.Data.UpdatedByName);
        }
    }
}
