@using GlobalTrader2.SharedUI.Interfaces
@using GlobalTrader2.Dto.SalesOrderLine

@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Localization
@inject IViewLocalizer _localizer
@inject IWebResourceManager WebResourceManager
@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer

@model LinesSectionViewModel




<div id="post-line-dialog" class="dialog-container" title="" style="display: none;">
    <div class="dialog-description">
        <span id="description"></span>
    </div>
    <div class="d-flex flex-column gap-3 mt-3">
        <div class="d-flex align-items-center">
            <div style="min-width: 120px;">
                <div class="fw-bold mb-0">@_commonLocalizer["Sales Order"]</div>
            </div>
            <div class="flex-grow-1">
                <span>@Model.SalesOrderNumber</span>
            </div>
        </div>
        <div class="d-flex align-items-center">
            <div style="min-width: 120px;">
                <div class="fw-bold mb-0">@_commonLocalizer["Customer"]</div>
            </div>
            <div class="flex-grow-1">
                <span id="post-line-customer-name"></span>
            </div>
        </div>
        <div class="d-flex flex-column gap-3" id="wrapper">
            <div class="d-flex align-items-center" id="service-wrapper">
                <div style="min-width: 120px;">
                    <div class="fw-bold mb-0" id="part-service-label"></div>
                </div>
                <div class="flex-grow-1" id="part-service">
                </div>
            </div>
            <div class="d-flex align-items-center" id="quantity-wrapper">
                <div style="min-width: 120px;">
                    <div class="fw-bold mb-0">@_commonLocalizer["Quantity"]</div>
                </div>
                <div class="flex-grow-1" id="quantity">
                </div>
            </div>
            <div class="d-flex align-items-center" id="price-wrapper">
                <div style="min-width: 120px;">
                    <div class="fw-bold mb-0">@_commonLocalizer["Price"]</div>
                </div>
                <div class="flex-grow-1" id="price">
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    const localized = {
        service: '@_commonLocalizer["Service"]',
        part: '@_commonLocalizer["Part No"]',
        message: '@_commonLocalizer["The balance of all posted line takes the company over its credit limit, this Sales Order cannot be posted"]',
        postTitle: '@_commonLocalizer["Post Sales Order Line"]',
        postAllTitle: '@_commonLocalizer["Post All Sales Order Lines"]',
        postMessage : '@_messageLocalizer["Are you sure you would like to post this "]',
        postAllMessage : '@_messageLocalizer["Are you sure you would like to post all the unposted "]',
        unpostAllMessage : '@_messageLocalizer["Are you sure you would like to unpost all the posted "]',
        unpostMessage : '@_messageLocalizer["Are you sure you would like to unpost this "]',
        unpostTitle: '@_commonLocalizer["Unpost Sales Order Line"]',
        unpostAllTitle: '@_commonLocalizer["Unpost All Sales Order Lines"]',
        deleteTitle: '@_commonLocalizer["Delete Sales Order Line"]',
        deleteMessage: '@_messageLocalizer["Are you sure you would like to delete this "]',
        line: '@_commonLocalizer["Sales Order Line"]',
        lines: '@_commonLocalizer["Sales Order Lines"]',
    };
</script> 