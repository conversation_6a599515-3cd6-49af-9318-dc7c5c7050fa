﻿using GlobalTrader2.Aggregator.UseCases.Orders.Quote.GetQuoteMainInfoById;
using GlobalTrader2.Contacts.UseCases.Contact.ContactDetails.Queries.GetCompanyAdvisoryNotes;
using GlobalTrader2.Core.Constants;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Dto.Contact;
using GlobalTrader2.Dto.Quote;
using GlobalTrader2.Orders.UI.Areas.Orders.Pages.HUBRFQ.Details;
using GlobalTrader2.Orders.UserCases.Orders.BOM.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.Quotes.Queries.GetQuoteForPage;
using GlobalTrader2.SharedUI.Areas.Containers.Pages.Shared.Components.CustomerRequirements;
using GlobalTrader2.UserAccount.UseCases.RecentlyViewed.Commands.Create;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Pages.Quotes.Details
{
    public class IndexModel : BasePageModel
    {
        private readonly SessionManager _sessionManager;
        private readonly IMediator _mediator;
        public IndexModel(SecurityManager securityManager, SessionManager sessionManager, IMediator mediator) : base(securityManager)
        {
            _sessionManager = sessionManager;
            _mediator = mediator;

            SiteSection = SiteSection.Orders;
            PageType = SitePage.Orders_QuoteDetail;
            ClientID = _sessionManager.ClientID;
            LoginID = _sessionManager.LoginID;
            ClientName = _sessionManager.ClientName;
            IsGlobalUser = _sessionManager.IsGlobalUser;
            IsGSA = _sessionManager.IsGSA;
            IsGSAViewPermission = _sessionManager.IsGSAViewPermission;
            IsDataHasOtherClient = IsGlobalUser;
        }

        [FromQuery(Name = "qt")]
        public int? QuoteId { get; set; }
        public bool ShowEditQuoteMainInfoButton { get; set; } = true;
        public bool ShowCreateSOButton { get; set; } = true;       
        public bool ShowCloseQuoteMainInfoButton { get; set; } = true;
        public bool ShowViewTreeButton { get; set; } = true;
        public bool ShowAddTaskButton { get; set; } = true;
        public bool ShowViewTaskButton { get; set; } = true;
        public bool IsGSA { get; set; } = false;
        public bool IsGSAViewPermission { get; set; } = false;
        public bool IsGSAEditPermission { get; set; } = false;
        public bool IsDifferentClient { get; set; } = false;
        public bool IsIPOHub { get; set; } = false;
        public int? ClientID { get; set; }
        public int? LoginID { get; set; }
        public string? ClientName { get; set; }
        public bool IsGlobalUser { get; set; } = false;
        public bool CanEditQuoteMainInfo { get; set; }
        public bool CanCreateSalesOrder { get; set; }
        public bool CanCloseAll { get; set; } 
        public bool CanEditAS9120 { get; set; }
        public string? AdvisoryNotes { get; set; }
        public QuoteForPage QuoteForPage { get; set; }
        public GsaInfo Gsa { get; set; } = new GsaInfo();

        public async Task<IActionResult> OnGetAsync(CancellationToken cancellationToken)
        {
            if (!QuoteId.HasValue)
            {
                return Redirect(V2Paths.NotFound);
            }

            QuoteForPage = await GetQuoteForPageAsync((int)QuoteId);
            if (QuoteForPage == null)
            {
                return Redirect(V2Paths.NotFound);
            }
            else if (QuoteForPage?.ClientNo != ClientID)
            {
                IsDifferentClient = true;
                IsGSAEditPermission = !IsGSAViewPermission;
                if (!IsGSA)
                {
                    IsGSAEditPermission = false;
                }
            }
            var isHasTabPermission = await CheckTabPermission();
            if (!isHasTabPermission)
            {
                return Redirect(V2Paths.NotFound);
            }
            CheckPermission();
            AddBreadCrumbs();
            SetUpPermissions();
            await SetupContent();
            await UpdateRecentlyView();
            return Page();
        }

        private async Task<QuoteForPage> GetQuoteForPageAsync(int quoteId)
        {
           
            var quoteForPageQuery = new GetQuoteForPageQuery(
               quoteId
           );
            var quoteForPage = await _mediator.Send(quoteForPageQuery);
            return quoteForPage.Data;

        }
        private async Task<bool> CheckTabPermission()
        {
           
            bool hasOrderQuoteViewPermission = _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_Quote_View);
            bool isNotDifferentClientOrNotGSA = (!IsDifferentClient || !IsGSA);
            if (IsGlobalUser)
            {
                if (hasOrderQuoteViewPermission)
                {
                    return true;
                } else
                {
                    IsDataHasOtherClient = false;
                    hasOrderQuoteViewPermission = await _securityManager.CheckFunctionPermissions(LoginID.Value, IsDataHasOtherClient, [SecurityFunction.Orders_Quote_View]);
                }
            }
            if (_securityManager != null && !hasOrderQuoteViewPermission && isNotDifferentClientOrNotGSA)
            {
                return false;
            }
            var listTabs = await GetVisibleTabSecurityList(SecurityFunction.Orders_Quotes_View);
            if (!listTabs.Contains((int)ViewLevelList.My) && isNotDifferentClientOrNotGSA)
            {
                return false;
            }
            if (QuoteForPage != null)
            {
                var isTabAllow = CheckTabSecurityInDetailPage(new TabSecurityInDetailPageRequest()
                {
                    ListTabs = listTabs,
                    TeamID = QuoteForPage.TeamNo,
                    LoginTeamID = _sessionManager.LoginTeamID.GetValueOrDefault(),
                    DivisionID = QuoteForPage.DivisionNo,
                    LoginDivisionID = _sessionManager.LoginDivisionID.GetValueOrDefault(),
                    ClientID = QuoteForPage.ClientNo,
                    LoginClientID = _sessionManager.ClientID.GetValueOrDefault(),
                    LoginID = QuoteForPage.Salesman,
                    LoginUserID = _sessionManager.LoginID.GetValueOrDefault(),
                });

                if (!isTabAllow && isNotDifferentClientOrNotGSA)
                {
                    return false;
                }
            }
            return true;
        }
        private void CheckPermission()
        {
            if (IsDifferentClient && IsGSA)
            {
                CanEditQuoteMainInfo = IsGSAEditPermission;
            }
        }
        private void AddBreadCrumbs()
        {
            BreadCrumb.Add(Navigations.Home);
            BreadCrumb.Add(Navigations.Orders);
            BreadCrumb.Add(Navigations.Quotes);
            BreadCrumb.Add(Navigations.QuoteDetails(QuoteForPage != null ? QuoteForPage.QuoteNumber.ToString() : ""));
        }
        private async Task UpdateRecentlyView()
        {
            var lastBreadcrumb = BreadCrumb[BreadCrumb.Count - 1];
            var command = new CreateRecentlyViewedCommand
            {
                LoginNo = HttpContext.Session.GetInt32(SessionKey.LoginID),
                PageTitle = lastBreadcrumb.PageTitle,
                PageUrl = $"{lastBreadcrumb.CtaUri}?qt={QuoteId}"
            };
            await _mediator.Send(command);
        }

        private void SetUpPermissions()
        {
            if (_securityManager == null) return;
            CanEditQuoteMainInfo = _securityManager.CheckPagePermission(SecurityFunction.Orders_Quote_MainInfo_Edit);
            CanCreateSalesOrder = _securityManager.CheckPagePermission(SecurityFunction.Orders_SalesOrder_Add);
            CanCloseAll = _securityManager.CheckPagePermission(SecurityFunction.Orders_QuoteLines_Close);
            CanEditAS9120 = _securityManager.CheckPagePermission(SecurityFunction.Orders_QuoteAndSO_Edit_AS9120);
        }
        private async Task SetupContent()
        {
            if (QuoteForPage != null)
            {
                var advisoryNotes = await _mediator.Send(new GetCompanyAdvisoryNotesQuery(QuoteForPage.CompanyNo));
                AdvisoryNotes = advisoryNotes.Data?.AdvisoryNotes;
            }
        }
    }
}
