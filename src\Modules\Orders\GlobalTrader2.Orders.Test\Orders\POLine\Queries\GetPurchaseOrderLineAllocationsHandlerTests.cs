using GlobalTrader2.Core.Bases;
using GlobalTrader2.Dto.Orders.Requirements;
using GlobalTrader2.Dto.PurchaseOrderLine;
using GlobalTrader2.Orders.UseCases.Orders.POLine.Queries.GetPurchaseOrderLineAllocations;
using GlobalTrader2.Orders.UseCases.Orders.POLine.Queries.GetPurchaseOrderLineDetails;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CompanyAdvisoryNote.CompanyAdvisoryNote.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.CurrentAtDate.Queries;
using MediatR;

namespace GlobalTrader2.Orders.Test.Orders.POLine.Queries
{
    public class GetPurchaseOrderLineAllocationsHandlerTests
    {
        private readonly IFixture _fixture;
        private readonly Mock<IBaseRepository<PurchaseOrderLineAllocationReadModel>> _mockRepo;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IMediator> _mockMediator;
        private readonly GetPurchaseOrderLineAllocationsHandler _handler;

        public GetPurchaseOrderLineAllocationsHandlerTests()
        {
            _fixture = new Fixture();
            _mockRepo = new Mock<IBaseRepository<PurchaseOrderLineAllocationReadModel>>();
            _mockMapper = new Mock<IMapper>();
            _mockMediator = new Mock<IMediator>();
            _handler = new GetPurchaseOrderLineAllocationsHandler(
                _mockRepo.Object,
                _mockMapper.Object,
                _mockMediator.Object);
        }

        [Fact]
        public async Task Handle_GivenRepositoryReturnsNull_ShouldReturnEmptyList()
        {
            // Arrange
            var query = new GetPurchaseOrderLineAllocationsQuery(42, false, "USD");
            _mockRepo
                .Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync((IReadOnlyList<PurchaseOrderLineAllocationReadModel>)null!);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            Assert.Empty(result.Data);
        }

        [Fact]
        public async Task Handle_GivenNoPOHubAndRepositoryReturnsNonEmptyList_ShouldMapAndFormatStrings()
        {
            // Arrange
            var query = new GetPurchaseOrderLineAllocationsQuery(7, false, "EUR");
            var readModels = _fixture.CreateMany<PurchaseOrderLineAllocationReadModel>(2).ToList();
            var dtos = _fixture.CreateMany<PurchaseOrderLineAllocationDto>(2).ToList();

            _mockRepo
                .Setup(r => r.SqlQueryRawAsync(It.Is<string>(s => s.Contains("usp_selectAll_Allocation_for_PurchaseOrderLine")), It.IsAny<object[]>()))
                .ReturnsAsync(readModels);
            _mockMapper
                .Setup(m => m.Map<IEnumerable<PurchaseOrderLineAllocationDto>>(readModels))
                .Returns(dtos);

            // Stub formatting and mediator calls
            _mockMediator
                .Setup(m => m.Send(It.IsAny<GetCompanyAdvisoryNoteQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<string> { Success = true, Data = "Note\nLine" });
            _mockMediator
                .Setup(m => m.Send(It.Is<GetCurrentAtDateQuery>(q => true), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<CurrentAtDateDto> { Success = true, Data = new CurrentAtDateDto { ExchangeRate = 1.1 } });

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
            Assert.Equal(dtos.OrderBy(d => d.Id), result.Data);
            foreach (var item in result.Data)
            {
                Assert.False(string.IsNullOrEmpty(item.FormattedQuantity));
                Assert.Equal(query.IsPOHub, item.IsPoHub);
                Assert.Equal("Note<br />Line", item.CustomerAdvisoryNotes);
                Assert.Contains("%", item.Margin);
            }
            _mockMediator.Verify(m => m.Send(It.Is<GetCompanyAdvisoryNoteQuery>(c => c.Id == dtos[0].CustomerNo), It.IsAny<CancellationToken>()), Times.Once);
            _mockMediator.Verify(m => m.Send(It.Is<GetCompanyAdvisoryNoteQuery>(c => c.Id == dtos[1].CustomerNo), It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task Handle_GivenPOHubTrueAndRepositoryReturnsNonEmptyList_ShouldCallDetailsAndExchangeQueries()
        {
            // Arrange
            var query = new GetPurchaseOrderLineAllocationsQuery(9, true, "USD");
            var readModels = _fixture.CreateMany<PurchaseOrderLineAllocationReadModel>(1).ToList();
            var dtos = _fixture.CreateMany<PurchaseOrderLineAllocationDto>(1).ToList();

            _mockRepo
                .Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(readModels);
            _mockMapper
                .Setup(m => m.Map<IEnumerable<PurchaseOrderLineAllocationDto>>(readModels))
                .Returns(dtos);

            // Setup detail query
            var detailsDto = _fixture.Create<PurchaseOrderLineDetailsDto>();
            _mockMediator
                .Setup(m => m.Send(It.Is<GetPurchaseOrderLineDetailsQuery>(q => q.PurchaseOrderLineId == query.PurchaseOrderLineId), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<PurchaseOrderLineDetailsDto> { Success = true, Data = detailsDto });

            // Setup advisory notes & currency queries
            _mockMediator
                .Setup(m => m.Send(It.IsAny<GetCompanyAdvisoryNoteQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<string> { Success = true, Data = string.Empty });
            _mockMediator
                .SetupSequence(m => m.Send(It.IsAny<GetCurrentAtDateQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new BaseResponse<CurrentAtDateDto> { Success = true, Data = new CurrentAtDateDto { ExchangeRate = 1 } })
                .ReturnsAsync(new BaseResponse<CurrentAtDateDto> { Success = true, Data = new CurrentAtDateDto { ExchangeRate = 2 } })
                .ReturnsAsync(new BaseResponse<CurrentAtDateDto> { Success = true, Data = new CurrentAtDateDto { ExchangeRate = 3 } })
                .ReturnsAsync(new BaseResponse<CurrentAtDateDto> { Success = true, Data = new CurrentAtDateDto { ExchangeRate = 4 } })
                .ReturnsAsync(new BaseResponse<CurrentAtDateDto> { Success = true, Data = new CurrentAtDateDto { ExchangeRate = 5 } })
                .ReturnsAsync(new BaseResponse<CurrentAtDateDto> { Success = true, Data = new CurrentAtDateDto { ExchangeRate = 6 } });

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            _mockMediator.Verify(m => m.Send(It.IsAny<GetPurchaseOrderLineDetailsQuery>(), It.IsAny<CancellationToken>()), Times.Once);
            _mockMediator.Verify(m => m.Send(It.IsAny<GetCurrentAtDateQuery>(), It.IsAny<CancellationToken>()), Times.AtLeast(5));
        }
    }
}