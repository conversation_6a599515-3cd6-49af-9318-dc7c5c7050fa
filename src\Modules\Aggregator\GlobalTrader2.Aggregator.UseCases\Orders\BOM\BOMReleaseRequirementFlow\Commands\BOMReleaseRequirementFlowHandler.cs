using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyReleaseBom;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.ReleaseRequirement.Commands;

namespace GlobalTrader2.Aggregator.UseCases.Orders.BOM.BOMReleaseRequirementFlow.Commands
{
    public class BomReleaseRequirement<PERSON>lowHandler(IMediator mediator) : IRequestHandler<BomReleaseRequirementFlowCommand, BaseResponse<bool>>
    {
        private readonly IMediator _mediator = mediator;

        public async Task<BaseResponse<bool>> Handle(BomReleaseRequirementFlowCommand request, CancellationToken cancellationToken)
        {
            ArgumentNullException.ThrowIfNull(request);

            var response = new BaseResponse<bool>();
            
            var releaseRes = await _mediator.Send(new ReleaseRequirementCommand
            {
                CustomerRequirementId = request.CustomerRequirementId,
                BomId = request.BomId,
                LoginId = request.UpdatedBy,
            }, cancellationToken);

            response.Success = true;
            return response;
        }
    }
}
