@using GlobalTrader2.SharedUI.Interfaces
@using GlobalTrader2.Dto.SalesOrderLine

@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Localization
@inject IViewLocalizer _localizer
@inject IWebResourceManager WebResourceManager
@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer

@model LinesSectionViewModel




<div id="close-line-dialog" class="dialog-container" title="@_commonLocalizer["Close Sales Order Line"]" style="display: none;">
    <div class="dialog-description">
        <span>@_messageLocalizer["Are you sure you would like to close this"]<strong>@_messageLocalizer[" Sales Order Line"]</strong>?</span>
    </div>
    <div class="d-flex flex-column gap-3 mt-3">
        <div class="d-flex align-items-center">
            <div style="min-width: 120px;">
                <div class="fw-bold mb-0">@_commonLocalizer["Customer"]</div>
            </div>
            <div class="flex-grow-1">
                <span id="close-line-customer"></span>
            </div>
        </div>
        <div class="d-flex align-items-center">
            <div style="min-width: 120px;">
                <div class="fw-bold mb-0">@_commonLocalizer["Sales Order"]</div>
            </div>
            <div class="flex-grow-1">
                <span>@Model.SalesOrderNumber</span>
            </div>
        </div>
        <div class="d-flex align-items-center">
            <div style="min-width: 120px;">
                <label class="fw-bold mb-0" for="reset-checkbox">@_localizer["Reset Quantity?"]</label>
            </div>
            <div class="flex-grow-1">
                <input class="form-check-input check-sm" type="checkbox" id="reset-checkbox" checked>
            </div>
        </div>
    </div>
</div>