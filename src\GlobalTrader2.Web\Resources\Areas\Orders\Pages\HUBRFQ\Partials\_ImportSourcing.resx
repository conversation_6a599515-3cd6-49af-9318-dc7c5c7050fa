﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Correction" xml:space="preserve">
    <value>Correction</value>
  </data>
  <data name="CSV Data" xml:space="preserve">
    <value>CSV Data</value>
  </data>
  <data name="Display Raw CSV Data" xml:space="preserve">
    <value>Display Raw CSV Data</value>
  </data>
  <data name="GT MFR" xml:space="preserve">
    <value>GT MFR</value>
  </data>
  <data name="Import Sourcing Title" xml:space="preserve">
    <value>Use "Export to Excel" function to have import template</value>
  </data>
  <data name="Incorrect MFR" xml:space="preserve">
    <value>Incorrect MFR</value>
  </data>
  <data name="Incorrect Supplier" xml:space="preserve">
    <value>Incorrect Supplier</value>
  </data>
  <data name="Please see below data to be bulk imported into GT. Please note line: highlighted in red indicate a data mismatch with GT and will require correction before being able to successfully import into GT" xml:space="preserve">
    <value>Please see below data to be bulk imported into GT. Please note line: highlighted in red indicate a data mismatch with GT and will require correction before being able to successfully import into GT</value>
  </data>
  <data name="Replace All" xml:space="preserve">
    <value>Replace All</value>
  </data>
  <data name="Reset_Title" xml:space="preserve">
    <value>Reset The Form</value>
  </data>
  <data name="Reset_Message" xml:space="preserve">
    <value>System will reset all the form. Are you sure you would like to proceed?</value>
  </data>
  <data name="Show only data row with a data mismatch" xml:space="preserve">
    <value>Show only data row with a data mismatch</value>
  </data>
  <data name="Replace_Successful_Msg" xml:space="preserve">
    <value>&lt;b&gt;Replace successful: [#recordCount#] record(s) updated.&lt;/b&gt;</value>
  </data>
  <data name="Unsave_Warning_Title" xml:space="preserve">
    <value>Unsaved Changes Warning</value>
  </data>
  <data name="Unsave_Warning_Message" xml:space="preserve">
    <value>Your input data has not been saved and will be cleared for the action. Do you want to continue?</value>
  </data>
  <data name="Save_Successful_Msg" xml:space="preserve">
    <value>&lt;b&gt;Update successful: [#recordCount#] record(s) updated.&lt;b&gt;</value>
  </data>
  <data name="Import_Successful_Msg" xml:space="preserve">
    <value>&lt;b&gt;Import sourcing results succeed: [#recordCount#] record(s).&lt;b&gt;</value>
  </data>
</root>