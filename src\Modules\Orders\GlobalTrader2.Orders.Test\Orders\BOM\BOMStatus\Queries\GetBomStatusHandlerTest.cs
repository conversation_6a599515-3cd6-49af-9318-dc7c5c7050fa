using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMStatus.Queries;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.BOM.BOMStatus.Queries
{
    public class GetBomStatusHandlerTest
    {
        private readonly Mock<IBaseRepository<VwBomModel>> _repositoryMock;
        private readonly GetBomStatusHandler _handler;
        private readonly IFixture _fixture;

        public GetBomStatusHandlerTest()
        {
            _fixture = new Fixture();
            _repositoryMock = new Mock<IBaseRepository<VwBomModel>>();
            _handler = new GetBomStatusHandler(_repositoryMock.Object);
        }

        [Fact]
        public async Task Handle_ReturnsSuccessWithStatus_WhenBomExistsWithStatus()
        {
            // Arrange
            var bomId = _fixture.Create<int>();
            var expectedStatus = _fixture.Create<string>();
            var bomModel = _fixture.Build<VwBomModel>()
                .With(x => x.BOMId, bomId)
                .With(x => x.Status, expectedStatus)
                .Create();

            var query = new GetBomStatusQuery(bomId);

            _repositoryMock
                .Setup(repo => repo.GetAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<VwBomModel, bool>>>()))
                .ReturnsAsync(bomModel);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(expectedStatus, result.Data);
            _repositoryMock.Verify(repo => repo.GetAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<VwBomModel, bool>>>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ReturnsSuccessWithNullStatus_WhenBomExistsWithNullStatus()
        {
            // Arrange
            var bomId = _fixture.Create<int>();
            var bomModel = _fixture.Build<VwBomModel>()
                .With(x => x.BOMId, bomId)
                .With(x => x.Status, (string?)null)
                .Create();

            var query = new GetBomStatusQuery(bomId);

            _repositoryMock
                .Setup(repo => repo.GetAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<VwBomModel, bool>>>()))
                .ReturnsAsync(bomModel);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.Null(result.Data);
            _repositoryMock.Verify(repo => repo.GetAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<VwBomModel, bool>>>()), Times.Once);
        }

        [Fact]
        public async Task Handle_ReturnsSuccessWithNullData_WhenBomDoesNotExist()
        {
            // Arrange
            var bomId = _fixture.Create<int>();
            var query = new GetBomStatusQuery(bomId);

            _repositoryMock
                .Setup(repo => repo.GetAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<VwBomModel, bool>>>()))
                .ReturnsAsync((VwBomModel?)null);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.Null(result.Data);
            _repositoryMock.Verify(repo => repo.GetAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<VwBomModel, bool>>>()), Times.Once);
        }

        [Fact]
        public async Task Handle_CallsRepositoryWithCorrectPredicate_WhenHandlingQuery()
        {
            // Arrange
            var bomId = _fixture.Create<int>();
            var query = new GetBomStatusQuery(bomId);

            _repositoryMock
                .Setup(repo => repo.GetAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<VwBomModel, bool>>>()))
                .ReturnsAsync((VwBomModel?)null);

            // Act
            await _handler.Handle(query, CancellationToken.None);

            // Assert
            _repositoryMock.Verify(repo => repo.GetAsync(
                It.Is<System.Linq.Expressions.Expression<System.Func<VwBomModel, bool>>>(
                    expr => expr != null)), Times.Once);
        }

        [Theory]
        [InlineData(1)]
        [InlineData(100)]
        [InlineData(999)]
        public async Task Handle_WorksWithVariousBomIds_WhenProvidedDifferentIds(int bomId)
        {
            // Arrange
            var query = new GetBomStatusQuery(bomId);

            _repositoryMock
                .Setup(repo => repo.GetAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<VwBomModel, bool>>>()))
                .ReturnsAsync((VwBomModel?)null);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            _repositoryMock.Verify(repo => repo.GetAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<VwBomModel, bool>>>()), Times.Once);
        }
    }
}
