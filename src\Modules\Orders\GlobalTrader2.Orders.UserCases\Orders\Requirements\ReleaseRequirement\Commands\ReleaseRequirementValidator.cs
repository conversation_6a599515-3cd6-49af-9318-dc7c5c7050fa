using FluentValidation;

namespace GlobalTrader2.Orders.UserCases.Orders.Requirements.ReleaseRequirement.Commands;

public class ReleaseRequirementValidator : AbstractValidator<ReleaseRequirementCommand>
{
    public ReleaseRequirementValidator()
    {
        RuleFor(x => x.CustomerRequirementId).GreaterThan(0);

        RuleFor(x => x.BomId).GreaterThan(0);

        RuleFor(x => x.LoginId).GreaterThan(0);
    }
}
