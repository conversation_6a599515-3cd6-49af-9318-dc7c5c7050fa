﻿using GlobalTrader2.Core.Infrastructure.Services;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.Templates;
using GlobalTrader2.Orders.UserCases.Orders.PrintEmailLog.Commands;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.Company.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.Queries;
using IronPdf;
using Microsoft.AspNetCore.Http;
using System.Linq;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Pages.Print;

public class IndexModel : BasePageModel
{
    private readonly IRazorViewToStringService _razorViewToStringService;
    private readonly IPdfService _pdfService;
    private readonly IMediator _mediator;
    private readonly SessionManager _sessionManager;
    private readonly IMapper _mapper;

    [BindProperty(SupportsGet = true)]
    [FromQuery(Name = "pro")]
    public ListForPrint PrintObject { get; set; }

    [BindProperty(SupportsGet = true)]
    [FromQuery(Name = "id")]
    public int? GenericID { get; set; }

    [BindProperty(SupportsGet = true)]
    [FromQuery(Name = "req")]
    public int? Req { get; set; }

    [BindProperty(SupportsGet = true)]
    [FromQuery(Name = "section")]
    public string? Section { get; set; }

    public IndexModel(IRazorViewToStringService razorViewToStringService, IPdfService pdfService, IMediator mediator, SessionManager sessionManager, IMapper mapper)
    {
        _razorViewToStringService = razorViewToStringService;
        _pdfService = pdfService;
        _mediator = mediator;
        _sessionManager = sessionManager;
        _mapper = mapper;
    }

    public async Task<IActionResult> OnGet()
    {
        if (PrintObject == ListForPrint.SingleRequirement && GenericID.HasValue && Req.HasValue)
        {
            var companyReq = await _mediator.Send(new GetCompanyMainInfoQuery()
            {
                Id = GenericID.Value
            });
            var company = companyReq.Data;
            var cusReqListReq = await _mediator.Send(new GetListForCustomerRequirementQuery()
            {
                ClientId = _sessionManager.GetInt32(SessionKey.ClientID)!.Value,
                CustomerRequirementNo = Req.Value
            });
            var cusReqList = cusReqListReq.Data!;
            if (!cusReqList!.Any())
            {
                return Page();
            }
            var htmls = new List<string>();
            for (var i = 0; i < cusReqList.Count(); i += 2)
            {
                var html = await _razorViewToStringService.RenderViewToStringAsync("Templates/Pdf/_CustomerReqFormPDF",
                                new EnquiryProgressTemplate()
                                {
                                    Index = i,
                                    Company = new CompanyInfoTemplate()
                                    {
                                        CompanyName = company?.CompanyName ?? string.Empty,
                                        ContactName = cusReqList!.ElementAt(0)!.ContactName ?? string.Empty,
                                        Fax = company?.Fax ?? string.Empty,
                                        SalesmanName = company?.SalesmanName ?? string.Empty,
                                        Telephone = company?.Telephone ?? string.Empty,
                                    },
                                    CustomerRequirements = _mapper.Map<List<CustomerRequirementTemplate>>(cusReqList.Skip(i).Take(2)),
                                });
                htmls.Add(html);
            }

            var option = new ChromePdfRenderOptions
            {
                PaperSize = IronPdf.Rendering.PdfPaperSize.A3,
                MarginLeft = 8,
                MarginRight = 8,
                MarginTop = 10,
                MarginBottom = 10,
                PaperOrientation = IronPdf.Rendering.PdfPaperOrientation.Landscape
            };

            var pdf = await _pdfService.CreateFromPdfAsync([.. htmls], option);

            await InsertLog("CustomerRequirement");
            Response.Headers.Append("Content-Disposition", "inline");
            return File([.. pdf], "application/pdf");
        }
        if (PrintObject == ListForPrint.PrintHUBRFQ || PrintObject == ListForPrint.EmailHUBRFQ)
        {
            await InsertLog("PrintHUBRFQ");
        }

        return Page();
    }

    private async Task InsertLog(string printSection)
    {
        if (string.IsNullOrEmpty(printSection) || printSection == "PrintLog")
            return;

        var updatedBy = _sessionManager.GetInt32(SessionKey.LoginID);
        var sectionName = printSection;
        var subSectionName = PrintObject.ToString();
        var actionName = "Print";
        var detail = "Action¦¦Print";

        if (GenericID != null && GenericID > 0)
        {
            await _mediator.Send(new CreatePrintEmailLogCommand
            {
                SectionName = sectionName,
                SubSectionName = subSectionName,
                ActionName = actionName,
                DocumentNo = GenericID.Value,
                UpdatedBy = updatedBy,
                Detail = detail
            });
            return;
        }

        var lnsCookie = Request.Cookies["lns"];
        if (lnsCookie == null)
            return;

        foreach (var idStr in lnsCookie.Split('|', StringSplitOptions.RemoveEmptyEntries))
        {
            if (int.TryParse(idStr, out int documentId))
            {
                await _mediator.Send(new CreatePrintEmailLogCommand
                {
                    SectionName = sectionName,
                    SubSectionName = subSectionName,
                    ActionName = actionName,
                    DocumentNo = documentId,
                    UpdatedBy = updatedBy,
                    Detail = detail
                });
            }
        }
    }
}
