namespace GlobalTrader2.Dto.PurchaseOrderLine
{
    public class PurchaseOrderLineAllocationDto
    {
        public int Id { get; set; }
        public string Part { get; set; } = null!;
        public byte? ROHS { get; set; }
        public int? StockNo { get; set; }
        public string? CustomerPart { get; set; }
        public int CustomerNo { get; set; }
        public string? Customer { get; set; }
        public string CustomerAdvisoryNotes { get; set; } = string.Empty;
        public string? CustomerPO { get; set; }
        public int? SalesOrderNo { get; set; }
        public int? SalesOrderNumber { get; set; }
        public int? SalesOrderLineNo { get; set; }
        public int? SOSerialNo { get; set; }
        public DateTime? DatePromised { get; set; }
        public bool? ShipASAP { get; set; }
        public int? SRMANo { get; set; }
        public int? SRMANumber { get; set; }
        public DateTime? ReturnDate { get; set; }
        public int Quantity { get; set; }
        public decimal? Price { get; set; }
        public string? CurrencyCode { get; set; }
        public string? DateCode { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public string Margin { get; set; } = string.Empty;
        public string MarginValue { get; set; } = string.Empty;
        public bool IsPoHub { get; set; }
        public decimal? CustomerTargetPrice { get; set; }
        public int? CustomerTargetCurrency { get; set; }
        public int? SoCurrencyMarginNo { get; set; }
        public decimal? SourcingPrice { get; set; }
        public int? SourcingCurrency { get; set; }

        // formatted strings for UI display
        public string LineNo { get; set; } = string.Empty;
        public string? FormattedDatePromised { get; set; }
        public string? FormattedReturnDate { get; set; }
        public string FormattedQuantity { get; set; } = string.Empty;
        public string FormattedPrice { get; set; } = string.Empty;
        public string? FormattedDeliveryDate { get; set; }

        // data for PO-Hub users only
        public string? PriceToClient { get; set; }
        public string? Profit { get; set; }
        public string? SellExchangeRate { get; set; }
        public string? ExchangeRate { get; set; }
    }
}