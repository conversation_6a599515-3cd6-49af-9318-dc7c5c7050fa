import { SystemDocument } from "../config/system-document-enum.js?v=#{BuildVersion}#";
import { getPageUrl, QueryString, ListEnum } from "./url-helper.js?v=#{BuildVersion}#";

export class ButtonHelper {
    static nubButton_SystemDocument(intSysDocTypeNo, intKeyNo, intSysDocNumber) {
        let strOut = "";
        if (intSysDocTypeNo <= 0) return "";
        switch (intSysDocTypeNo) {
            case SystemDocument.CreditNote: strOut = ButtonHelper.createNubButton(ButtonHelper.URL_CreditNote(intKeyNo), intSysDocNumber); break;
            case SystemDocument.CustomerRequirement: strOut = ButtonHelper.createNubButton(ButtonHelper.URL_CustomerRequirement(intKeyNo), intSysDocNumber); break;
            case SystemDocument.CustomerRMA: strOut = ButtonHelper.createNubButton(ButtonHelper.URL_CRMA(intKeyNo), intSysDocNumber); break;
            case SystemDocument.DebitNote: strOut = ButtonHelper.createNubButton(ButtonHelper.URL_DebitNote(intKeyNo), intSysDocNumber); break;
            case SystemDocument.GoodsIn: strOut = ButtonHelper.createNubButton(ButtonHelper.URL_GoodsIn(intKeyNo), intSysDocNumber); break;
            case SystemDocument.Invoice: strOut = ButtonHelper.createNubButton(ButtonHelper.URL_Invoice(intKeyNo), intSysDocNumber); break;
            case SystemDocument.PurchaseOrder: strOut = ButtonHelper.createNubButton(ButtonHelper.URL_PurchaseOrder(intKeyNo), intSysDocNumber); break;
            case SystemDocument.Quote: strOut = ButtonHelper.createNubButton(ButtonHelper.URL_Quote(intKeyNo), intSysDocNumber, "link-align-left"); break;
            case SystemDocument.SalesOrder: strOut = ButtonHelper.createNubButton(ButtonHelper.URL_SalesOrder(intKeyNo), intSysDocNumber); break;
            case SystemDocument.SupplierRMA: strOut = ButtonHelper.createNubButton(ButtonHelper.URL_SRMA(intKeyNo), intSysDocNumber); break;
            case SystemDocument.PurchaseRequisition: strOut = ButtonHelper.createNubButton(ButtonHelper.URL_PurchaseRequisition(intKeyNo), intSysDocNumber); break;
            case SystemDocument.SupplierInvoice: strOut = ButtonHelper.createNubButton(ButtonHelper.URL_SupplierInvoice(intKeyNo), intSysDocNumber); break;
            case SystemDocument.InternalPurchaseOrder: strOut = ButtonHelper.createNubButton(ButtonHelper.URL_InternalPurchaseOrder(intKeyNo), intSysDocNumber); break;
            case SystemDocument.PurchaseQuote: strOut = ButtonHelper.createNubButton(ButtonHelper.URL_POQuote(intKeyNo), intSysDocNumber); break;
            case SystemDocument.ClientInvoice: strOut = ButtonHelper.createNubButton(ButtonHelper.URL_ClientInvoice(intKeyNo), intSysDocNumber); break;
            case _: return strOut;
        }
        return strOut;
    }
    static nubButton_Contact(intID, strName) {
        let button = strName;
        if (intID > 0) {
            button = ButtonHelper.createNubButton(ButtonHelper.URL_Contact(intID), GlobalTrader.StringHelper.setCleanTextValue(strName));
        }
        return button
    }
    static nubButton_SoQuote(intID, strName, quoteLineId = null) {
        let button = '';
        if (intID > 0) {
            button = ButtonHelper.createNubButton(ButtonHelper.URL_SoQuote(intID, quoteLineId), GlobalTrader.StringHelper.setCleanTextValue(strName));
        }
        return button
    }
    static nubButton_SalesOrderLine(soId, name, solId = null) {
        let button = '';
        if (soId > 0) {
            button = ButtonHelper.createNubButton(ButtonHelper.URL_SalesOrderLine(soId, solId), GlobalTrader.StringHelper.setCleanTextValue(name));
        }
        return button;
    }
    static nubButton_CustomerRequirement(intID, strName) {
        let button = strName;
        if (intID > 0) {
            button = ButtonHelper.createNubButton(ButtonHelper.URL_CustomerRequirement(intID), GlobalTrader.StringHelper.setCleanTextValue(strName));
        }
        return button
    }
    static nubButton_Company(intID, strName, strNotes, intTab = null, enmCompanyListType = null) {
        let button = strName;
        if (intID > 0) {
            button = ButtonHelper.createNubButton(ButtonHelper.URL_Company(intID, intTab, enmCompanyListType), strName);
        }
        if (strNotes) {
            const toolTip = ButtonHelper.createAdvisoryNotesIcon(strNotes);
            button = `
            <span>
            ${button}
            <span> </span>${toolTip}
            </span>
            `
        }
        return button;
    }
    static nubButton_CompanyOpenInNewTab(intID, strName, strNotes, intTab = null, enmCompanyListType = null) {
        let button = strName;
        if (intID > 0) {
            button = ButtonHelper.createNubButtonOpenInNewTab(ButtonHelper.URL_Company(intID, intTab, enmCompanyListType), strName);
        }
        if (strNotes) {
            const toolTip = ButtonHelper.createAdvisoryNotesIcon(strNotes);
            button = `
            <span>
            ${button}
            <span> </span>${toolTip}
            </span>
            `
        }
        return button;
    }
    static nubButton_PurchaseInformation(id, no) {
        return this.createNubButton(ButtonHelper.URL_PurchaseOrder(id), no);
    }
    static nubButton_Quote(intID, strNumber) {
        let button = strNumber;
        if (intID > 0)
            button = this.nubButton_SystemDocument(SystemDocument.Quote, intID, strNumber);
        return button;
    }
    static createNubButton(url, text, customCSS = "") {
        return `<a class="dt-hyper-link ${customCSS}" href="${url}">${text}</a>`;
    }
    static createNubButtonOpenInNewTab(url, text, customCSS = "") {
        return `<a class="dt-hyper-link ${customCSS}" target="_blank" href="${url}">${text}</a>`;
    }
    static nubButton_Manufacturer(intID, strName, strNotes) {
        let button = strName;
        if (intID > 0) {
            button = ButtonHelper.createNubButton(ButtonHelper.URL_Manufacturer(intID), strName);
        }
        if (strNotes) {
            const toolTip = ButtonHelper.createAdvisoryNotesIcon(strNotes);
            button = `
                <span>
                    ${button}
                    <span> </span>${toolTip}
                </span>
            `
        }
        return button;
    }

    static nubButton_Service(id, name) {
        return this.createNubButton(ButtonHelper.URL_Service(id), GlobalTrader.StringHelper.setCleanTextValue(name));
    }

    static createAdvisoryNotesIcon(notes) {
        if (!notes) {
            return "";
        }

        const formatedNotes = GlobalTrader.StringHelper.formatAdvisoryNotes(notes)
        return `<img src="/img/icons/circle-exclamation-red.svg" title="${formatedNotes}" class="ms-1 rounded-circle bg-white" height="14" data-bs-toggle="tooltip" data-bs-placement="bottom" />`;
    }

    static createAdvisoryNotesIconLabel(notes) {
        if (!notes) {
            return "";
        }

        const formatedNotes = GlobalTrader.StringHelper.formatAdvisoryNotes(notes)
        return `<img src="/img/icons/circle-exclamation-red.svg" title="${formatedNotes}" class="ms-1 rounded-circle bg-white mb-4px" height="14" data-bs-toggle="tooltip" data-bs-placement="bottom" />`;
    }

    static createViewPdfFileHyperlink(fileName, customCSS, onClick) {
        const $el = $(`
            <a class="dt-hyper-link ${customCSS}" style="cursor: pointer;">
                <img src="/img/icons/file-pdf.png" title="${fileName}" width="24" height="24"/>
                <span>${fileName}</span>
            </a>
        `);

        if (typeof onClick === 'function') {
            $el.on('click', onClick);
        }

        return $el;
    }

    static createOnStopIcon() {
        const resultDiv = document.createElement("span");
        resultDiv.id = "onstop";
        resultDiv.className = "onstop-badge";

        const img = document.createElement("img");
        img.src = "/img/icons/slash.svg";
        img.height = 10;
        img.alt = "OnStop";

        const span = document.createElement("span");
        span.textContent = "On Stop";
        span.className = 'text ms-1 fw-bold';

        resultDiv.appendChild(img);
        resultDiv.appendChild(span);

        return resultDiv.outerHTML;
    }

    static URL_Service(id) {
        return `/warehouse/service/details?srv=${id}`;
    }

    static URL_Contact(intID) {
        let strOut = "/";
        const urlPage = getPageUrl(ListEnum.Contact_ContactDetail);
        const queryStringContactId = QueryString.ContactID;
        strOut = strOut + `${urlPage}?${queryStringContactId}=${intID}`;

        const urlParams = new URLSearchParams(window.location.search);
        const queryStringCltValue = urlParams.get(QueryString.CompanyListType);
        if (queryStringCltValue) {
            strOut = strOut + `&${QueryString.CompanyListType}=${queryStringCltValue}`;
        }
        return strOut;
    }
    static URL_CreditNote(intId) {
        const urlPage = getPageUrl(ListEnum.Orders_CreditNoteDetail);
        const queryString = QueryString.CreditID;
        return `/${urlPage}?${queryString}=${intId}`;
    }
    static URL_CustomerRequirement(intId) {
        const urlPage = getPageUrl(ListEnum.Orders_CustomerRequirementDetail);
        const queryString = QueryString.CustomerRequirementID;
        return `/${urlPage}?${queryString}=${intId}`;
    }
    static URL_CRMA(intId) {
        const urlPage = getPageUrl(ListEnum.Orders_CustomerRMADetail);
        const queryString = QueryString.CRMAID;
        return `/${urlPage}?${queryString}=${intId}`;
    }
    static URL_DebitNote(intId) {
        const urlPage = getPageUrl(ListEnum.Orders_DebitNoteDetail);
        const queryString = QueryString.DebitID;
        return `/${urlPage}?${queryString}=${intId}`;
    }
    static URL_GoodsIn(intId) {
        const urlPage = getPageUrl(ListEnum.Warehouse_GoodsInDetail);
        const queryString = QueryString.GoodsInID;
        return `/${urlPage}?${queryString}=${intId}`;
    }
    static URL_Invoice(intId) {
        const urlPage = getPageUrl(ListEnum.Orders_InvoiceDetail);
        const queryString = QueryString.InvoiceID;
        return `/${urlPage}?${queryString}=${intId}`;
    }
    static URL_PurchaseOrder(intId) {
        const urlPage = getPageUrl(ListEnum.Orders_PurchaseOrderDetail);
        const queryString = QueryString.PurchaseOrderID;
        return `/${urlPage}?${queryString}=${intId}`;
    }
    static URL_Quote(intId) {
        const urlPage = getPageUrl(ListEnum.Orders_QuoteDetail);
        const queryString = QueryString.QuoteID;
        return `/${urlPage}?${queryString}=${intId}`;
    }
    static URL_SoQuote(intId, quoteLineId = null) {
        const urlPage = getPageUrl(ListEnum.Orders_QuoteDetail);
        const queryString = QueryString.QuoteID;
        let string = `/${urlPage}?${queryString}=${intId}`;
        if (quoteLineId != null && quoteLineId > 0) {
            string += `&${QueryString.QuoteLineID}=${quoteLineId}`;
        }
        return string;
    }
    static URL_SalesOrder(intId) {
        const urlPage = getPageUrl(ListEnum.Orders_SalesOrderDetail);
        const queryString = QueryString.SalesOrderID;
        return `/${urlPage}?${queryString}=${intId}`;
    }
    static URL_SalesOrderLine(soId, solId = null) {
        const urlPage = getPageUrl(ListEnum.Orders_SalesOrderDetail);
        const queryString = QueryString.SalesOrderID;
        let string = `/${urlPage}?${queryString}=${soId}`;
        if (solId != null && solId > 0) {
            string += `&${QueryString.SalesOrderLineID}=${solId}`;
        }
        return string;
    }
    static URL_SRMA(intId) {
        const urlPage = getPageUrl(ListEnum.Orders_SupplierRMADetail);
        const queryString = QueryString.SRMAID;
        return `/${urlPage}?${queryString}=${intId}`;
    }
    static URL_PurchaseRequisition(intId) {
        const urlPage = getPageUrl(ListEnum.Orders_PurchaseRequisitionDetail);
        const queryString = QueryString.PurchaseRequisitionID;
        return `/${urlPage}?${queryString}=${intId}`;
    }
    static URL_SupplierInvoice(intId) {
        const urlPage = getPageUrl(ListEnum.Warehouse_SupplierInvoiceDetail);
        const queryString = QueryString.SupplierInvoiceID;
        return `/${urlPage}?${queryString}=${intId}`;
    }
    static URL_InternalPurchaseOrder(intId) {
        const urlPage = getPageUrl(ListEnum.Orders_InternalPurchaseOrderDetail);
        const queryString = QueryString.InternalPurchaseOrderID;
        return `/${urlPage}?${queryString}=${intId}`;
    }
    static URL_POQuote(intId) {
        const urlPage = "Ord_POQuoteDetail.aspx";
        const queryString = QueryString.POQuoteID;
        return `/${urlPage}?${queryString}=${intId}`;
    }
    static URL_ClientInvoice(intId) {
        const urlPage = getPageUrl(ListEnum.Orders_ClientInvoiceDetail);
        const queryString = QueryString.ClientInvoiceID;
        return `/${urlPage}?${queryString}=${intId}`;
    }
    static URL_Company(intId, intTab, enmCompanyListType) {
        const urlPage = `/${getPageUrl(ListEnum.Contact_CompanyDetail)}`;
        let queryString = `?${QueryString.CompanyID}=${intId}`;
        if (intTab) {
            queryString = queryString + `&${QueryString.Tab}=${intTab}`;
        }
        if (enmCompanyListType) {
            queryString = queryString + `&${QueryString.CompanyListType}=${enmCompanyListType}`;
        }
        return urlPage + queryString;
    }
    static URL_Manufacturer(intId) {
        const urlPage = `/${getPageUrl(ListEnum.Contact_ManufacturerDetail)}`;
        let queryString = `?${QueryString.ManufacturerId}=${intId}`;
        return urlPage + queryString;
    }
    static URL_Quote_Add(intCompanyID, strCompanyName, intReqID, strLineIDs, intContactID) {
        let urlPage = `${getPageUrl(ListEnum.Orders_QuoteAdd)}`;
        urlPage = ButtonHelper.addOrUpdateUrlParam(urlPage, QueryString.CompanyID, intCompanyID);
        urlPage = ButtonHelper.addOrUpdateUrlParam(urlPage, QueryString.CompanyName, strCompanyName);
        urlPage = ButtonHelper.addOrUpdateUrlParam(urlPage, QueryString.CustomerRequirementID, intReqID);
        if (strLineIDs) {
            urlPage += (`${urlPage.length > 0 ? '&' : '?'}${QueryString.LineIDs}=${strLineIDs}`);
        }
        urlPage = ButtonHelper.addOrUpdateUrlParam(urlPage, QueryString.ContactID, intContactID);
        return urlPage;
    }
    static URL_Orders_Customer_Requirement_Print(companyID, companyName, contactID) {
        let urlPage = `${getPageUrl(ListEnum.Orders_CustomerReqPrint)}`;
        urlPage = ButtonHelper.addOrUpdateUrlParam(urlPage, QueryString.CompanyID, companyID);
        urlPage = ButtonHelper.addOrUpdateUrlParam(urlPage, QueryString.CompanyName, companyName);
        urlPage = ButtonHelper.addOrUpdateUrlParam(urlPage, QueryString.ContactID, contactID);
        return urlPage;
    }

    static URL_All_Document(docNo, actiontype, docId) {
        let urlPage = `${getPageUrl(ListEnum.All_Document)}`;
        urlPage = ButtonHelper.addOrUpdateUrlParam(urlPage, QueryString.DocNo, docNo);
        urlPage = ButtonHelper.addOrUpdateUrlParam(urlPage, QueryString.ActionType, actiontype);
        urlPage = ButtonHelper.addOrUpdateUrlParam(urlPage, QueryString.DocId, docId);
        return urlPage;
    }

    static URL_All_IHSDocument(ihsPartNo, iFrom) {
        let urlPage = `${getPageUrl(ListEnum.All_IHSDocument)}`;
        urlPage = ButtonHelper.addOrUpdateUrlParam(urlPage, QueryString.IHSDocument, ihsPartNo);
        urlPage = ButtonHelper.addOrUpdateUrlParam(urlPage, QueryString.IFrom, iFrom);
        return urlPage;
    }

    static URL_Print(genericId, pro, section, cusReqId) {
        let urlPage = `${getPageUrl(ListEnum.Print)}`;
        urlPage = ButtonHelper.addOrUpdateUrlParam(urlPage, QueryString.PrintObject, pro);
        urlPage = ButtonHelper.addOrUpdateUrlParam(urlPage, QueryString.GenericID, genericId);
        urlPage = ButtonHelper.addOrUpdateUrlParam(urlPage, QueryString.Section, section);
        urlPage = ButtonHelper.addOrUpdateUrlParam(urlPage, QueryString.CustomerRequirementID, cusReqId);
        return urlPage;
    }

    static addOrUpdateUrlParam = (urlString, key, value) => {
        if (!value)
            return urlString;
        let path = urlString;
        let queryString = '';
        const queryIndex = urlString.indexOf('?');

        if (queryIndex !== -1) {
            path = urlString.substring(0, queryIndex);
            queryString = urlString.substring(queryIndex + 1);
        }

        const params = new URLSearchParams(queryString);
        params.set(key, value);
        const updatedQueryString = params.toString();
        if (updatedQueryString) {
            return `${path}?${updatedQueryString}`;
        } else {
            return path;
        }
    };
}