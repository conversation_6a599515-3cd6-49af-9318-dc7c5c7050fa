﻿import { SectionBox } from "../../../../components/base/section-box.component.js?v=#{BuildVersion}#";
import { TableFilterComponent } from "../../../../components/table-filter/table-filter.component.js?v=#{BuildVersion}#";
import { ORIGINAL_OFFER_EDIT_FILTER_INPUTS } from "../constants/original-offer-edit-filter-inputs.constants.js?v=#{BuildVersion}#";
import { ButtonHelper } from '../../../../helper/button-helper.js?v=#{BuildVersion}#';

const urlOpenQuoteLine = `/orders/quotes/${stateValue.quoteId}/quote-line-open`;
const urlClosedQuoteLine = `/orders/quotes/${stateValue.quoteId}/quote-line-closed`;
const urlAllQuoteLine = `/orders/quotes/${stateValue.quoteId}/quote-line-all`;
const quoteLineUrls = Object.freeze({
    0: urlOpenQuoteLine,
    1: urlClosedQuoteLine,
    2: urlAllQuoteLine
});

const state = {
    quoteLineSectionBox: null,
    originalOfferEditDialog: null,
    quoteLinesResultsTable: null,
    quoteMainSectionBox: null,
    quoteInfo: null,
    quoteLineUrl: urlOpenQuoteLine
};

$(async () => {
    setupQuoteLineResultBox();

    await loadQuoteLineResultsAsync(false, urlOpenQuoteLine);

    await registerTabChangeEvent();

    initDialogs();

    setupEventListener();
    
    await getQuoteMainInfoAndBinding();

    await initFilter();
})

function setupQuoteLineResultBox() {
    setupQuoteLinesResultsTable();
    state.quoteLineSectionBox = new SectionBox('#quote-lines-box');
    state.quoteLineSectionBox.on('onRefreshed.msb', async () => {
        const selectedIds = state.quoteLinesResultsTable.rows({ selected: true }).data().map(row => row.quoteLineId).toArray();
        await loadQuoteLineResultsAsync(true, state.quoteLineUrl);
        state.quoteLinesResultsTable.rows().every(function () {
            const quoteLineRow = this.data();
            if (selectedIds.includes(quoteLineRow.quoteLineId)) {
                this.select();
            }
        });
        restoreAndScrollToQuoteLineResultRow();
    });

    state.quoteLineSectionBox.init();
    state.quoteMainSectionBox = new SectionBox('#quote-main-information-box');
    state.quoteMainSectionBox.on('onRefreshed.msb', async () => {
        await loadQuoteMainInfoAsync(true);
    });
    state.quoteMainSectionBox.init();
}



function setupQuoteLinesResultsTable(){
    if (state.quoteLinesResultsTable) {
        return;
    }

    state.quoteLinesResultsTable = new DataTable('#quote-lines-results-table', {
        paging: false,
        searching: false,
        ordering: false,
        autoWidth: false,
        scrollCollapse: true,
        loading: true,
        info: false,
        select: {
            style: 'single',
            toggleable: false
        },
        language: {
            emptyTable: localizedTitles.quoteLinesResultsEmptyMsg,
            zeroRecords: localizedTitles.quoteLinesResultsEmptyMsg
        },
        rowCallback: function (row, data) {
            if (data.closed === true) {
                $(row).addClass('text-light-gray');
            }
        },
        columns: [
            {
                ...createDataTableDefaultColumns('part', 'Part No', 'Customer Part'),
                width: "14%",
                render: function(_data, _type, row) {
                    return GlobalTrader.DataTablesHelper.createStackedCell([
                        GlobalTrader.StringHelper.getPartNoAndRohsStatus(row.part, row.rohs),
                        row?.customerPart
                    ]);
                }
            },
            {
                ...createDataTableDefaultColumns('manufacturerCode', 'Mfr', 'DC'),
                width: "14%",
                render: function (_data, _type, row) {
                    return `${GlobalTrader.StringHelper.setCleanTextValue(ButtonHelper.nubButton_Manufacturer(row.manufacturerNo, row.manufacturerCode, row.manufacturerAdvisoryNotes))} <br> 
                            ${GlobalTrader.StringHelper.setCleanTextValue(row.dateCode)}`;
                }
            },
            {
                ...createDataTableDefaultColumns('productDescription', 'Product', 'Package'),
                width: "14%",
                render: function (_data, _type, row) {
                    return `${GlobalTrader.StringHelper.setCleanTextValue(row.productDescription)} <br> 
                            ${GlobalTrader.StringHelper.setCleanTextValue(row.packageName)}`;
                }
            },
            {
                ...createDataTableDefaultColumns('quantity', 'Quantity'),
                width: "14%"
            },
            {
                ...createDataTableDefaultColumns('price', 'Unit Price'),
                width: "14%"
            },
            {
                ...createDataTableDefaultColumns('total', 'Total'),
                width: "14%"
            },
            {
                ...createDataTableDefaultColumns('aS6081', 'Inhouse AS6081 testing required'),
                render: function (_data, _type, row) {
                    return `${row.aS6081 ? 'Yes' : 'No'}`;
                }
            },
        ]
    });
}

async function loadQuoteLineResultsAsync(isRefreshed, url) {
    state.quoteLineSectionBox.loading(true);
    toggleSourcingResultsResizeBar(false);
    if (!isRefreshed) state.quoteLinesResultsTable.rows().deselect();
    let quoteLines = await GlobalTrader.ApiClient.getAsync(url);
    state.quoteLinesResultsTable.rows().remove().draw();
    state.quoteLinesResults = quoteLines.data;
    loadDataToSummaryField(quoteLines.data.freight, quoteLines.data.total);

    if (quoteLines.data.line.length > 0) {
        toggleSourcingResultsResizeBar(true);
        for (let quoteLine of quoteLines.data.line) {
            state.quoteLinesResultsTable.row.add(quoteLine).draw();
        }
        state.quoteLineSectionBox.stopLoading(false);
        setQuoteLinesResultsTableHeight(null, isRefreshed);
    }else {
        toggleSourcingResultsResizeBar(true);
        state.quoteLineSectionBox.stopLoading(false);
        setQuoteLinesResultsTableHeight(95, isRefreshed);
    }
}

function toggleSourcingResultsResizeBar(isVisible) {
    if (isVisible) {
        $("#quote-lines-results-resize").removeClass("d-none");
    } else {
        $("#quote-lines-results-resize").addClass("d-none");
    }
}

function setQuoteLinesResultsTableHeight(height = null, isRefreshed = false) {
    const $container = $("#quote-lines-results-table-container");

    if (height !== null) {
        $container.css("height", `${height}px`);
        return;
    }

    const currentHeight = $container.outerHeight(true);
    const requiredHeight = calculateFirstXRowsHeight("quote-lines-results-table", 10);

    $container.css("height", `${!isRefreshed ? requiredHeight : Math.max(requiredHeight, currentHeight)}px`);
}


function restoreAndScrollToQuoteLineResultRow() {
    const datatable = $("#quote-lines-results-table").DataTable();
    const container = $("#quote-lines-results-table-container");
    const selectedRow = datatable.row({ selected: true }).data();
    if (!selectedRow) return;

    GlobalTrader.Helper.scrollToRowContainer(container, datatable, selectedRow.id);
}


function setupEventListener() {
    $('#quote-lines-calculator-btn').button().on('click', () => {
        window.open('/orders/quotes/salescalculator', '_blank', 'width=400,height=600');
    })

    $('#lines-details-toggle-btn').button().on('click', () => onToggleShowLineDetails());

    $('#original-offer-edit-btn').button().on('click', () => state.originalOfferEditDialog.dialog("open"));

    $(document).on('click', '#orderQuotesDetailsLinesTab button', (e) => onClickTabs($(e.target).attr("tabId")))
}

function loadDataToSummaryField(estimatedFreight, total) {
    $('#lines-details-estimated-freight').text(`${estimatedFreight}`);
    $('#lines-details-total').text(`${total}`);
}

function createDataTableDefaultColumns(name, ...title) {
    return {
        title: renderTitle(...title),
        className: 'text-wrap text-break header-custom',
        data: name,
        name: name
    }
}

function renderTitle(...title) {
    if (title.length < 1)
        return '';
    if (title.length == 1)
        return title[0];
    return GlobalTrader.StringHelper.stringFormat(`<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">{0}</div>{1}`, ...title);
}

function initDialogs() {
    state.originalOfferEditDialog = $("#original-offer-edit-dialog").dialog({
        autoOpen: false,
        height: "auto",
        width: '55vw',
        modal: true,
        maxHeight: $(window).height(),
        draggable: false,
        buttons: [
            {
                text: 'Save',
                class: 'btn btn-primary fw-normal',
                html: `<i class="fa-solid fa-check"></i>${window.localizedStrings.save}`,
                click: function () {
                    $(this).dialog("close");
                }
            },
            {
                text: 'Cancel',
                class: 'btn btn-primary fw-normal',
                html: `<i class="fa-solid fa-ban"></i>${window.localizedStrings.cancel}`,
                click: function () {
                    $(this).dialog("close");
                }
            }
        ],
        open: function (event, ui) {
            $(this).removeClass('d-none');
            $('.ui-dialog-titlebar-close').css('display', 'none');
        },
        close: function (event, ui) {
            $(this).dialog("close");
        },
    });
}

async function initFilter() {
    const filter = new TableFilterComponent('#original-offer-edit-filter', 'Edit Original Offer', {
        inputConfigs: ORIGINAL_OFFER_EDIT_FILTER_INPUTS,
        showButtons: false,
        wrapperClass: 'bg-none m-0 p-0'
    });

    await filter.init();
}

function onSelectRowData(data, index) {
    console.log(index);
    loadDataToLineDetails(data, index);
    setShowLineDetails(true);
}

function setShowLineDetails(isShow) {
    $('#order-quotes-details-line-details-wrapper').toggleClass('d-none', !isShow);
}

function loadDataToLineDetails(data, index) {
    $('#oqdLineDetail').text(`Line ${index + 1} of ${state.linesTable.data().length}`);
    // Pls handle for showing Data
}

function onToggleShowLineDetails() {
    const $container = $('#order-quotes-details-line-details');
    const $btnIcon = $('#lines-details-toggle-btn img');
    const isVisible = !$container.hasClass('d-none');

    if (isVisible) {
        $container.addClass('d-none');
        $btnIcon.attr('src', '/img/icons/plus.svg');
        $btnIcon.attr('alt', 'Add icon');
    } else {
        $container.removeClass('d-none');
        $btnIcon.attr('src', '/img/icons/minus.svg');
        $btnIcon.attr('alt', 'Remove icon');
    }
}

function onClickTabs(currentTabId) {
    setShowLineDetails(false);
    state.linesTable.rows({ selected: true }).deselect();
}
async function getQuoteMainInfoAndBinding() {
    let response = await GlobalTrader.ApiClient.getAsync(
        `orders/quotes/${quoteMainInfoStateValue.id}/main-info`
    )
    const data = response.data;
    state.quoteInfo = response.data;
    updateQuoteMainInformation(data);
}
function updateQuoteMainInformation(data) {
    const boxSelector = '#quote-main-information-box #quote-main-information-content';
    const setText = (name, value, allowHtml = false) => {
        const sanitized = GlobalTrader.StringHelper.setCleanTextValue(value);

        const $el = $(`${boxSelector} span[name="${name}"]`);
        if (allowHtml) $el.html(sanitized);
        else $el.text(sanitized);
    };
    const setCheckbox = (name, checked) => {
        $(`${boxSelector} input[name="${name}"]`).prop('checked', !!checked);
    };
    const setVisibility = (name, show) => {
        const $el = $(`${boxSelector} input[name="${name}"]`);
        if (show) {
            $el.removeClass('d-none').addClass('d-flex');
        }
        else {
            $el.addClass('d-none').removeClass('d-flex');
        }
    };


    const fields = {
        customer: state.quoteInfo.companyName,
        buyer: state.quoteInfo.contactName,
        salesperson: state.quoteInfo.salesmanName,
        'division-sales': state.quoteInfo.divisionName,
        'division-header': state.quoteInfo.divisionHeaderName,
        'date-quoted': state.quoteInfo.dateQuotedStr,
        terms: state.quoteInfo.termsName,
        incoterms: state.quoteInfo.incotermName,
        'support-team-member-to-update': state.quoteInfo.supportTeamMemberName,
        currency: state.quoteInfo.currencyName,
        'estimated-freight': state.quoteInfo.freightStr,
        'notes-to-customer': state.quoteInfo.notes,
        'internal-notes': state.quoteInfo.instructions,
        'purchasing-notes': state.quoteInfo.purchasingNotes,
        status: state.quoteInfo.quoteStatusName,
        'inhouse-AS6081-part-included': state.quoteInfo.aS6081,
    };
    for (const [name, value] of Object.entries(fields)) {
        setText(name, value);
    }

    setCheckbox("approved-customer", state.quoteInfo.companySOApproved);
    setCheckbox("source-of-supply-required", state.quoteInfo.aS9120);
    setCheckbox("mark-as-important", state.quoteInfo.isImportant);

    let $customerElement = $('#customer-link');
    $customerElement.attr('href', `/Contact/AllCompanies/Details?cm=${state.quoteInfo.companyNo}`);

    $('#customer-onstop-icon').html(
        state.quoteInfo.companyOnStop ? ButtonHelper.createOnStopIcon() : ""
    );
    $('#customer-advisory-icon').html(
        state.quoteInfo.companyAdvisoryNotes?.length > 0
            ? ButtonHelper.createAdvisoryNotesIcon(state.quoteInfo.companyAdvisoryNotes)
            : ""
    );

    let $buyerElement = $('#buyer-link');
    $buyerElement.attr('href', `/Contact/Contacts/Details?con=${state.quoteInfo.contactNo}`);
    $buyerElement.html(`<span>${state.quoteInfo.contactName || ''}</span>`)

    if (state.quoteInfo.uploadedBy) {
        setVisibility("is-from-pr-offer", true);
        setCheckbox("is-from-pr-offer", state.quoteInfo.isFromProspectiveOffer);
        $(`${boxSelector} span[name="uploaded-by"]`).show().text(state.quoteInfo.uploadedBy);
    }
    const isAs6081 = state.quoteInfo.aS6081 === true;

    $(`${boxSelector} span[name="inhouse-AS6081-part-included"]`).text(isAs6081 ? window.localizedStrings.yes : window.localizedStrings.no);
    const as6081ParentSpan = $(`${boxSelector} span[name="inhouse-AS6081-part-included"]`);
    as6081ParentSpan.empty();

    if (isAs6081) {
        as6081ParentSpan.append(`<span class="highlighted-as6081">${window.localizedStrings.yes}</span>`);
        as6081ParentSpan.find('.highlighted-as6081').css('background-color', 'yellow');
    } else {
        as6081ParentSpan.append(`<span class="highlighted-as6081">${window.localizedStrings.no}</span>`);
        as6081ParentSpan.find('.highlighted-as6081').css('background-color', '');
    }


    if (state.quoteInfo.purchasingNotes) {
        const parentSpan = $(`${boxSelector} span[name="purchasing-notes"]`);

        parentSpan.empty();

        parentSpan.append(`<span class="highlighted-notes">${state.quoteInfo.purchasingNotes}</span>`);

        parentSpan.find('.highlighted-notes').css('background-color', 'yellow');
    }

    if (state.quoteInfo.aS9120 === true && state.quoteLinesResults.allLineContainSource === false) {
        $('#quote-main-info-warning-section').removeClass('d-none');
    } else {
        $('#quote-main-info-warning-section').addClass('d-none');
    }

    // Last updated
    $(`${boxSelector} i[name="last-updated"]`).text(`${localizedTitles.lastUpdated}: ${state.quoteInfo.lastUpdated}`);
}
async function loadQuoteMainInfoAsync(isRefreshed = false) {

    state.quoteMainSectionBox.loading(true);
    let response = await GlobalTrader.ApiClient.getAsync(
        `orders/quotes/${quoteMainInfoStateValue.id}/main-info`
    );
    const data = response.data;
    state.quoteInfo = response.data;
    updateQuoteMainInformation(data);
    state.quoteMainSectionBox.stopLoading(false);
}

async function registerTabChangeEvent(){
    $('button[data-bs-toggle="tab"]').on('shown.bs.tab', async function (e) {
        const tabId = Number($(e.target).data('tabid'));
        const url = quoteLineUrls[tabId];      
        if (url) {
            await loadQuoteLineResultsAsync(true, url);
            state.quoteLineUrl = url;
        }
    });
}

