﻿using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrderQuote.Command;

namespace GlobalTrader2.Contacts.UseCases.Companies.Commands
{
    public class UpdatePurchaseRequestLineDetailHandler : IRequestHandler<UpdatePurchaseRequestLineDetailCommand, BaseResponse<int>>
    {
        private readonly IBaseRepository<object> _objectRepository;
        public UpdatePurchaseRequestLineDetailHandler(IBaseRepository<object> objectRepository)
        {
            _objectRepository = objectRepository;
        }

        public async Task<BaseResponse<int>> Handle(UpdatePurchaseRequestLineDetailCommand request, CancellationToken cancellationToken)
        {
            var output = new SqlParameter("@RowsAffected", SqlDbType.Int) { Direction = ParameterDirection.Output };
            var parameters = new List<SqlParameter>
            {
                    new SqlParameter("@PurchaseRequestLineDetailId", SqlDbType.Int) { Value = request.PurchaseRequestLineDetailId },
                    new SqlParameter("@PurchaseRequestLineNo", SqlDbType.Int) { Value = request.PurchaseRequestLineNo ?? (object)DBNull.Value },
                    new SqlParameter("@CompanyNo", SqlDbType.Int) { Value = request.CompanyNo },
                    new SqlParameter("@Price", SqlDbType.Float) { Value = request.Price ?? 0 },
                    new SqlParameter("@SPQ", SqlDbType.NVarChar) { Value = request.SPQ ?? (object)DBNull.Value },
                    new SqlParameter("@LeadTime", SqlDbType.NVarChar) { Value = request.LeadTime ?? (object)DBNull.Value },
                    new SqlParameter("@ROHS", SqlDbType.NVarChar) { Value = request.ROHSStatus ?? (object)DBNull.Value },
                    new SqlParameter("@FactorySealed", SqlDbType.NVarChar) { Value = request.FactorySealed ?? (object)DBNull.Value },
                    new SqlParameter("@MSL", SqlDbType.NVarChar) { Value = ""},
                    new SqlParameter("@ManufacturerName", SqlDbType.NVarChar) { Value = request.ManufacturerName ?? (object)DBNull.Value },
                    new SqlParameter("@DateCode", SqlDbType.NVarChar) { Value = request.DateCode ?? (object)DBNull.Value },
                    new SqlParameter("@PackageType", SqlDbType.NVarChar) { Value = request.PackageType ?? (object)DBNull.Value },
                    new SqlParameter("@ProductType", SqlDbType.NVarChar) { Value = request.ProductType ?? (object)DBNull.Value },
                    new SqlParameter("@MOQ", SqlDbType.NVarChar) { Value = request.MOQ ?? (object)DBNull.Value },
                    new SqlParameter("@TotalQSA", SqlDbType.NVarChar) { Value = request.TotalQSA ?? (object)DBNull.Value },
                    new SqlParameter("@LTB", SqlDbType.NVarChar) { Value = request.LTB ?? (object)DBNull.Value },
                    new SqlParameter("@Notes", SqlDbType.NVarChar) { Value = request.Notes ?? (object)DBNull.Value },
                    new SqlParameter("@UpdatedBy", SqlDbType.Int) { Value = request.UpdatedBy ?? (object)DBNull.Value },
                    new SqlParameter("@CurrencyNo", SqlDbType.Int) { Value = request.CurrencyNo},
                    new SqlParameter("@MSLLevelNo", SqlDbType.Int) { Value = request.MSLLevelNo ?? (object)DBNull.Value },
                    output
                };

            await _objectRepository.ExecuteSqlRawAsync(
                $"{StoredProcedures.Update_PurchaseRequestLineDetail} @PurchaseRequestLineDetailId,@PurchaseRequestLineNo, @CompanyNo, @Price, @SPQ, @LeadTime, @ROHS, @FactorySealed, " +
                $"@MSL, @ManufacturerName, @DateCode, @PackageType, @ProductType, @MOQ, @TotalQSA, @LTB, @Notes, @UpdatedBy, @CurrencyNo, @MSLLevelNo,@RowsAffected OUTPUT",
                parameters.ToArray()
            );

            var result = output.Value == null ? 0 : (int)output.Value;

            return new BaseResponse<int>()
            {
                Success = result > 0,
                Data = result,
            };
        }
    }
}
