import { LiteFormDialog } from "../../../components/base/lite-form-dialog.js?v=#{BuildVersion}#"
import { CompanySupplierSearchSelectComponent } from '../hubrfq/components/search-selects/company-supplier-search-select.component.js?v=#{BuildVersion}#';

export class AddEditPriceRequestDialog extends LiteFormDialog {
    constructor(selector, options) {
        const defaultOptions = {
            bindScope: true,
            width: "600px",
            maxHeight: $(window).height(),
            closeWhenSuccess: true,
            url: function () {
                if (this.isAddForm) {
                    return "/api/orders/purchase-quote/create-purchase-request-line";
                } else {
                    return "/api/orders/purchase-quote/update-purchase-request-line";
                }
            },
            method: function () {
                if (this.isAddForm) {
                    return "POST";
                } else {
                    return "PUT";
                }
            },
            buttons: [
                {
                    name: "save",
                    icon: "check",
                    alt: "save",
                    display: localizedTitles.save || "Save",
                },
                {
                    name: "cancel",
                    icon: "xmark",
                    alt: "cancel",
                    display: localizedTitles.cancel || "Cancel",
                },
            ],
            validationRules: {
                SupplierNo: {
                    required: true,
                },
                CurrencyNo: {
                    required: true,
                },
                price: {
                    required: true,
                },
            },
            validationMessages: {
                CurrencyNo: window.localizedStrings.requiredField,
                SupplierNo: window.localizedStrings.requiredField,
                price: window.localizedStrings.requiredField,
            },
            errorPlacement: function (error, element) {
                error.insertAfter($(element).closest("div"));
            },
            body: () => {
                const bodyRequest = this.buildBodyRequest();
                return bodyRequest;
            },
        };

        super(selector, defaultOptions);
        this.dataModel = {};
        this.apiEndpoints = {
            currency: "/lists/buy-currency-by-global-no",
            company: "contact/manufacturers/suppliers/search-company",
            mls:"lists/msls"
        };
    }

    init() {
        this.initSearchSelects();
        this.initDropdown();
        this.registerEvents();
    }
    close() {
        super.close();
        this.priceRequestSupplierSearchSelect.resetSearchSelect();
    }

    initDropdown() {
        this.mslDropdown = $("#add-edit-price-request-form #msl-dropdown").dropdown({
            deferLoad: true,
            serverside: false,
            valueKey: "id",
            endpoint: this.apiEndpoints.mls,
            isCacheApplied: false
        });

        this.currencyDropDown = $("#add-edit-price-request-form #currency-dropdown").dropdown({
            deferLoad: true,
            serverside: false,
            endpoint: this.apiEndpoints.currency,
            valueKey: 'currencyId',
            textKey: 'name',
            placeholderValue: "",
        });
    }

    async _reloadDropdownAsync() {
        const currencyPromise = this.currencyDropDown.dropdown("reload", this.apiEndpoints.currency, { globalNo: this.dataModel?.globalCurrencyNo ?? null, buy: true });
        const mlsPromise = this.mslDropdown.dropdown("reload", this.apiEndpoints.mls, { refreshData: true });

        await Promise.all([
            currencyPromise,
            mlsPromise,
        ]);
    }

    initSearchSelects() {
        this.priceRequestSupplierSearchSelect = new CompanySupplierSearchSelectComponent('price-request-supplier-auto-search', 'PriceRequestSupplierId', 'single', 'keyword', this.apiEndpoints.company );
    }

    registerEvents() {
        this.priceRequestSupplierSearchSelect.on('companySupplierSelected', async (data) => {
            await this.currencyDropDown.dropdown("reload", this.apiEndpoints.contact, { globalNo: data.globalCurrencyNo , buy: true });
            const dataform = {
                CurrencyNo: data.poCurrencyNo,
                PocurrencyCode: data.poCurrencyCode,
            };

            super.populateForm(dataform);
        });

        this.currencyDropDown.on('change', (e, selectedData) => {
            const extraData = this.currencyDropDown.dropdown('getSelectedExtraData');
            super.populateForm({ PocurrencyCode: extraData?.code ?? ""});

        });
    }

    showForm(part, isAddForm, data, purchaseRequestLineId) {
        this.dataModel = data;
        this.isAddForm = isAddForm;
        this.purchaseRequestLineId = purchaseRequestLineId;
        this._reloadDropdownAsync();
        $("#add-edit-price-request-form #MPNQuotedPriceRequest").text(part);
        $("#add-edit-price-request-form #msl-dropdown").closest('div').addClass('col-9');
        $("#add-edit-price-request-dialog").removeClass("d-none");
        if (isAddForm) {
            $("#add-edit-price-request-dialog label[for='MPNQuotedPriceRequest']").text(localizedTitles.mpnQuote)

            $("#add-edit-price-request-dialog").dialog("option", "title", localizedTitles.addDialog);
            $("#add-edit-price-request-title").text(localizedTitles.addTitle);
            $("#add-edit-price-msl-label").html('');

        } else {
            this.priceRequestSupplierSearchSelect.selectItem({
                label: data.companyName,
                value: data.companyNo,
                skip: true
            })
            $("#add-edit-price-request-dialog label[for='MPNQuotedPriceRequest']").text(localizedTitles.part)

            $("#add-edit-price-request-dialog").dialog("option", "title", localizedTitles.editDialog);
            $("#add-edit-price-request-title").text(localizedTitles.editTitle);
            if (this.dataModel.msl?.length > 0 && this.dataModel.mslLevelNo <= 0) {
                $("#add-edit-price-msl-label").html(`( ${this.dataModel.msl} )`);
            }
        }

        this.open(data ? this.prepareModelBinding(data) : null);
    }

    buildBodyRequest() {
        let bodyRequest = this.prepareModelRequest(super.getFormData());

        if (!this.isAddForm) {
            bodyRequest.PurchaseRequestLineDetailId = this.dataModel.purchaseRequestLineDetailId
        }
        return bodyRequest;
    }

    prepareModelBinding(data) {
        return {
            CurrencyNo: data.currencyNo,
            price: data.price,
            PocurrencyCode: data.currencyCode,
            ManufacturerName: data.manufacturerName,
            DateCode: data.dateCode,
            PackageType: data.packageType,
            ProductType: data.productType,
            StandardPackQuantity: data.spq,
            MinimumOrderQuantity: data.moq,
            LeadTimeWeeks: data.leadTime,
            RohsCompliant: data.roHSStatus,
            TotalQuantityAvailable: data.totalQSA,
            LastTimeBuyLTB: data.ltb,
            Notes: data.notes,
            FactorySealed: data.factorySealed,
            MslLevelNo: data.mslLevelNo
        };
    }

    prepareModelRequest(data) {
        return {
            purchaseRequestLineNo: this.purchaseRequestLineId,
            companyNo: data.SupplierNo,
            price: data.price,
            spq: data.StandardPackQuantity,
            leadTime: data.LeadTimeWeeks,
            rohsStatus: data.RohsCompliant,
            factorySealed: data.FactorySealed,
            manufacturerName: data.ManufacturerName,
            dateCode: data.DateCode,
            packageType: data.PackageType,
            productType: data.ProductType,
            moq: data.MinimumOrderQuantity,
            totalQSA: data.TotalQuantityAvailable,
            ltb: data.LastTimeBuyLTB,
            notes: data.Notes,
            currencyNo: data.CurrencyNo,
            mslLevelNo: this.mslDropdown.dropdown("selectedValue")
        };
    }
}