@using Microsoft.Extensions.Localization
@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.HUBRFQ.Details
@using GlobalTrader2.SharedUI.Helper

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IViewLocalizer _localizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer

@model IndexModel

@{
    var sectionBoxClasses = HtmlHelperExtensions.GetSectionBoxClass();
    var headerClasses = HtmlHelperExtensions.GetHeaderClass();
    var contentClasses = HtmlHelperExtensions.GetContentClass();
    var accordionHeaderIconClass = HtmlHelperExtensions.GetAccordionHeaderIconClass();
}

<div id="release-requirement-dialog" class="dialog-container" title="@_localizer["ReleaseRequirementTitle"]" style="display: none;">
    <div class="dialog-description">
        <span>@_localizer["Message"]<strong>@_localizer["HUBRFQ"]</strong>?</span>
    </div>
    <form id="release-requirement-form" class="row common-form mt-2">
        <input type="hidden" name="customerRequirementId" />
        <input type="hidden" name="bomId" value="@Model.BomId" />
        <input type="hidden" name="bomCode" />
        <input type="hidden" name="bomName" />
        <input type="hidden" name="bomCompanyName" />
        <input type="hidden" name="bomCompanyNo" />
        <input type="hidden" name="salesManNo" />
        <input type="hidden" name="custReqNo" />
        <input type="hidden" name="reqSalesPerson" />
        <input type="hidden" name="supportTeamMemberNo" />
        
        <div id="release-req-sourcing-box" class="@sectionBoxClasses">
            <h3 class="@headerClasses">
                <span class="@accordionHeaderIconClass"></span>
                <span class="section-box-title">@_localizer["SourcingResults"]</span>
            </h3>

            <div id="release-req-sourcing-content" class="row @contentClasses">
                <table id="release-req-sourcing-table" class="table simple-table display responsive">
                    <tr>
                        <th></th>
                    </tr>
                </table>
            </div>
        </div>
    </form>
</div>

<script>
    const releaseRequirementLocalized = {
        supplier: "@_localizer["Supplier"]",
        partNo: "@_localizer["PartNo"]",
        buyPrice: "@_localizer["BuyPrice"]",
        unitSellPrice: "@_localizer["UnitSellPrice"]",
        warningMessage: "@_localizer["WarningMessage"]",
        noData: "@_localizer["NoData"]",
        release: "@_commonLocalizer["Release"]",
        cancel: "@_commonLocalizer["Cancel"]",
        buyPriceGreaterText: "@_localizer["BuyPriceGreaterText"]",
        ofSelectedPartGreaterThan: "@_localizer["OfSelectedPartGreaterThan"]",
        buyPriceEqualText: "@_localizer["BuyPriceEqualText"]",
        ofSelectedPartEqualTo: "@_localizer["OfSelectedPartEqualTo"]"
    };
</script>
