import { SystemDocument } from "../../../../../../config/system-document-enum.js?v=#{BuildVersion}#";
import { ButtonHelper } from "../../../../../../helper/button-helper.js?v=#{BuildVersion}#";
import { CollapsibleBoxHelper } from "../../../../../../helper/collapsible-box-helper.js?v=#{BuildVersion}#";
import { ROHSHelper } from "../../../../../../helper/rohs-helper.js?v=#{BuildVersion}#";

export class POLineAllocationsManager {
    constructor(purchaseOrderLineId, isPOHub = false) {
        this.purchaseOrderLineId = purchaseOrderLineId;
        this.isPOHub = isPOHub;
        this._apiEndpoint = `/api/orders/purchase-orders/po-lines/${this.purchaseOrderLineId}/allocations`;
        this._loadedData = [];
        this._selectedLines = [];
        this.$table = null;
        this.$contentContainer = $('#allocations-container');
        this.$refreshButton = this.$contentContainer.find('.collapsible-box-refresh-button');
    }

    initialize() {
        if (!this.$table) {
            this._initTable();
            this._setUpEventListeners();
        }
    }

    async reload(newPurchaseOrderLineId) {
        CollapsibleBoxHelper.setCollapsibleBoxLoading(this.$contentContainer, true);
        CollapsibleBoxHelper.updateRowsCount(this.$contentContainer, 0);
        if (newPurchaseOrderLineId && this.purchaseOrderLineId !== newPurchaseOrderLineId) {
            this.salesOrderLineId = newPurchaseOrderLineId;
            this._apiEndpoint = `/api/orders/purchase-orders/po-lines/${this.purchaseOrderLineId}/allocations`;
            this.$table.ajax.url(this._apiEndpoint);
        }
        await this.$table.ajax.reload();
    }

    toggleVisibility(isVisible = true) {
        this.$contentContainer.toggle(isVisible);
    }

    _renderStackedTitle(topLabel, bottomLabel) {
        return `<span class="dt-column-title d-flex flex-column gap-1">
            <div>${topLabel}</div>
            <div class="dt-column-line"></div>
            <div>${bottomLabel}</div>
            <div></div>
        </span>`;
    }

    _formatPartCell(row) {
        let partDisplay;
        if (row.isPoHub) {
            partDisplay = GlobalTrader.StringHelper.setCleanTextValue(row.part);
        }
        else {
            partDisplay = GlobalTrader.HtmlHelper.createHyperLinkHtml({
                url: GlobalTrader.PageUrlHelper.Get_URL_Stock(row.stockNo),
                title: ROHSHelper.writePartNo(row.part, row.rohs)
            });
        }

        const customerPartDisplay = GlobalTrader.StringHelper.setCleanTextValue(row.customerPart) || '&nbsp;';

        return `
            <div>${partDisplay || '&nbsp;'}</div>
            <div>${customerPartDisplay}</div>
        `;
    }


    _formatCustomerCell(row) {
        let customerDisplay;
        if (row.isPoHub) {
            customerDisplay = GlobalTrader.StringHelper.setCleanTextValue(row.customer);
        }
        else if (row.customer) {
            customerDisplay = ButtonHelper.nubButton_Company(
                row.customerNo,
                row.customer,
                row.customerAdvisoryNotes
            );
        }
        else {
            customerDisplay = '&nbsp;';
        }

        const customerPODisplay = GlobalTrader.StringHelper.setCleanTextValue(row.customerPO) || '&nbsp;';

        return `
            <div>${customerDisplay}</div>
            <div>${customerPODisplay}</div>
        `;
    }


    _formatSalesOrderCell(row) {
        let soDisplay;
        if (row.isPoHub) {
            soDisplay = GlobalTrader.StringHelper.setCleanTextValue(row.salesOrderNumber);
        }
        else if (row.salesOrderNumber) {
            soDisplay = ButtonHelper.nubButton_SalesOrderLine(
                row.salesOrderNo,
                row.salesOrderNumber,
                row.salesOrderLineNo
            );
        }
        else {
            soDisplay = '&nbsp;';
        }

        const lineNoSuffix = (row.lineNo && row.lineNo !== '0')
            ? ` (${row.lineNo})`
            : '';

        const promisedDate = row.shipASAP
            ? `${row.formattedDatePromised} (or ASAP)`
            : row.formattedDatePromised || '&nbsp;';

        return `
            <div>${soDisplay}${lineNoSuffix}</div>
            <div>${promisedDate}</div>
        `;
    }


    _formatSRMACell(row) {
        let srmaDisplay;
        if (row.isPoHub) {
            srmaDisplay = GlobalTrader.StringHelper.setCleanTextValue(row.srmaNumber);
        }
        else if (row.srmaNumber) {
            srmaDisplay = ButtonHelper.nubButton_SystemDocument(
                SystemDocument.SupplierRMA,
                row.srmaNo,
                row.srmaNumber
            );
        }
        else {
            srmaDisplay = '&nbsp;';
        }

        const returnDateDisplay = row.formattedReturnDate || '&nbsp;';

        return `
            <div>${srmaDisplay}</div>
            <div>${returnDateDisplay}</div>
        `;
    }

    _getBaseColumns() {
        return [
            {
                data: null,
                className: 'text-wrap text-break',
                title: this._renderStackedTitle(allocationsLocalizedStrings.partNo, allocationsLocalizedStrings.customerPart),
                render: (data, type, row) => this._formatPartCell(row)
            },
            {
                data: null,
                className: 'text-wrap text-break',
                title: this._renderStackedTitle(allocationsLocalizedStrings.customer, allocationsLocalizedStrings.customerPo),
                render: (data, type, row) => this._formatCustomerCell(row)
            },
            {
                data: null,
                className: 'text-wrap text-break',
                title: this._renderStackedTitle(`${allocationsLocalizedStrings.so} (${allocationsLocalizedStrings.lineNo})`, allocationsLocalizedStrings.promised),
                render: (data, type, row) => this._formatSalesOrderCell(row)
            },
            {
                data: null,
                className: 'text-wrap text-break',
                title: this._renderStackedTitle(allocationsLocalizedStrings.srma, allocationsLocalizedStrings.returnDate),
                render: (data, type, row) => this._formatSRMACell(row)
            },
            {
                data: null,
                className: 'text-wrap text-break',
                title: this._renderStackedTitle(allocationsLocalizedStrings.quantity, allocationsLocalizedStrings.price),
                render: function (data, type, row) {
                    return `
                        <div>${row.quantity || '&nbsp;'}</div>
                        <div>${row.formattedPrice || '&nbsp;'}</div>
                    `;
                }
            },
            {
                data: null,
                className: 'text-wrap text-break',
                title: this._renderStackedTitle(allocationsLocalizedStrings.dc, allocationsLocalizedStrings.deliveryDate),
                render: function (data, type, row) {
                    return `
                        <div>${row.dateCode || '&nbsp;'}</div>
                        <div>${row.formattedDeliveryDate || '&nbsp;'}</div>
                    `
                }
            },
            {
                data: null,
                className: 'text-wrap text-break',
                title: this._renderStackedTitle(`PO > SO ${allocationsLocalizedStrings.margin} (%)`, `PO > SO ${allocationsLocalizedStrings.marginValue}`),
                render: function (data, type, row) {
                    return `
                        <div>${row.margin || '&nbsp;'}</div>
                        <div>${row.marginValue || '&nbsp;'}</div>
                    `
                }
            }
        ]
    }

    _getPOHubColumns() {
        return [
            {
                data: null,
                className: 'text-wrap text-break',
                title: this._renderStackedTitle(allocationsLocalizedStrings.priceToClient, `HUB > ${allocationsLocalizedStrings.profit} (%)`),
                render: function (data, type, row) {
                    return `
                        <div>${row.priceToClient || '&nbsp;'}</div>
                        <div>${row.profit || '&nbsp;'}</div>
                    `
                }
            },
            {
                data: null,
                className: 'text-wrap text-break',
                title: this._renderStackedTitle(allocationsLocalizedStrings.sellExchangeRate, allocationsLocalizedStrings.exchangeRate),
                render: function (data, type, row) {
                    return `
                        <div>${row.sellExchangeRate || '&nbsp;'}</div>
                        <div>${row.exchangeRate || '&nbsp;'}</div>
                    `
                }
            }
        ]
    }

    _getColumns() {
        const baseColumns = this._getBaseColumns();
        if (this.isPOHub) {
            const insertIndex = baseColumns.length - 2;
            const pohubColumns = this._getPOHubColumns();
            return [
                ...baseColumns.slice(0, insertIndex),
                ...pohubColumns,
                ...baseColumns.slice(insertIndex)
            ];
        }
        return baseColumns;
    }

    _initTable() {
        CollapsibleBoxHelper.setCollapsibleBoxLoading(this.$contentContainer, true);

        this.$table = $('#allocations-table').DataTable({
            ajax: {
                url: this._apiEndpoint,
                type: 'GET',
                dataSrc: (json) => {
                    this._loadedData = json.data || [];
                    return json.data;
                }
            },

            columns: this._getColumns(),
            responsive: true,
            ordering: false,
            searching: false,
            paging: false,
            info: false,
            autoWidth: false,
            select: {
                toggleable: true,
                info: false,
                style: 'os',
            },
            language: {
                emptyTable: `<i>${allocationsLocalizedStrings.noDataMessage}</i>`,
                zeroRecords: `<i>${allocationsLocalizedStrings.noDataMessage}</i>`,
            },

            drawCallback: () => {
                CollapsibleBoxHelper.updateRowsCount(this.$contentContainer, this._loadedData.length);
                CollapsibleBoxHelper.setCollapsibleBoxLoading(this.$contentContainer, false);
            }
        }).on('select', (e, dt, type, indexes) => {
            if (type !== 'row') return;

            dt.rows(indexes).data().toArray().every((line) => {
                this._selectedLines.push(line);
            })
        }).on('deselect', (e, dt, type, indexes) => {
            if (type !== 'row') return;

            dt.rows(indexes).data().toArray().every((line) => {
                this._selectedLines = this._selectedLines.filter(r => r.id !== line.id);
            })
        });
    }

    _setUpEventListeners() {
        this.$refreshButton.off('click.allocations').on('click.allocations', async () => {
            this.reload();
        });
    }
}