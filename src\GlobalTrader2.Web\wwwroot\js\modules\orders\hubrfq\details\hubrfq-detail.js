import { MediaDocumentManager } from "../../../../../js/components/documents-list/media-document-list.js?v=#{BuildVersion}#";
import { SourcingResults } from "../../../..//modules/orders/sourcing/containers/sourcing-results.js?v=#{BuildVersion}#";
import { LiteDatatable } from "../../../../components/base/lite-datatable.component.js?v=#{BuildVersion}#";
import { LiteFormDialog } from "../../../../components/base/lite-form-dialog.js?v=#{BuildVersion}#";
import { SectionBox } from "../../../../components/base/section-box.component.js?v=#{BuildVersion}#";
import { PartTableButton } from "../../../../components/button/part-table-button.js?v=#{BuildVersion}#";
import { FixedDialogComponent } from '../../../../components/dialogs/fixed-dialog/fixed-dialog.component.js?v=#{BuildVersion}#';
import { TableFilterComponent } from "../../../../components/table-filter/table-filter.component.js?v=#{BuildVersion}#";
import BindingService from "../../../../helper/binding.service.js?v=#{BuildVersion}#";
import { ButtonHelper } from "../../../../helper/button-helper.js?v=#{BuildVersion}#";
import { ROHSHelper } from "../../../../helper/rohs-helper.js?v=#{BuildVersion}#";
import { openPopup } from "../../../../helper/url-helper.js?v=#{BuildVersion}#";
import { ItemDetailComponent } from "../components/item-detail/item-detail.component.js?v=#{BuildVersion}#";
import { MailRecipientSearchSelectComponent } from "../components/mail-recipient-search-select/mail-recipient-search-select.component.js?v=#{BuildVersion}#";
import { ADD_HUBRFQ_ITEM_FILTER_INPUTS } from "../constants/add-hubrfq-item-filter.constant.js?v=#{BuildVersion}#";
import { EditHubrfqDialog } from "../edit-hubrfq-dialog.js?v=#{BuildVersion}#";
import { AddItemTable } from "./add-item-table.js?v=#{BuildVersion}#";
import { CloseHubRfqHandler } from "./close-hubrfq.js?v=#{BuildVersion}#";
import { KubAssistance } from "./kub-assistance.js?v=#{BuildVersion}#";
import { NoBidAllHandler } from "./nobid-all.js?v=#{BuildVersion}#";
import { ReleaseRequirementHandler } from "./release-requirement.js?v=#{BuildVersion}#";
import { NoBidHandler } from "./nobid.js?v=#{BuildVersion}#";
import { ReleaseAllHandler } from "./release-all.js?v=#{BuildVersion}#";
import { SendToPurchaseHubHandler } from "./send-to-purchase-hub.js?v=#{BuildVersion}#";

const hubrfqDetailsBox = $("#main-information-box");
const communicationNoteBox = $("#communication-note-box");
const bomQualityBox = $("#bom-quality-box");
const hubRfqItemBox = $("#hub-rfq-item-box");
const uploadedDocumentBox = $("#uploaded-documents-box");
const logBox = $("#log-box");
const assignmentHistoryBox = $("#assignment-history-box");

const isPoHub = window.isPoHub();

const listForPrint = {
    PrintHUBRFQ: 41,
    PrintLog: 48
}

let state = {
    sourcingResults: null,
    eccnLogButton: null,
    communicationTable: null,
    bomQualificationTable: null,
    hubRfqItemsTable: null,
    assignmentHistoryTable: null,
    sendToPurchaseHubBtn: null,
    releaseBtn: null,
    sendToPurchaseHubHandler: null,
    releaseAllHandler: null,
    closeHubRfqHandler: null,
    noBidAllHandler: null,
    noBidAllBtn: null,
    noBidBtn: null,
    noBidHandler: null,
    recallNoBidBtn: null,
    recallNoBidDialog: null,
    releaseRequirementBtn: null,
    releaseRequirementHandler: null,
    itemDetailComponent: null,
    mainInformationSectionBox: null,
    currentSelectedPartDetailItem: {
        id: null
    },
    kubAssistance: null,
    magicBoxDialogControl: null,
    BomImagesDocumentsManager: null,
    sourcingResultsDetail: null,
    hasSourcingResultFlag: null,
    addItemFilter: null,
    addItemTable: null,
    addItemSelectedItems: [],
    allHasDelDate: false,
    allHasProduct: false,
    canReleaseSourcingResults: false
};

let shouldUpdateTitle = false;

$(async () => {
    setUpSectionBox();
    if (!state.sourcingResultsSectionBox) {
        let sourcingResultSectionBox = new SourcingResults("customer-requrement-sourcing-results-box", "customer-requirement-sourcing-results-table-container", "customer-requirement-sourcing-results-table", "customer-requirement-sourcing-results-resize", {
            defaultCustomerRequirementId: state.currentSelectedPartDetailItem.id,
            api: "/orders/bom/part-detail/"
        }, isPoHub, false);
        state.sourcingResultsSectionBox = sourcingResultSectionBox;
        window.sourcingResultsSectionBox = sourcingResultSectionBox;
    }
    state.sourcingResultsSectionBox.init();

    GlobalTrader.SourcingHelper.setupSourcingBox();
    $("#sourcing-box").addClass("d-none");
    updateCustomerRequirementsTitle(0);

    await getDetailandBinding();

    state.kubAssistance = new KubAssistance(localizedKubAssistance);
    await state.kubAssistance.initKubAssistanceAsync();

    const tableInitPromises = [
        initCommunicationNotesTable().catch(err => {
            console.error("Communication table init failed:", err);
        }),
        initHubRfqItemsTable().catch(err => {
            console.error("HUBRFQ Items table init failed:", err);
        }),
        initBOMQualificationAnswersTable().catch(err => {
            console.error("BOM Qualification table init failed:", err);
        }),
        initAssignmentHistoryTable().catch(err => {
            console.error("Assignment History table init failed:", err);
        })
    ];

    await Promise.allSettled(tableInitPromises);

    initForm();
    _initMagicBoxDialog();


    await getDetailandBinding();

    await initAddItemFilter();
    setupAddItemEventListeners();

    // Initialize unrelease button visibility for GSA users
    initUnreleaseButtonVisibility();
    setUpMainInfoButtonVisible();

    registerEvents();
});

function notifyReleaseRequirementHandler(selectedRowData) {
        if (state.releaseRequirementHandler && selectedRowData) {
            state.releaseRequirementHandler.updateSelectedRowData(selectedRowData);
        }
}

async function loadBOMItemDetail(response) {
    window.sourcingResultsDetail = response;

    if (response?.data) {
        await state.kubAssistance.loadKubAssistantData(response.data);
        state.hasSourcingResultFlag = !response.data.sourcingResult;
        updateButtonStates();
        updateUnReleaseButton();
    }
}

function validateDeliveryDateAndProduct(hasDelDateAndProduct) {
    if (hasDelDateAndProduct) {
        showDeliveryDateProductValidationNotification(false);
        return true;
    } else {
        const message = localizedTitles.errorDelDateAndProduct;
        showDeliveryDateProductValidationNotification(true, message);
        return false;
    }
}

function clearSourcingResultsErrorMessages() {
    showDeliveryDateProductValidationNotification(false);
}
async function refreshLyticaApiData(partNumber, mfrCode, mfrNo, mfrName) {
    if (!partNumber || !mfrNo) {
        return;
    }

    const header = {};
    const data = {
        partNumber,
        mfrCode,
        mfrNo,
        mfrName
    };
    await GlobalTrader.ApiClient.postAsync(`/orders/customer-requirements/refresh-lytica-api-data`, data, header);
}

function createHubRfqDataTableDefaultColumns(name, ...title) {
    return {
        title: renderHubRfqTitle(...title),
        className: 'text-wrap text-break text-left header-custom',
        data: name,
        name: name,
    }
}

function renderHubRfqTitle(...title) {
    if (title.length < 1)
        return '';
    if (title.length == 1)
        return title[0];
    return GlobalTrader.StringHelper.stringFormat(`<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">{0}</div>{1}`, ...title);
}

function setUpSectionBox() {
    // Set up main information section box with onRefreshed.msb event pattern
    state.mainInformationSectionBox = new SectionBox("#main-information-box", {
        loading: false,
        loadingContentId: 'main-information-content'
    });

    state.mainInformationSectionBox.init();

    // Listen for the onRefreshed.msb custom event
    state.mainInformationSectionBox.on('onRefreshed.msb', async () => {
        hubrfqDetailsBox.section_box("option", { loading: true });
        try {
            await getDetailandBinding();
            updateButtonStates();
            if (shouldUpdateTitle) {
                $('#hubrfq-primary-title').text(state.data.name);
                shouldUpdateTitle = false;
            }

            state.hubRfqItemsTable.table.ajax.reload(function () {
                setTimeout(updateButtonStates, 0);
            });
        } finally {
            hubrfqDetailsBox.section_box("option", { loading: false });
        }
    });


    communicationNoteBox.section_box({
        loading: false,
        onRefreshClick: async () => {
            state.communicationTable.table.ajax.reload();
        },
    });


    bomQualityBox.section_box({
        loading: false,
        loadingContentId: 'bom-quality-content',
        onRefreshClick: async () => {
            bomQualityBox.section_box("option", { loading: true });
            await state.bomQualificationTable.reloadAsync();
            bomQualityBox.section_box("option", { loading: false });
        },
    });


    hubRfqItemBox.section_box({
        loading: false,
        onRefreshClick: async () => {
            $('#hubrfq-item-detail').addClass("d-none");
            await new Promise(resolve => {
                state.hubRfqItemsTable.table.ajax.reload(function () {
                    updateButtonStates();
                    resolve();
                });
            });
            await state.itemDetailComponent.reload();
            if (window.sourcingResultsSectionBox && state.currentSelectedPartDetailItem.id) {
                await window.sourcingResultsSectionBox.loadSourcingResultsAsync(state.currentSelectedPartDetailItem.id);
            }

            let autoSourcingTable = $('#auto-sourcing-table').DataTable();
            if (autoSourcingTable) {
                handleRefreshDataTable($('#auto-sourcing-table'));
            }
        },
    });


    uploadedDocumentBox.section_box({
        loading: false,
        onRefreshClick: () => { },
    });


    logBox.section_box({
        loading: false,
        onRefreshClick: () => { },
    });


    assignmentHistoryBox.section_box({
        loading: false,
        onRefreshClick: () => {
            if (state.assignmentHistoryTable) {
                assignmentHistoryBox.section_box("option", { loading: true });
                state.assignmentHistoryTable.reloadAsync();
            } else {
                initAssignmentHistoryTable();
            }
        },
    });
}

async function getDetailandBinding() {
    let response = await GlobalTrader.ApiClient.getAsync(
        `/orders/bom/${stateValue.id}`
    );
    const data = response.data;
    state.bomName = data.name;
    state.currencyCode = data.currencyCode;
    state.companyID = data.companyNo;
    state.data = data;
    stateValue.data = data;
    // Bind data to UI
    updateMainInformation(data);
}

function updateMainInformation(data) {
    const boxSelector = '#main-information-box #main-information-content';

    const setText = (name, value, allowHtml = false) => {
        const sanitized = GlobalTrader.StringHelper.setCleanTextValue(value);
        const $el = $(`${boxSelector} span[name="${name}"]`);
        if (allowHtml) $el.html(sanitized);
        else $el.text(sanitized);
    };

    const setCheckbox = (name, checked) => {
        $(`${boxSelector} input[name="${name}"]`).prop('checked', !!checked);
    };

    const setVisibility = (name, show) => {
        const $el = $(`${boxSelector} input[name="${name}"]`);
        if (show) $el.removeClass('d-none').addClass('d-flex');
        else $el.addClass('d-none').removeClass('d-flex');
    };

    const fields = {
        code: data.code,
        name: data.name,
        "quote-required": data.quoteRequired,
        "requested-by": data.requestedby,
        "cc-communication-notes-to": data.contact2Name,
        currency: data.currencyCode,
        contact: data.contactName,
        "current-supplier": data.currentSupplier,
        "released-by": data.releasedby,
        "assign-to": data.assignedUser,
        notes: data.notes,
        "inhouse-as6081": data.aS6081,
        "purchasing-notes": data.purchasingNotes,
    };

    // Set text fields
    for (const [name, value] of Object.entries(fields)) {
        setText(name, value);
    }

    // Special case: company with embedded HTML
    const companyHtml = `${GlobalTrader.StringHelper.setCleanTextValue(data.company)} <span>(${GlobalTrader.StringHelper.setCleanTextValue(data.companyType)})</span> ${ButtonHelper.createAdvisoryNotesIcon(data.companyAdvisoryNotes)}`;
    setText("company", companyHtml, true);

    // Checkbox values
    setCheckbox("source-of-supply", data.aS9120);
    setCheckbox("inactive", data.inActive);

    // Uploaded by (conditionally visible)
    if (data.uploadedBy) {
        setVisibility("is-from-pr-offer", true);
        setCheckbox("is-from-pr-offer", data.isFromProspectiveOffer);
        $(`${boxSelector} span[name="uploaded-by"]`).show().text(data.uploadedBy);
    }

    // Highlight fields if necessary
    if (data.aS6081 === 'Yes') {
        $(`${boxSelector} span[name="inhouse-as6081"]`).css('background-color', 'yellow');
    }

    if (data.purchasingNotes) {
        const parentSpan = $(`${boxSelector} span[name="purchasing-notes"]`);

        parentSpan.empty();

        parentSpan.append(`<span class="highlighted-notes">${data.purchasingNotes}</span>`);

        parentSpan.find('.highlighted-notes').css('background-color', 'yellow');
    }

    // Last updated
    $(`${boxSelector} i[name="last-updated"]`).text(`${localizedTitles.lastUpdated} ${data.lastUpdated}`);
}

async function registerEvents() {
    state.addBtn = new PartTableButton("#add-btn", async function () {
        updateAddItemDialogButtons();
        const receivedDateFromInput = state.addItemFilter.getInputElementByName("receivedDateFrom");
        receivedDateFromInput.setRequiredCheckbox(true);
        receivedDateFromInput.setValue(GlobalTrader.DatetimeHelper.oneWeekAgo());

        const receivedDateToInput = state.addItemFilter.getInputElementByName("receivedDateTo");
        receivedDateToInput.setRequiredCheckbox(true);
        receivedDateToInput.element.datepicker2("setToDay");

        if (!state.addItemTable) {
            setupAddItemHubrfqItemsTable();
        }

        await state.addItemTable.search(state.addItemFilter.getAllValue());

        state.addItemDialog.open();
        state.addItemTable.table.state.table.columns.adjust().draw();

        $("#add-hubrfq-item-dialog")
            .find(":input:visible:not([readonly]):first")
            .trigger("focus");
    })

    state.eccnLogButton = new PartTableButton("#view-tree-btn", () => {
        openPopup(
            ButtonHelper.URL_All_Document(stateValue.id, "BOM", state.bomName),
            "winTreeView",
            450
        );
    });

    state.closeBtn = new PartTableButton("#close-btn", function () {
        state.closeHubRfqHandler.form.open();
    })

    state.printHubrfq = new PartTableButton('#print-hubrfq', () => {
        openPopup(ButtonHelper.URL_Print(stateValue.id, listForPrint.PrintHUBRFQ), "winPrint", 1070)
    });

    state.printHubrfq = new PartTableButton('#print-log', () => {
        openPopup(ButtonHelper.URL_Print(stateValue.id, listForPrint.PrintLog, "PrintHUBRFQ"), "winPrint", 770)
    });

    state.notifyBtn = new PartTableButton("#notify-btn", function () {
        setTextForSendToSupplierDialog();
        if (!state.toAutoSearch) {
            state.toAutoSearch = new MailRecipientSearchSelectComponent('send-to-suplier-input-search', 'send-to-suplier-input', 'multiple', 'nameSearch', '/lists/search-for-po-approve-suppliers');
        }
        state.sendToSuplier.open();
    })

    state.bomQualificationEditBtn = new PartTableButton("#bom-qualification-edit-btn", async function () {
        await generateTemplateDiaLogBomQualification("#bom-qualification-form");
        window.applyInputDirectives(document.getElementById("bom-qualification-form"));

        // trigger to update character count
        $("#bom-qualification-form textarea[name='PPVAsnwerNote']").each(function () {
            $(this).trigger('input');
        });

        state.bomQualificationDialog.open();
    })

    state.bomQualificationViewBtn = new PartTableButton("#bom-qualification-view-btn", async function () {
        await generateTemplateDiaLogBomQualification("#bom-qualification-view-form", true);
        window.applyInputDirectives(document.getElementById("bom-qualification-view-form"));

        // trigger to update character count
        $("#bom-qualification-view-form textarea[name='PPVAsnwerNote']").each(function () {
            $(this).trigger('input');
        });

        state.bomQualificationDialogView.open();
    })

    state.bomQualificationDeleteBtn = new PartTableButton("#bom-qualification-delete-btn", async function () {
        state.bomQualificationDialogDelete.open();
    })

    state.deleteBomItemsBtn = new PartTableButton("#hub-rfq-delete-btn", function () {
        const selectedRowData = getSelectedRowData();
        if (!selectedRowData) {
            alert(localizedTitles.pleaseSelectRowToDelete);
            return;
        }
        state.bomItemDeleteDialog.open();
    });

    state.unReleaseBtn = new PartTableButton("#unrelease-btn", function () {
        const selectedRowData = getSelectedRowData();
        if (!selectedRowData) {
            alert(localizedTitles.pleaseSelectRowToUnrelease);
            return;
        }
        state.unReleaseConfirmDialog.open();
    });

    state.releaseRequirementBtn = new PartTableButton("#release-requirement-btn", async function () {
        const selectedRowData = getSelectedRowData();
        if (!selectedRowData) {
            showToast('warning', localizedTitles.noRowSelected);
            return;
        }
        
        clearSourcingResultsErrorMessages();
        
        const isValidSourcing = validateDeliveryDateAndProduct(state.allHasDelDate && state.allHasProduct);
        
        if (!isValidSourcing) {
            return;
        }
        
        if (state.sourcingResultsSectionBox && selectedRowData.customerRequirementId) {
            await state.sourcingResultsSectionBox.loadSourcingResultsAsync(selectedRowData.customerRequirementId);
        }
        
        state.releaseRequirementHandler.open();
    });

    state.exportCsvBtn = new PartTableButton('#export-csv-btn', async () => {
        const header = { "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() }
        const data = {
            id: stateValue.id,
            currencyCode: state.data.currency_Code
        }

        await GlobalTrader.ApiClient.postDownloadFileAsync(`/orders/bom/export-to-csv`, data, header)
    });

    state.bomMainInfomationEditBtn = new PartTableButton("#main-information-edit-btn", async function () {
        let formData = structuredClone(state.data);

        const companyName = GlobalTrader.StringHelper.setCleanTextValue(formData.company);
        const companyType = GlobalTrader.StringHelper.setCleanTextValue(formData.companyType);
        const companyLbl = companyType
            ? `${companyName} (${companyType})`
            : companyName;

        const data = {
            txtName: getDisplayBomName(formData.name, formData.clientNo),
            companyLbl: companyLbl,
            contact: formData.contactNo,
            currency: formData.currencyNo,
            txtCurrentSupplier: formData.currentSupplier,
            txtQuoteRequired: formData.quoteRequired,
            chkAS9120: formData.aS9120,
            chkInActive: formData.inActive,
            txtNotes: formData.notes,
            salesperson: formData.contact2Id
        };
        state.addEditHubrfqForm.openWithData(data, formData);
        const advisoryNoteIcon = ButtonHelper.createAdvisoryNotesIcon(formData.companyAdvisoryNotes);
        if (advisoryNoteIcon) {
            $('#edit-hubrfq-dialog').find('#addnewhubrfq-companylbl').append(' ' + advisoryNoteIcon);
        }
    });

    if (!state.sendToPurchaseHubHandler) {
        state.sendToPurchaseHubHandler = new SendToPurchaseHubHandler(state.data);
    }

    if (!state.closeHubRfqHandler) {
        state.closeHubRfqHandler = new CloseHubRfqHandler(stateValue.id);
    }

    if (!state.releaseAllHandler) {
        state.releaseAllHandler = new ReleaseAllHandler();
    }

    if (!state.noBidAllHandler) {
        state.noBidAllHandler = new NoBidAllHandler();
    }

    if (!state.noBidHandler) {
        state.noBidHandler = new NoBidHandler();
    }

    const currentClientId = $("#client-by-master-dropdown").val();
    if (Number(state.data.clientNo) === Number(currentClientId)) {
        state.sendToPurchaseHubBtn = new PartTableButton(
            "#send-purchase-hub-btn",
            async () => {
                let response = await GlobalTrader.ApiClient.getAsync(
                    `/orders/bom/${stateValue.id}`
                );

                $("#hubrfq-send-to-purchase-hub-dialog [name='assignUserNo']").dropdown(
                    {
                        serverside: false,
                        endpoint: "/lists/employee/purchase-hub/114",
                        valueKey: "loginId",
                        textKey: "employeeName",
                        isHideRefresButton: false,
                        isCacheApplied: true,
                    }
                );
                state.sendToPurchaseHubHandler.updateData(response.data);
                state.sendToPurchaseHubHandler.open();
            }
        );
    }

    if (state.closeBtn.element && !state.data.isClosed && state.data.isAssignToMe) {
        $('#close-btn').attr('disabled', false);
    }

    if ($("#no-bid-btn")) {
        state.noBidAllBtn = new PartTableButton("#no-bid-btn", function () {
            state.noBidAllHandler.form.open(state.data);
        });
    }

    if ($("#nobid-btn")) {
        state.noBidBtn = new PartTableButton("#nobid-btn", function () {
            const selectedRowData = getSelectedRowData();
            if (!selectedRowData) {
                return;
            }
            state.noBidHandler.form.open({
                ...state.data,
                customerRequirementId: selectedRowData.customerRequirementId
            });
        });
    }

    if ($("#magicbox")) {
        state.magicBoxBtn = new PartTableButton("#magicbox", function () {
            openMagicBoxDialog();
        });
    }

    if ($("#recallnobid-btn")) {
        state.recallNoBidBtn = new PartTableButton("#recallnobid-btn", function () {
            const selectedRowData = getSelectedRowData();
            if (!selectedRowData) {
                showToast('warning', localizedTitles.noRowSelected || 'No row selected');
                return;
            }
            state.recallNoBidDialog.open();
        });
    }

    if ($("#note-btn")) {
        state.noteBtn = new PartTableButton("#note-btn", function () {
            $("#add-communication-note-dialog textarea[name='expediteNotes']").val('');
            state.addCommunicationNoteDialog.open();
        });
    }

    if ($("#release-btn")) {
        state.releaseBtn = new PartTableButton("#release-btn", async () => {
            const errorSummarySelector = "#main-information-box #main-information-content .form-error-summary";
            if (!state.allHasDelDate || !state.allHasProduct) {
                toggleFormErrorSummary(errorSummarySelector, true, localizedTitles.errorDelDateAndProduct);
            } else {
                toggleFormErrorSummary(errorSummarySelector, false);
                const response = await GlobalTrader.ApiClient.getAsync(
                    `/orders/bom/${stateValue.id}/sourcing-for-release`
                );
                // Condition to check price
                let buyPrice = null;
                let sellPrice = null;
                state.merginCanReleaseAll = false;

                if (response.data.length > 0) {
                    for (let i = 0; i < response.data.length; i++) {
                        const row = response.data[i];
                        buyPrice = row.buyPrice;
                        sellPrice = row.price;

                        if (buyPrice != null && sellPrice != null) {
                            if (buyPrice >= sellPrice) {
                                state.merginCanReleaseAll = true;
                            }
                        }
                    }
                }

                // Toggle error mergin
                if (!state.merginCanReleaseAll) {
                    toggleFormErrorSummary(errorSummarySelector, false);
                } else {
                    toggleFormErrorSummary(errorSummarySelector, true, localizedTitles.errorMerginCanRelease);
                }

                // Open dialog
                state.releaseAllHandler.form.open(state.data);
            }
        });
    }

    $('#exporttoexcel-btn').button().on('click', () => {
        GlobalTrader.ApiClient.postDownloadFileAsync(
            "/orders/bom/export-to-excel",
            {
                id: stateValue.id,
            },
            {},
            {
                timeoutMilliseconds: 500 * 1000,
            },
        );
    });

    state.closeHubRfqHandler.form.on('closedWithSuccessedResponse.mf', async () => {
        state.mainInformationSectionBox.trigger('onRefreshed.msb');
        await updateBomStatus(stateValue.id);
    });

    state.sendToPurchaseHubHandler.form.on('closedWithSuccessedResponse.mf', () => {
        state.mainInformationSectionBox.trigger('onRefreshed.msb');
    });

    state.noBidAllHandler.form.on('closedWithSuccessedResponse.mf', async () => {
        state.mainInformationSectionBox.trigger('onRefreshed.msb');
        state.hubRfqItemsTable.table.ajax.reload(function () {
                setTimeout(updateButtonStates, 0);
            });
        await updateBomStatus(stateValue.id);
    });

    state.releaseAllHandler.form.on('closedWithSuccessedResponse.mf', async () => {
        state.mainInformationSectionBox.trigger('onRefreshed.msb');
        await updateBomStatus(stateValue.id);
    });

    state.noBidHandler.form.on('closedWithSuccessedResponse.mf', async () => {
        if (state.hubRfqItemsTable?.table) {
            await new Promise(resolve => {
                state.hubRfqItemsTable.table.ajax.reload(async function () {
                    updateButtonStates();
                    await updateBomStatus(stateValue.id);
                    resolve();
                }, false);
            });

            if (state.itemDetailComponent) {
                await state.itemDetailComponent.reload();
            }
        }
    });

    if (!state.itemDetailComponent) {
        state.itemDetailComponent = new ItemDetailComponent();
    }
}

function initUnreleaseButtonVisibility() {
    const isPOHub = window.isPoHub();
    const shouldHideUnreleaseButton =
        (userContext.isGSAUser && userContext.isDifferentClient && !userContext.isDMCC) ||
        !isPOHub;

    const $unReleaseBtn = $('#hub-rfq-item-box #unrelease-btn');
    if (shouldHideUnreleaseButton) {
        $unReleaseBtn.hide();
    } else {
        if (state.hasSourcingResultFlag === null) {
            $unReleaseBtn.prop('disabled', true);
            $unReleaseBtn.show();
            return;
        }
        const selectedRowData = getSelectedRowData();
        const hasRowSelection = selectedRowData !== null;
        const isRequirementReleased = selectedRowData?.isReleased === true;
        const hasSourcingResult = state.hasSourcingResultFlag === true;
        const isHubRfqClosed = state.data?.isClosed === true;
        const isAssignedToMe = selectedRowData?.isAssignedToMe === true;
        const canUnRelease = hasRowSelection &&
            isRequirementReleased &&
            isPOHub &&
            hasSourcingResult &&
            !isHubRfqClosed &&
            isAssignedToMe;
        $unReleaseBtn.prop('disabled', !canUnRelease);
        $unReleaseBtn.show();
    }
}

async function generateTemplateDiaLogBomQualification(elementId, isView) {
    let data = [];

    let response = await GlobalTrader.ApiClient.getAsync(
        `/orders/bom/${stateValue.id}/pvv-items`
    );
    data = response.data;

    let template = function (question, answer, questionId) {
        return `
    <div class="row mb-3">
        <div class="col-6">
            <label for="${questionId}" 
                   class="form-label mb-0 fw-bold"
                   style="display:block; max-width:100%; word-break:break-word; white-space:normal;">
                ${question}
            </label>
        </div>
        <div class="col-6">
            <textarea
                class="form-control form-textarea height-auto"
                style="height:auto"
                id="${questionId}"
                name="PPVAsnwerNote"
                rows="2"
                maxlength="500"
                data-directive="maxLength"
                ${isView ? "disabled" : ""}
            >${answer ?? ""}</textarea>
        </div>
    </div>
    `;
    }


    let formHtml = "";
    data.forEach(item => {
        formHtml += template(item.pvvQuestionName, item.pvvAnswerName, item.pvvQuestionId)
    });
    $(elementId).html(formHtml);
}

async function initCommunicationNotesTable() {
    try {
        state.communicationTable = new GlobalTrader.Common.SearchTablePageBase({
            sectionBoxSelector: "#communication-note-box",
            tableSelector: "#communication-note-table",
            tableOptions: {
                ajax: `/api/orders/bom/communication-note/${stateValue.id}`,
                language: {
                    emptyTable: `<i>${localizedTitles.noData}</i>`
                },
                searching: false,
                select: false,
                columnDefs: [
                    { type: 'string', targets: '_all' }
                ],
                columns: [
                    {
                        title: localizedTitles.expediteNotes,
                        data: "expediteNotes",
                        width: "30%",
                        className: "text-break text-pre-wrap",
                    },
                    {
                        title: localizedTitles.reqNo,
                        data: "reqNo",
                        width: "10%",
                    },
                    {
                        title: localizedTitles.dlup,
                        data: "dlup",
                        width: "12%",
                    },
                    {
                        title: localizedTitles.employeeName,
                        data: "employeeName",
                        width: "12%",
                    },
                    {
                        title: localizedTitles.assignTo,
                        data: "assignTo",
                        width: "12%",
                    },
                    {
                        title: localizedTitles.ccUserId,
                        data: "ccUserID",
                        width: "10%",
                    },
                    {
                        title: localizedTitles.sendToGroup,
                        data: "sendToGroup",
                        width: "14%",
                    },
                ],
                rowId: "hubrfqExpediteNotesId",
                layout: {
                    bottomStart: null,
                    bottomEnd: null,
                    topStart: null,
                    topEnd: null
                },
            }
        });

        state.communicationTable.init();
    } catch (error) {
        console.error("Error initializing Communication Notes Table:", error);
        throw error;
    }
}

async function initHubRfqItemsTable() {
    try {

        state.hubRfqItemsTable = new GlobalTrader.Common.SearchTablePageBase({
            sectionBoxSelector: "#hub-rfq-item-box",
            tableSelector: "#hub-rfq-items-table",
            tableOptions: {
                ajax: {
                    url: `/api/orders/bom/${stateValue.id}/items`,
                    type: 'GET',
                    dataSrc: function (json) {
                        enableAddHubrfqItemBtn();
                        if (json.success && json?.data?.items) {
                            state.allHasProduct = json.data.allHasProduct;
                            state.allHasDelDate = json.data.allHasDelDate;
                            updateCustomerRequirementsTitle(json.data.items.length);
                            state.data.items = json.data.items;
                            state.hubRfqItemsTable.totalRow = json.data.items.length;
                            return json.data.items;
                        }
                        const items = json.items || json.data || json || [];
                        updateCustomerRequirementsTitle(items.length);
                        return items;
                    },
                    error: function (xhr, error, code) {
                        updateCustomerRequirementsTitle(0);
                    }
                },
                language: {
                    emptyTable: `<i>${localizedTitles.noDataHUBRFQItems}</i>`
                },
                searching: false,
                select: {
                    style: 'single',
                    toggleable: true,
                    info: false,
                },
                resizeConfig: {
                    numberOfRowToShow: 3
                },
                columnDefs: [
                    { type: 'string', targets: '_all' },
                    { orderable: false, targets: [0] },
                    { width: "3%", targets: [0] },
                    { width: "6%", targets: [1] },
                    { width: "8%", targets: [2] },
                    { width: "10%", targets: [3] },
                    { width: "5%", targets: [4] },
                    { width: "7%", targets: [5] },
                    { width: "7%", targets: [6] },
                    { width: "20%", targets: [7] },
                    { width: "6%", targets: [8] },
                    { width: "11%", targets: [9] },
                    { width: "7%", targets: [10] },
                    { width: "9%", targets: [11] }
                ],
                columns: [
                    {
                        title: "",
                        data: null,
                        className: "text-right",
                        orderable: false,
                        render: function (data, type, row) {
                            let content = '';
                            const isAssignedToMe = row.isAssignedToMe === true;
                            const isRequestToPurchaseQuote = row.isRequestToPurchaseQuote === true;
                            const isClosed = state.data?.isClosed === true;

                            if (isAssignedToMe) {
                                const isCheckboxEnabled = isRequestToPurchaseQuote && !isClosed;
                                const disabledAttr = !isCheckboxEnabled ? 'disabled' : '';
                                const disabledClass = !isCheckboxEnabled ? 'disabled-checkbox imageCheckBoxDisabled' : 'imageCheckBox';

                                content = `<input type="checkbox" class="form-check-input ${disabledClass}" ${disabledAttr} data-row-id="${GlobalTrader.StringHelper.setCleanTextValue(row.customerRequirementId)}">`;
                            } else {
                                content = '';
                            }
                            return content;
                        }
                    },
                    {
                        ...createHubRfqDataTableDefaultColumns("customerRequirementNumber", localizedTitles.req),
                        render: function (data, type, row) {
                            // highlight alternate parts with yellow background
                            // V1: t.Alt==!0?$R_FN.showYellowTextImportant($RGT_nubButton_CustomerRequirement(t.ID,t.CustReqNo))
                            const isAlternate = row.alt === true;
                            if (isAlternate) {
                                return `<span class="bg-warning" style="background-color: #FFFF00 !important;">${ButtonHelper.createNubButton(ButtonHelper.URL_CustomerRequirement(row.customerRequirementId), row.customerRequirementNumber) || ""}</span>`;
                            }
                            return ButtonHelper.createNubButton(ButtonHelper.URL_CustomerRequirement(row.customerRequirementId), row.customerRequirementNumber) || "";
                        }
                    },
                    {
                        title: isPoHub ?
                            renderHubRfqTitle(localizedTitles.quantity, localizedTitles.hubPartWatch) :
                            localizedTitles.quantity || "Quantity",
                        data: "quantity",
                        className: "text-wrap text-break header-custom",
                        render: function (data, type, row) {
                            const partWatch = row.partWatchHUBIPO === true ? "Yes" : "No";
                            if (isPoHub) {
                                const quantityDiv = $('<div></div>');
                                quantityDiv.text(data ? data : "");

                                const partWatchDiv = $('<div style="font-size:12px; padding-top: 10px;"></div>');
                                partWatchDiv.text(" " + partWatch);

                                const container = $('<div></div>');
                                container.append(quantityDiv);
                                container.append(partWatchDiv);

                                return container.prop('outerHTML');
                            }
                            return data ? data : "";
                        }
                    },
                    {
                        ...createHubRfqDataTableDefaultColumns("part", localizedTitles.partNo, localizedTitles.customerPart),
                        render: function (data, type, row) {
                            let partText = renderPartAlternate(data, row.alternateStatus, row.alt);

                            if (row.rohs && row.rohs > 0 && row.rohs !== 4) {
                                partText = ROHSHelper.writePartNo(partText, row.rohs);
                            }

                            const partDiv = $('<div></div>');
                            if (row.alt === true) {
                                const highlightSpan = $('<span class="alternate-part-highlight" style="background-color: #FFFF00 !important; color: #000000 !important;"></span>');
                                highlightSpan.html(partText); // partText is already safely generated HTML
                                partDiv.append(highlightSpan);
                            } else {
                                partDiv.html(partText); // partText is already safely generated HTML
                            }

                            const customerPartDiv = $('<div style="font-size:12px; padding-top: 10px;"></div>');
                            customerPartDiv.text(row.customerPart || "");

                            const container = $('<div></div>');
                            container.append(partDiv);
                            container.append(customerPartDiv);

                            return container.prop('outerHTML');
                        }
                    },
                    {
                        ...createHubRfqDataTableDefaultColumns("mfr", localizedTitles.mfr, localizedTitles.dC),
                        render: function (data, type, row) {

                            let icons = '';

                            // V1 Logic: Price Issue Alert for ALL rows when POHub and PriceIssueBuyAndSell is true
                            // V1: this._blnPOHub==!0&&t.PriceIssueBuyAndSell==!0&&(u="notReadyToShip",e=!0)
                            if (isPoHub && (row.priceIssueBuyAndSell === true || row.priceIssueBuyAndSell === 1)) {
                                icons += ' <img src="/img/icons/circle-exclamation-red.svg" ' +
                                    'class="ms-1 rounded-circle bg-white" ' +
                                    `title="${localizedTitles.sourcingResultsPriceIssue}" ` +
                                    'height="14"' +
                                    'data-bs-toggle="tooltip" data-bs-placement="bottom" alt="Price Issue" />';
                            }

                            if (row.mfrAdvisoryNotes?.trim()) {
                                const escapedNotes = GlobalTrader.StringHelper.setCleanTextValue(row.mfrAdvisoryNotes);
                                icons += ' <img src="/img/icons/circle-exclamation-red.svg" ' +
                                    `title="${escapedNotes}" ` +
                                    'class="ms-1 rounded-circle bg-white" height="14" ' +
                                    'data-bs-toggle="tooltip" data-bs-placement="bottom" alt="Price Issue" />';
                            }

                            const manufacturerHtml = row.mfr ? ButtonHelper.nubButton_Manufacturer(row.mfrNo, row.mfr, row.mfrAdvisoryNotes) : "";

                            const container = $('<div></div>');
                            if (manufacturerHtml) {
                                container.html(manufacturerHtml + "<br>"); // manufacturerHtml is safely generated
                            }

                            const dcSpan = $('<span></span>');
                            dcSpan.text(row.dc || "");
                            container.append(dcSpan);

                            return container.prop('outerHTML');
                        }
                    },
                    {
                        ...createHubRfqDataTableDefaultColumns("product", localizedTitles.product, localizedTitles.package),
                        render: function (data, type, row) {
                            const productDiv = $('<div></div>');
                            productDiv.text(data || "");

                            const packageDiv = $('<div style="font-size:12px; padding-top: 10px;"></div>');
                            packageDiv.text(row.package || "");

                            const container = $('<div></div>');
                            container.append(productDiv);
                            container.append(packageDiv);

                            return container.prop('outerHTML');
                        }
                    },
                    {
                        ...createHubRfqDataTableDefaultColumns("company", localizedTitles.customer, localizedTitles.required),
                        render: function (data, type, row) {
                            const customerDiv = $('<div></div>');
                            customerDiv.text(data || "");

                            // V1 Logic: Add Advisory Notes icon if present
                            if (row.companyAdvisoryNotes?.trim()) {
                                const escapedNotes = GlobalTrader.StringHelper.setCleanTextValue(row.companyAdvisoryNotes);
                                const iconHtml = ' <img src="/img/icons/circle-exclamation-red.svg" ' +
                                    `title="${escapedNotes}" ` +
                                    'class="ms-1 rounded-circle bg-white" height="14" ' +
                                    'data-bs-toggle="tooltip" data-bs-placement="bottom" alt="Price Issue" />';
                                customerDiv.append(iconHtml);
                            }

                            const requiredDiv = $('<div style="font-size:12px; padding-top: 10px;"></div>');
                            requiredDiv.text(row.date || "");

                            const container = $('<div></div>');
                            container.append(customerDiv);
                            container.append(requiredDiv);

                            return container.prop('outerHTML');
                        }
                    },
                    {
                        ...createHubRfqDataTableDefaultColumns("tPriceInBom", localizedTitles.targetPrice, localizedTitles.salesperson),
                        width: "8%",
                        render: function (data, type, row) {
                            const priceDiv = $('<div></div>');
                            priceDiv.text(data || "");

                            const salespersonDiv = $('<div style="font-size:12px; padding-top: 10px;"></div>');
                            salespersonDiv.text(row.salesmanName || "");

                            const container = $('<div></div>');
                            container.append(priceDiv);
                            container.append(salespersonDiv);

                            return container.prop('outerHTML');
                        }
                    },
                    {
                        ...createHubRfqDataTableDefaultColumns("msl", localizedTitles.msl, localizedTitles.factorySealed),
                        render: function (data, type, row) {
                            const mslDiv = $('<div></div>');
                            if (data) {
                                mslDiv.text(data); // Use text() for actual data to prevent XSS
                            } else {
                                mslDiv.html("&nbsp;"); // Only use html() for safe HTML entity when empty
                            }

                            const factorySealedDiv = $('<div style="font-size:12px; padding-top: 10px;"></div>');
                            factorySealedDiv.text(row.factorySealed || "NO");

                            const container = $('<div></div>');
                            container.append(mslDiv);
                            container.append(factorySealedDiv);

                            return container.prop('outerHTML');
                        }
                    },
                    {
                        title: "Inhouse AS6081<br>testing required",
                        data: "isAs6081Required",
                        name: "isAs6081Required",
                        className: "text-wrap text-break text-left header-custom",
                        render: function (data, type, row) {
                            // V1 Logic: Check for AS6081 - handle both boolean and string values
                            const isRequired = data === true || data === "Yes" || data === "yes";
                            const displayText = isRequired ? "Yes" : "No";
                            return displayText;
                        }
                    },
                    {
                        ...createHubRfqDataTableDefaultColumns("assignedTo", localizedTitles.assignedTo),
                        render: function (data) {
                            return GlobalTrader.StringHelper.setCleanTextValue(data || "");
                        }
                    },
                    {
                        title: isPoHub ?
                            renderHubRfqTitle(localizedTitles.priceRequest, localizedTitles.notes) :
                            localizedTitles.notes,
                        data: "instructions",
                        className: "text-wrap text-break text-left header-custom",
                        render: function (data, type, row) {
                            const instructions = data || "";
                            // Replace <br> tags with actual line breaks for display
                            const formattedInstructions = instructions.replace(/<br(?:\s*\/)?\s*>/gi, '\n');
                            if (isPoHub) {
                                // Show Purchase Request status in top row, Instructions in bottom row
                                const prStatusDiv = $('<div></div>');
                                prStatusDiv.text("YES");
                                const instructionsDiv = $('<div style="font-size:12px; padding-top: 10px; white-space:pre-line;"></div>');
                                instructionsDiv.text(formattedInstructions);
                                const container = $('<div></div>');
                                container.append(prStatusDiv);
                                container.append(instructionsDiv);
                                return container.prop('outerHTML');
                            } else {
                                // Show only Instructions for non-POHub users
                                let span = $('<span></span>');
                                span.text(formattedInstructions);
                                return span.prop('outerHTML');
                            }
                        }
                    }
                ],
                rowId: "customerRequirementId",
                headerCallback: (thead) => {
                    $(thead).find("th").addClass('align-baseline');
                },
                rowCallback: function (row, data) {
                    $(row).removeClass('cusReqMainPart allocated readyToShip notReadyToShip notReadyToShip_First readyToShip_First');
                    let rowClass = 'cusReqMainPart';
                    if (data.isReleased || data.released) {
                        rowClass = 'readyToShip';
                    } else if (data.isAllocated || (!data.released && data.hasSourcingResult)) {
                        rowClass = 'allocated';
                    }
                    if (isPoHub && (data.priceIssueBuyAndSell === true || data.priceIssueBuyAndSell === 1)) {
                        rowClass = 'notReadyToShip';
                    }
                    $(row).addClass(rowClass);

                    const $firstCell = $('td:eq(0)', row);
                    if (rowClass === 'notReadyToShip') {
                        $firstCell.addClass('notReadyToShip_First');
                    } else if (rowClass === 'readyToShip') {
                        $firstCell.addClass('readyToShip_First');
                    } else if (rowClass === 'allocated') {
                        $firstCell.addClass('allocated_First');
                    }
                },
                initComplete: function (settings, json) {
                    // Only enable the button if other conditions are also met
                    const rowCount = state.hubRfqItemsTable.table.rows().count();
                    if (
                        rowCount > 0 &&
                        !state.data.inActive &&
                        !isPoHub &&
                        !state.data.isClosed &&
                        !state.data.blnReqToPoHub &&
                        state.data.isAssignToMe
                    ) {
                        $("#send-purchase-hub-btn").attr("disabled", false);
                    }

                    if (rowCount > 0 &&
                        isPoHub &&
                        !state.data.blnRelease &&
                        !state.data.inActive &&
                        state.data.allHasSourcing > 0 &&
                        !state.data.isClosed &&
                        state.data.isAssignToMe
                    ) {
                        $("#release-btn").attr("disabled", false);
                    }

                    if (state.hubRfqItemsTable.table.rows().count() > 0 &&
                        isPoHub &&
                        !state.data.inActive &&
                        state.data.isNoBidCount &&
                        !state.data.isClosed &&
                        state.data.isAssignToMe
                    ) {
                        $("#no-bid-btn").attr("disabled", false);
                    }

                    if (state.data.items.some(x => x.bomStatus.toUpperCase() != "RELEASED" && x.bomStatus.toUpperCase() != "CLOSED")) {
                        $("#hubimportsr-btn").attr("disabled", false);
                        $("#exporttoexcel-btn").attr("disabled", false);
                    }
                    else {
                        $("#hubimportsr-btn").attr("disabled", true);
                        $("#exporttoexcel-btn").attr("disabled", true);
                    }

                    // V1 Logic: Initialize button states after table is fully loaded
                    updateButtonStates();
                }
            }
        });

        state.hubRfqItemsTable.init();

        state.hubRfqItemsTable.table.on('xhr.dt', function () {
            updateButtonStates();
        });

        $(state.hubRfqItemsTable.tableSelector).on('draw.dt', () => {
            $("#hub-rfq-items-table-wrapper").removeClass("d-none");
            const $table = $("#hub-rfq-items-table");
            $table.css({ 'table-layout': 'auto', 'width': '100%' });
        });

        $(document).on('mouseenter', '.notReadyToShip .notReadyToShip_First', function () {
            $(this).attr('title', localizedTitles.sourcingResultsPriceIssue || 'Sourcing Results having price issue kindly check and verify.');
        });

        $(document).on('change', '#hub-rfq-items-table input[type="checkbox"]', function () {
            updateButtonStates();
        });

        // V1 Logic: Prevent interaction with disabled checkboxes (imageCheckBoxDisabled behavior)
        $(document).on('click', '#hub-rfq-items-table input[type="checkbox"]:disabled', function (e) {
            e.preventDefault();
            e.stopPropagation();
            return false;
        });

        state.hubRfqItemsTable.table.on('select', async (e, dt, type, indexes) => {
            if (type === 'row') {
                updateButtonStates();
                const currentSelectedItems = dt.row(indexes).data();

                await state.itemDetailComponent.setId(currentSelectedItems.customerRequirementId);

                const isBomDetailsInactive = $("#is-inactive").is(':checked');
                if (currentSelectedItems) {
                    if (state.kubAssistance) {
                        state.kubAssistance.closeModal();
                    }

                    await Promise.all([
                        refreshLyticaApiData(currentSelectedItems.part, currentSelectedItems.mfr, currentSelectedItems.mfrNo),
                        loadBOMItemDetail(state.itemDetailComponent.getCurrentResponse())
                    ]);

                    stateValue.selectedRequirementDetails = {
                        number: currentSelectedItems.customerRequirementNumber,
                        id: currentSelectedItems.customerRequirementId,
                        closed: currentSelectedItems.closed,
                        companyId: currentSelectedItems.companyNo,
                        companyName: currentSelectedItems.company,
                        part: currentSelectedItems.part
                    }

                    const isAs6081Required = currentSelectedItems.isAs6081Required === "Yes";

                    if (isAs6081Required) {
                        const messageText = localizedTitles.as6081ComplianceRequired;
                        showAs6081ComplianceNotification(true, messageText);
                    } else {
                        showAs6081ComplianceNotification(false);
                    }

                    // Sourcing result/ Quote to Client visible when bomDetails inactive is false
                    if (!isBomDetailsInactive) {
                        state.sourcingResultsSectionBox.setIsAssignToMe(currentSelectedItems.isAssignedToMe);
                        state.sourcingResultsSectionBox.setSelectedCustomerRequirementId(currentSelectedItems.customerRequirementId);
                        state.sourcingResultsSectionBox.setIsClose(currentSelectedItems.closed);
                        state.currentSelectedPartDetailItem.id = currentSelectedItems.customerRequirementId;
                        state.sourcingResultsSectionBox.setupSourcingResultsTable();

                        // bidding event to sourcingResultsSectionBox to init image document section on selected
                        state.sourcingResultsSectionBox.state.sourcingResultsTable.on("select", async () => {
                            if (type !== 'row') return;
                            const selectedRows = state.sourcingResultsSectionBox.state.sourcingResultsTable.rows({ selected: true });
                            const selectedCount = selectedRows.count();
                            if (selectedCount !== 1) {
                                $("#" + dropDragImageDocumentsSectionId + "-wrapper").addClass('d-none');
                                return;
                            }
                            const selectedId = state.sourcingResultsSectionBox.state.selectedSourcingResultDetailsId;
                            if (selectedId) {
                                await initDragDropImageSection(selectedId);
                                $("#" + dropDragImageDocumentsSectionId + "-wrapper").removeClass('d-none');
                            } else {
                                $("#" + dropDragImageDocumentsSectionId + "-wrapper").addClass('d-none');
                            }
                        });

                        state.sourcingResultsSectionBox.state.sourcingResultsTable.on("deselect", async () => {
                            if (type !== 'row') return;
                            $("#" + dropDragImageDocumentsSectionId + "-wrapper").addClass('d-none');
                        });
                    };
                }
                // Sourcing visible when bomDetails inactive is false and status not equal to 'CLOSED'
                const bomDetailsStatus = $("#bom-details-status").text();
                let isClose = bomDetailsStatus == 'CLOSED';
                if (!isBomDetailsInactive && !isClose) {
                    $("#sourcing-box").removeClass("d-none");
                }

                document.getElementById("PartNumber").value = currentSelectedItems.partNo;
                document.getElementById("PartNumber").dispatchEvent(new Event("change"));
            }
        });

        state.hubRfqItemsTable.table.on('deselect', async (e, dt, type, indexes) => {
            if (type === 'row') {
                showAs6081ComplianceNotification(false);
                showDeliveryDateProductValidationNotification(false);
                // V1 Logic: Update button states when row is deselected
                updateButtonStates();

                GlobalTrader.SourcingHelper.collapseAllSections();
                resetStoredSourcingState();
            }
        });

        state.hubRfqItemsTable.table.on('user-select.dt', (e, dt, type, cell) => {
            if (type !== 'row') return;

            const selectedIndexes = dt.rows({ selected: true }).indexes();
            const clickedRowIndex = cell.index().row;

            if (selectedIndexes.length && selectedIndexes[0] === clickedRowIndex) {
                e.preventDefault(); // prevent deselection
            }
        });

        updateButtonStates();

    } catch (error) {
        console.error("Error initializing HUBRFQ Items Table:", error);
        throw error;
    }
}
function renderPartAlternate(part, alternateStatus, atl) {
    if (part && alternateStatus > 0 && atl) {
        const safePart = GlobalTrader.StringHelper.setCleanTextValue(part);
        const safeAlternative = GlobalTrader.StringHelper.setCleanTextValue(custReqAlternative[alternateStatus] || "");
        return `${safePart} (${safeAlternative})`
    }
    return GlobalTrader.StringHelper.setCleanTextValue(part || "");
}

function getSelectedRowIds() {
    // V1 Logic: Only consider enabled (non-disabled) checkboxes
    const checkboxes = $('#hub-rfq-items-table input[type="checkbox"]:checked:not(:disabled)');

    const selectedIds = [];
    checkboxes.each(function () {
        const rowId = $(this).data('row-id');
        if (rowId) {
            selectedIds.push(rowId);
        }
    });

    return selectedIds;
}

function getSelectedRowData() {
    // Get the selected row from DataTable
    const table = state.hubRfqItemsTable?.table;
    if (!table) return null;

    const selectedRows = table.rows('.selected').data();
    if (selectedRows.length > 0) {
        return selectedRows[0];
    }

    return null;
}

function updateButtonStates() {
    const selectedCount = getSelectedRowIds().length;
    const hasSelection = selectedCount > 0;
    const selectedRowData = getSelectedRowData();
    const rowDataHubrfqItemsTable = $('#hub-rfq-items-table').DataTable().rows({ selected: true }).data().toArray();
    const hasRowSelection = selectedRowData !== null;
    const isCusClosed = rowDataHubrfqItemsTable.length > 0 && rowDataHubrfqItemsTable[0].closed;

    notifyReleaseRequirementHandler(selectedRowData);

    if (!hasRowSelection && state.kubAssistance) {
        state.kubAssistance.disable();
    }

    // Enable Delete button validation

    const isHubRfqClosed = state.data?.isClosed === true;
    const isItemSentToPurchaseHub = selectedRowData?.isRequestToPurchaseQuote === true;
    const canDelete = hasRowSelection && !isHubRfqClosed && !isItemSentToPurchaseHub;

    const isPOHub = window.isPoHub(); // Assuming this function exists
    const canEdit = !state.data.isClosed && !isPOHub && state.data.isAssignToMe;
    const canAddCommunicationNote = state.data?.isAssignToMe === true;

    $('#main-information-edit-btn').prop('disabled', !canEdit);


    if ($("#hub-rfq-delete-btn").length) {
        $('#hub-rfq-delete-btn').prop('disabled', !canDelete);
    }

    const enableNobid = selectedRowData
        && selectedRowData.customerRequirementId > 0
        && !selectedRowData.hasSourcingResult
        && isPoHub
        && !isHubRfqClosed
        && !selectedRowData.isNoBid
        && selectedRowData.isAssignedToMe

    $('#hub-rfq-item-box #nobid-btn').prop('disabled', !enableNobid);

    // Recall No-Bid button: enabled when item is selected, has no-bid status, and HubRFQ not closed
    const isNoBidStatus = selectedRowData?.isNoBid === true || selectedRowData?.status?.toLowerCase().includes('no bid');
    const canRecallNoBid = hasRowSelection && isNoBidStatus && !isHubRfqClosed && isPOHub;
    $('#hub-rfq-item-box #recallnobid-btn').prop('disabled', !canRecallNoBid);

    $('#hub-rfq-item-box #note-btn').prop('disabled', !hasSelection || !canAddCommunicationNote);

    // Toggle editSourcingResult button
    $("#editSourcingResult").attr("disabled", true);

    // Toggle addSourcingResult button
    const canAddSourcingResult = !isCusClosed && state.data.isAssignToMe && !isPoHub
    $("#addSourcingResult").prop('disabled', !canAddSourcingResult);

    updatePartWatchButtonStates();
    updateSendToSupplierButtonStates(state.hubRfqItemsTable.table.rows().count(), state.data.blnReqToPoHub, state.data.isClosed, state.data.inActive);
    updateReleaseRequirementButton();
    $("#send-purchase-hub-btn").attr("disabled", !(state.hubRfqItemsTable.table.rows().count() > 0 && !isPoHub && !state.data.blnReqToPoHub
        && !state.data.inActive && !isHubRfqClosed && state.data?.isAssignToMe));
}

function updateReleaseRequirementButton() {
    const selectedRowData = getSelectedRowData();
    const hasRowSelection = selectedRowData !== null;
    const isHubRfqClosed = state.data?.isClosed === true;
    const isRequirementReleased = selectedRowData?.isReleased === true;
    const hasSourcingResult = selectedRowData?.hasSourcingResult === true;
    const isAssignedToMe = selectedRowData?.isAssignedToMe === true;
    const isPOHub = window.isPoHub();
    
    const $releaseRequirementBtn = $('#hub-rfq-item-box #release-requirement-btn');
    
    if (!isPOHub) {
        $releaseRequirementBtn.hide();
        return;
    }
    
    $releaseRequirementBtn.show();

    const canReleaseRequirement = hasRowSelection &&
        !isRequirementReleased &&
        isPOHub &&
        hasSourcingResult &&
        !isHubRfqClosed &&
        isAssignedToMe;
        
    $releaseRequirementBtn.prop('disabled', !canReleaseRequirement);
}

function updateUnReleaseButton() {
    const selectedRowData = getSelectedRowData();
    const hasRowSelection = selectedRowData !== null;
    const isHubRfqClosed = state.data?.isClosed === true;
    const isRequirementReleased = selectedRowData?.isReleased === true;
    const hasSourcingResult = state.hasSourcingResultFlag === true;
    const isAssignedToMe = selectedRowData?.isAssignedToMe === true;
    const isPOHub = window.isPoHub();
    const shouldHideUnreleaseButton =
        (userContext.isGSAUser && userContext.isDifferentClient && !userContext.isDMCC) ||
        !isPOHub;
    const $unReleaseBtn = $('#hub-rfq-item-box #unrelease-btn');
    if (shouldHideUnreleaseButton) {
        $unReleaseBtn.hide();
    } else {
        $unReleaseBtn.show();
        if (state.hasSourcingResultFlag === null) {
            $unReleaseBtn.prop('disabled', true);
            return;
        }
        const canUnRelease = hasRowSelection &&
            isRequirementReleased &&
            isPOHub &&
            hasSourcingResult &&
            !isHubRfqClosed &&
            isAssignedToMe;
        $unReleaseBtn.prop('disabled', !canUnRelease);
    }
}

function updateSendToSupplierButtonStates(hubrfqItemsNumber, isSentToPurchaseHub, isClosed, isInActive) {
    const shouldEnable =
        hubrfqItemsNumber > 0 &&
        !isClosed &&
        !isInActive &&
        ((isPoHub && isSentToPurchaseHub) || (!isPoHub && !isSentToPurchaseHub));

    $("#notify-btn").attr("disabled", !shouldEnable);
}

function setTextForSendToSupplierDialog() {
    const dialogBody = sendToSupplierLocalized.body;
    const dialogSubject = `HUBRFQ ${state.data.code} Notification`;
    $("#send-to-suplier-hub-form input[name='Subject']").val(dialogSubject);
    $("#send-to-suplier-hub-form textarea[name='Message']").val(dialogBody);

}

function updatePartWatchButtonStates() {
    // V1 Logic: Use checkbox selection instead of row selection
    const selectedIds = getSelectedRowIds();
    const applyBtn = $('#applypartwatch-btn');

    if (selectedIds.length === 0) {
        applyBtn.prop('disabled', true);
        return;
    }

    const table = state.hubRfqItemsTable?.table;
    if (!table) {
        applyBtn.prop('disabled', true);
        return;
    }

    let hasPartWatch = false;
    let hasNoPartWatch = false;
    let isClosed = false;

    selectedIds.forEach(reqId => {
        const rowData = table.rows().data().toArray().find(row => row.customerRequirementId == reqId);
        if (rowData) {
            if (rowData.partWatchHUBIPO === true) {
                hasPartWatch = true;
            } else {
                hasNoPartWatch = true;
            }
            if (rowData.status?.toLowerCase().includes('closed')) {
                isClosed = true;
            }
        }
    });

    if (isClosed) {
        applyBtn.prop('disabled', false);
    } else if (hasPartWatch && !hasNoPartWatch) {
        applyBtn.prop('disabled', true);
    } else {
        applyBtn.prop('disabled', false);
    }
}

async function initBOMQualificationAnswersTable() {
    state.bomQualificationTable = new LiteDatatable('#bom-qualification', {
        ajax: {
            url: `/api/orders/bom/${stateValue.id}/pvv-items`,
            type: 'GET',
        },
        language: {
            emptyTable: `<i>${localizedTitles.noQuestionPPVData}</i>`
        },
        searching: false,
        resizeConfig: { numberOfRowToShow: 5 },
        columnDefs: [
            { type: 'string', targets: '_all' }
        ],
        columns: [
            {
                title: 'Question',
                data: "pvvQuestionName",
                width: "50%",
                className: 'text-wrap text-break',
            },
            {
                title: 'Answer',
                data: "pvvAnswerName",
                className: 'text-wrap text-break',
                width: "50%",
            }
        ],
        rowId: "pvvQuestionId",
        disableSelect: true,
        enableResize: true
    });

    state.bomQualificationTable.on('xhr.dt', function (e, settings, json, xhr) {
        const rows = json.data || [];
        const isRecord = rows.length > 0;
        let inactive = true;
        if (rows.length > 0) {
            const lastRecord = rows[rows.length - 1];
            if (lastRecord.pvvAnswerId == 0) {
                inactive = false;
            }
        }

        GlobalTrader.ApiClient.getAsync(`/orders/bom/${stateValue.id}/get-check-data`)
            .then(response => {
                const data = response?.data;
                if (data.length > 0) {
                    $("#bom-qualification-edit-btn").show()
                    $('#lblPVVBOMIHS').css('display', 'none');
                } else {
                    $("#bom-qualification-edit-btn").hide()
                    $('#lblPVVBOMIHS').css('display', 'inline-block');
                }
            });
        enableBomQualificationTableButtons(isRecord, inactive);
    });

    state.bomQualificationTable.init();
}

function enableBomQualificationTableButtons(isRecord, inactive) {
    $('#bom-qualification-view-btn').prop('disabled', !isRecord);
    $('#bom-qualification-edit-btn').prop('disabled', isPoHub);
    $('#bom-qualification-delete-btn').prop('disabled', !(isRecord && inactive && !isPoHub));
}

function getDisplayBomName(bomName, bomClientNo) {
    let lastPart = bomName.split('-').pop();
    let isExcelOrCsv = bomName.endsWith('.xlsx') || bomName.endsWith('.xls') || bomName.endsWith('.csv');
    if (isExcelOrCsv && lastPart != bomClientNo) {
        return bomName;
    }
    if (lastPart == bomClientNo) {
        let lastIndex = bomName.lastIndexOf('-');
        return bomName.substring(0, lastIndex);
    }
    return bomName;
}

async function initForm() {
    state.sendToSuplier = new LiteFormDialog("#send-to-suplier-dialog", {
        width: '40vw',
        closeWhenSuccess: true,
        url: "/api/orders/bom/send-mail-message-supplier",
        method: "POST",
        buttons: [
            { name: "save", icon: "check", alt: "yes", display: 'Send' },
            { name: "cancel", icon: "xmark", alt: "no", display: 'Cancel' }
        ],
        validationRules: {
            Subject: {
                required: true,
            },
            Message: {
                required: true
            },
            To: {
                required: true,
            }
        },
        validationMessages: {
            Subject: window.localizedStrings.requiredField,
            Message: window.localizedStrings.requiredField,
            To: window.localizedStrings.requiredField
        },
        errorPlacement: function (error, element) {
            if (element.attr("name") === "To") {
                error.insertAfter(element);
            } else {
                error.insertAfter(element.parent());
            }
        },
        body: function () {
            const subject = $(`#send-to-suplier-hub-form [name='Subject']`).val();
            const message = $(`#send-to-suplier-hub-form [name='Message']`).val();
            const arrayIds = state.toAutoSearch.getSelectedMetadata();

            const valuesArray = arrayIds.map(item => item.value);
            const itemValuesArray = arrayIds.map(item => item.itemValue);

            return {
                toCompanyIdsArray: itemValuesArray,
                toLoginsArray: valuesArray,
                subject: subject,
                message: message,
                currencyCode: state.currencyCode,
                id: stateValue.id,
                reportNo: 114
            }
        }
    })

    state.sendToSuplier.close = function () {
        this.resetForm();
        state.toAutoSearch.resetSearchSelect();
        this.summaryDiv.hide();
        this.$dialog.dialog("close");
    };

    state.bomQualificationDialogView = new LiteFormDialog("#bom-qualification-view-dialog", {
        width: '80vw',
        closeWhenSuccess: true,
        method: "POST",
        buttons: [
            { name: "cancel", icon: "xmark", alt: "no", display: 'Cancel' }
        ],
        maxHeight: 580
    })

    state.bomQualificationDialogDelete = new LiteFormDialog("#bom-qualification-delete-dialog", {
        width: '500px',
        closeWhenSuccess: true,
        url: function () {
            if (state.generatedID) {
                return `/api/orders/bom/delete-temp-pvv-questions/${stateValue.id}`;
            } else {
                return `/api/orders/bom/delete-pvv-questions/${stateValue.id}`;
            }
        },
        method: "DELETE",
        buttons: [
            { name: "save", icon: "check", alt: "yes", display: 'Yes' },
            { name: "cancel", icon: "xmark", alt: "no", display: 'No' }
        ]
    })

    state.bomQualificationDialog = new LiteFormDialog("#bom-qualification-dialog", {
        width: '80vw',
        closeWhenSuccess: true,
        url: function () {
            if (state.generatedID) {
                return "/api/orders/bom/temp-pvv-answers";
            } else {
                return `/api/orders/bom/${stateValue.id}/pvv-answers`;
            }
        },
        method: "POST",
        buttons: [
            { name: "save", icon: "check", alt: "yes", display: 'Save' },
            { name: "cancel", icon: "xmark", alt: "no", display: 'Cancel' }
        ],
        maxHeight: 600,
        body: function () {
            //get information in bomdetails and bind on this
            let items = $("#bom-qualification-form").find("textarea");
            let pvvAnswers = "";
            for (let index = 0; index < items.length; index++) {
                const element = items[index];
                pvvAnswers += `${element.id}|${$(element).val()}`
                if (index < items.length - 1) {
                    pvvAnswers += ',|,';
                }
            }
            return {
                BomIdGenerated: state.generatedID,
                BOMNo: stateValue.id,
                PVVAnswers: pvvAnswers
            }
        }
    });

    state.bomItemDeleteDialog = new LiteFormDialog("#bom-item-delete-dialog", {
        width: '400px',
        closeWhenSuccess: true,
        url: "/api/orders/bom/delete-bom-item",
        method: "DELETE",
        buttons: [
            { name: "save", icon: "check", alt: "yes", display: 'Yes' },
            { name: "cancel", icon: "xmark", alt: "no", display: 'No' }
        ],
        body: function () {
            const selectedRowData = getSelectedRowData();
            if (!selectedRowData) {
                throw new Error('No row selected');
            }
            return {
                bomId: stateValue.id,
                requirementId: selectedRowData.customerRequirementId
            };
        }
    });

    state.bomItemDeleteDialog.$form.on('closedWithSuccessedResponse.mf', async (e, { response }) => {
        await new Promise(resolve => {
            state.hubRfqItemsTable.table.ajax.reload(function () {
                updateButtonStates();
                resolve();
            });
        });

        if (state.hubRfqItemsTable.table.rows().count() == 0) {
            state.itemDetailComponent.clearUI();
            $("#sourcing-box").addClass("d-none");
            $("#customer-requrement-sourcing-results-box").addClass("d-none");
            $('#bom-details-status').text("NEW");
        }
    });

    state.unReleaseConfirmDialog = new LiteFormDialog("#unrelease-confirm-dialog", {
        width: '400px',
        closeWhenSuccess: true,
        url: "/api/orders/bom/unrelease-bom-item",
        method: "POST",
        buttons: [
            { name: "save", icon: "check", alt: "yes", display: localizedTitles.yes || 'Yes' },
            { name: "cancel", icon: "xmark", alt: "no", display: localizedTitles.no || 'No' }
        ],
        body: function () {
            const selectedRowData = getSelectedRowData();
            if (!selectedRowData) {
                throw new Error('No row selected');
            }
            return {
                bomId: stateValue.id,
                requirementId: selectedRowData.customerRequirementId
            };
        }
    });

    state.unReleaseConfirmDialog.$form.on('closedWithSuccessedResponse.mf', (e, { response }) => {
        location.reload();
    });

    state.releaseRequirementHandler = new ReleaseRequirementHandler({
        bomData: state.data,
        bomId: stateValue.id,
        localization: {
            release: releaseRequirementLocalized?.release,
            cancel: releaseRequirementLocalized?.cancel,
            noData: releaseRequirementLocalized?.noData,
            supplier: releaseRequirementLocalized?.supplier,
            partNo: releaseRequirementLocalized?.partNo,
            buyPrice: releaseRequirementLocalized?.buyPrice,
            unitSellPrice: releaseRequirementLocalized?.unitSellPrice,
            warningMessage: releaseRequirementLocalized?.warningMessage,
            buyPriceGreaterText: releaseRequirementLocalized?.buyPriceGreaterText,
            ofSelectedPartGreaterThan: releaseRequirementLocalized?.ofSelectedPartGreaterThan,
            buyPriceEqualText: releaseRequirementLocalized?.buyPriceEqualText,
            ofSelectedPartEqualTo: releaseRequirementLocalized?.ofSelectedPartEqualTo
        }
    });

    const currentSelectedRowData = getSelectedRowData();
    if (currentSelectedRowData) {
        notifyReleaseRequirementHandler(currentSelectedRowData);
    }

    state.recallNoBidDialog = new LiteFormDialog("#recall-nobid-confirm-dialog", {
        width: '400px',
        closeWhenSuccess: true,
        url: "/api/orders/bom/recall-no-bid",
        method: "PUT",
        buttons: [
            { name: "save", icon: "check", alt: localizedTitles.yes || "yes", display: localizedTitles.yes || 'Yes' },
            { name: "cancel", icon: "xmark", alt: localizedTitles.yes || "no", display: localizedTitles.no || 'No' }
        ],
        body: function () {
            const selectedRowData = getSelectedRowData();
            if (!selectedRowData) {
                throw new Error('No row selected');
            }
            return {
                customerRequirementId: selectedRowData.customerRequirementId
            };
        }
    });

    state.recallNoBidDialog.$form.on('closedWithSuccessedResponse.mf', (e, { response }) => {
        if (state.hubRfqItemsTable?.table) {
            state.hubRfqItemsTable.table.ajax.reload(function () {
                setTimeout(updateButtonStates, 0);
            }, false);
            location.reload();
        }
    });

    state.applyPartWatchDialog = new LiteFormDialog("#apply-partwatch-dialog", {
        width: '500px',
        closeWhenSuccess: true,
        url: "/api/orders/bom/apply-partwatch",
        method: "POST",
        buttons: [
            { name: "save", icon: "check", alt: "yes", display: localizedTitles.apply || 'Apply' },
            { name: "cancel", icon: "xmark", alt: "no", display: localizedTitles.cancel || 'Cancel' }
        ],
        body: function () {
            const selectedRequirementIds = getSelectedRowIds();
            return {
                BOMId: stateValue.id,
                ReqIds: selectedRequirementIds.join(',')
            };
        }
    });

    state.addEditHubrfqForm = new EditHubrfqDialog('#edit-hubrfq-dialog', {
        closeWhenSuccess: true,
        bindScope: true,
        url: '/api/orders/bom/update-bom',
        method: 'PUT',
        buttons: [
            {
                name: 'save', alt: 'save', display: localizedTitles.Save || 'Save', icon: 'save'
            },
            {
                name: 'cancel', alt: 'cancel', display: localizedTitles.cancel || 'Cancel', icon: 'slash'
            },
        ],
        width: '60vw',
        maxHeight: $(window).height(),
        validationRules: {
            name: {
                required: function () {
                    return !state.addEditHubrfqForm.dataModel?.blnReqToPoHub;
                }
            },
            contactId: {
                min: function () {
                    return !state.addEditHubrfqForm.dataModel?.blnReqToPoHub ? 1 : 0;
                }
            },
            currency: {
                min: function () {
                    return !state.addEditHubrfqForm.dataModel?.blnReqToPoHub ? 1 : 0;
                }
            },
            txtQuoteRequired: {
                required: function () {
                    return !state.addEditHubrfqForm.dataModel?.blnReqToPoHub;
                }
            }
        },
        validationMessages: {
            name: { required: localizedTitles.errorEditMessage },
            contactId: { min: localizedTitles.errorEditMessage },
            currency: { min: localizedTitles.errorEditMessage },
            txtQuoteRequired: { required: localizedTitles.errorEditMessage },
        },
        errorPlacement: function (error, element) {
            const specialFields = ["txtQuoteRequired", "contactId", "currency"];
            if (specialFields.includes(element.attr("name"))) {
                error.insertAfter($(element).closest('div'));
            } else {
                error.insertAfter(element);
            }
        },
        body: function () {
            const formRequest = state.addEditHubrfqForm.buildSaveRequest(stateValue);

            return formRequest;
        },
    });

    state.addEditHubrfqForm.init();

    state.addItemDialog = new LiteFormDialog("#add-hubrfq-item-dialog", {
        width: "80vw",
        modal: true,
        maxHeight: $(window).height(),
        draggable: false,
        closeWhenSuccess: true,
        url: "/api/orders/bom/bom-customer-requirement",
        method: "PUT",
        buttons: [
            { name: "save", icon: "check", alt: "yes", display: localizedTitles.save },
            {
                name: "cancel", icon: "xmark", alt: "no", display: localizedTitles.cancel,
                onClick: function () {
                    resetAddItemDialog();
                }
            }
        ],
        body: function () {
            updateAddItemSelectedItems();

            const itemIdsString = state.addItemSelectedItems.map(item => item.customerRequirementId).join(',');
            return {
                reqIds: itemIdsString,
                bomId: stateValue.id
            }
        }
    })

    state.applyPartWatchBtn = new PartTableButton("#applypartwatch-btn", function () {
        const selectedIds = getSelectedRowIds();
        if (selectedIds.length === 0) {
            showToast('information', localizedTitles['Please select requirements first'] || 'Please select requirements first');
            return;
        }
        state.applyPartWatchDialog.open();
    });

    state.applyPartWatchDialog.$form.on('closedWithSuccessedResponse.mf', async function () {
        if (state.hubRfqItemsTable?.table) {
            state.hubRfqItemsTable.table.ajax.reload(function () {
                setTimeout(updateButtonStates, 0);
            });
        }
        // Reload Quote to Client table (sourcing results) after apply partwatch
        if (state.sourcingResultsSectionBox && state.currentSelectedPartDetailItem.id) {
            await state.sourcingResultsSectionBox.loadSourcingResultsAsync(state.currentSelectedPartDetailItem.id);
        }
    });

    const handleAddEditError = async function () {
        showToast('danger', window.localizedStrings.unexpectedError);
    };
    const handleEditHubrfqError = async function (e, response) {
        const isDuplicateName = response.Errors?.BOMName?.[0] === "UniqueMessageKey";

        if (isDuplicateName) {
            state.addEditHubrfqForm.validator.showErrors({
                name: localizedTitles.hubrfqDuplicateName
            });
        }

        state.addEditHubrfqForm.summaryDiv.show();

        if (!isDuplicateName) {
            showToast('danger', window.localizedStrings.unexpectedError);
        }
    };

    state.bomQualificationDialog.$form.on('errorResponse.mf', handleAddEditError);
    state.bomQualificationDialog.$form.on('errorRequest.mf', handleAddEditError);
    state.bomQualificationDialog.$form.on('closedWithSuccessedResponse.mf', async function () {
        bomQualityBox.find('.section-box-refesh-button').trigger('click');
    })

    state.bomQualificationDialogDelete.$form.on('errorResponse.mf', handleAddEditError);
    state.bomQualificationDialogDelete.$form.on('errorRequest.mf', handleAddEditError);
    state.bomQualificationDialogDelete.$form.on('closedWithSuccessedResponse.mf', async function () {
        bomQualityBox.find('.section-box-refesh-button').trigger('click');
    })

    state.addEditHubrfqForm.$form.on('errorResponse.mf', handleAddEditError);
    state.addEditHubrfqForm.$form.on('errorRequest.mf', handleEditHubrfqError);
    state.addEditHubrfqForm.$form.on('closedWithSuccessedResponse.mf', async function () {
        shouldUpdateTitle = true;
        hubrfqDetailsBox.find('.section-box-refesh-button').trigger('click');
    })

    state.addItemDialog.$form.on('errorResponse.mf', handleAddEditError);
    state.addItemDialog.$form.on('errorRequest.mf', handleAddEditError);
    state.addItemDialog.$form.on('closedWithSuccessedResponse.mf', async function () {
        state.hubRfqItemsTable.table.ajax.reload(function () {
                setTimeout(updateButtonStates, 0);
            });
        $('#bom-details-status').text("OPEN");
    })

    state.addCommunicationNoteDialog = new LiteFormDialog("#add-communication-note-dialog", {
        width: '600px',
        closeWhenSuccess: true,
        url: "/api/orders/bom/communication-note",
        method: "POST",
        buttons: [
            { name: "save", icon: "check", alt: "save", display: localizedTitles.save || 'Save' },
            { name: "cancel", icon: "xmark", alt: "cancel", display: localizedTitles.cancel || 'Cancel' }
        ],
        validationRules: {
            expediteNotes: {
                required: true,
                maxlength: 500
            }
        },
        validationMessages: {
            expediteNotes: {
                required: window.localizedStrings.requiredField
            }
        },
        errorPlacement: function (error, element) {
            if (element.attr("name") === "expediteNotes") {
                error.insertAfter($(element).closest("div"));
            }
        },
        body: function () {
            const expediteNotes = $("#add-communication-note-dialog textarea[name='expediteNotes']").val();
            return {
                HUBRFQId: stateValue.id,
                AddNotes: expediteNotes,
                HUBRFQName: state.data.name,
                HubrfqCode: state.data.code,
                CompanyNo: state.data.companyNo,
                CompanyName: state.data.company,
                ContactName: state.data.contactName,
                Requestedby: state.data.requestToPOHubBy,
                UpdateByPH: state.data.updateByPH,
                Contact2No: state.data.contact2Id,
                ReqSalesPerson: state.data.reqSalesPerson || "",
            };
        }
    });

    state.addCommunicationNoteDialog.$form.on('closedWithSuccessedResponse.mf', (e, { response }) => {
        // Refresh communication notes table
        if (state.communicationTable?.table) {
            state.communicationTable.table.ajax.reload();
        }
    });
}

function updateCustomerRequirementsTitle(count) {
    try {
        const $hubRfqItemBox = $('#hub-rfq-item-box');
        const $titleElement = $hubRfqItemBox.find('.section-box-title');
        const tabText = count > 0 ? `${localizedTitles.customerRequirements} (${count})` : localizedTitles.customerRequirements;

        $titleElement.text(localizedTitles.hubrfqItems);
        $hubRfqItemBox.find('.customer-req-count-below-btns').remove();

        const $tabsWrapper = $hubRfqItemBox.find('.customer-req-tabs-wrapper');

        if ($tabsWrapper.length === 0) {
            createCustomerRequirementsTab($hubRfqItemBox, tabText);
        } else {
            $hubRfqItemBox.find('[id^="customer-req-tab-btn"]').text(tabText).css('font-weight', 'bold');
        }
    } catch (error) {
        console.error('Error updating customer requirements title:', error);
    }
}

function createCustomerRequirementsTab($hubRfqItemBox, tabText) {
    const $template = $('#customer-requirements-tab-template');
    if ($template.length === 0) {
        console.error(localizedTitles.customerRequirementsTabTemplateNotFound);
        return;
    }

    const $tabsHtml = $template.clone().removeClass('d-none');
    $tabsHtml.find('#customer-req-tab-btn').text(tabText).css('font-weight', 'bold');

    const $contentSection = $hubRfqItemBox.find('#hub-rfq-items-content');
    if ($contentSection.length > 0) {
        $contentSection.prepend($tabsHtml);

        const $tabContent = $contentSection.find('#customer-req-content');
        const $tableWrapper = $contentSection.find('.table-responsive').detach();

        $tabContent.append($tableWrapper);
    }
}

function showAs6081ComplianceNotification(show, message = '') {
    const errorSummarySelector = "#hub-rfq-item-box #hub-rfq-items-content .form-error-summary";
    toggleFormErrorSummary(errorSummarySelector, show, message, true);
}

function showDeliveryDateProductValidationNotification(show, message = '') {
    // Use the pre-existing HTML element for delivery validation
    const errorSummarySelector = "#hub-rfq-item-box #hub-rfq-items-content .form-error-summary.delivery-validation";
    toggleFormErrorSummary(errorSummarySelector, show, message);
}

async function initAssignmentHistoryTable() {
    const assignmentHistoryBox = $("#assignment-history-box");
    assignmentHistoryBox.section_box("option", { loading: true });

    state.assignmentHistoryTable = new LiteDatatable('#assignment-history-table', {
        ajax: {
            url: `/api/orders/bom/${stateValue.id}/assignment-history`,
            type: 'GET',
        },
        language: {
            emptyTable: `<i>${localizedTitles.noDataWasFound}</i>`
        },
        searching: false,
        columnDefs: [
            { type: 'string', targets: '_all' }
        ],
        columns: [
            {
                title: localizedTitles.documentNumber,
                data: "documentNumber",
                width: "20%",
                className: 'text-wrap text-break',
                render: function (data, type, row) {
                    return GlobalTrader.StringHelper.setCleanTextValue(data);
                }
            },
            {
                title: localizedTitles.assignedTo,
                data: "assignedTo",
                width: "20%",
                className: 'text-wrap text-break',
                render: function (data, type, row) {
                    return GlobalTrader.StringHelper.setCleanTextValue(data);
                }
            },
            {
                title: localizedTitles.assignedBy,
                data: "assignedBy",
                width: "20%",
                className: 'text-wrap text-break',
                render: function (data, type, row) {
                    return GlobalTrader.StringHelper.setCleanTextValue(data);
                }
            },
            {
                title: localizedTitles.logDate,
                data: "logDate",
                width: "20%",
                className: 'text-wrap text-break',
                render: function (data, type, row) {
                    return GlobalTrader.StringHelper.setCleanTextValue(data);
                }
            },
            {
                title: localizedTitles.assignmentType,
                data: "assignmentType",
                width: "20%",
                className: 'text-wrap text-break',
                render: function (data, type, row) {
                    return GlobalTrader.StringHelper.setCleanTextValue(data);
                }
            }
        ],
        disableSelect: true,
        resizeConfig: { numberOfRowToShow: 5 },
        enableResize: true
    });

    state.assignmentHistoryTable.on('xhr.dt', function (e, settings, json, xhr) {
        assignmentHistoryBox.section_box("option", { loading: false });
    });

    state.assignmentHistoryTable.init();
}

function toggleFormErrorSummary(selector, show, message = '', useFirst = false) {
    const errorSummary = useFirst ? $(selector).first() : $(selector);

    if (show && message) {
        errorSummary.removeClass("d-none").addClass("d-flex");
        errorSummary.find("p").text(message);
    } else {
        errorSummary.addClass("d-none").removeClass("d-flex");
        errorSummary.find("p").text("");
    }
}

let mainInfoButtonGroups = {
    sourcingResult: $('#sourcing-results-btn-group'),
    sourcingFindPartNo: $('#search-part-number-btn'),
    sourcingAddSourcingInfo: $('#add-sourcing-info'),
    sourcingAddTrusted: $('#add-trusted'),
    sourcingAddOffer: $('#add-offer'),
}

function setUpMainInfoButtonVisible() {
    if (userContext.isGSAUser && userContext.isDifferentClient && !userContext.isDMCC && !userContext.gsaUserHasEditPermission) {
        Object.values(mainInfoButtonGroups).forEach($btnGroup => $btnGroup.addClass('d-none'));
    }
}
function _initMagicBoxDialog() {
    state.magicBoxDialogControl = new FixedDialogComponent('dialogContainer', '600px', '20px', '5%');
}


async function openMagicBoxDialog() {
    let partNo = stateValue.selectedRequirementDetails.part;
    if (!partNo) return;
    let data = await _getPartDetail(partNo);
    let formatedData = formatData(data);
    data.partNoMagicBox = partNo;
    data.companyName = stateValue.selectedRequirementDetails.companyName;
    BindingService.bindToElements("#dialogContainer", formatedData);
    state.magicBoxDialogControl.open();
}

async function _getPartDetail(partNo) {
    const response = await GlobalTrader.ApiClient.getAsync(`/orders/customer-requirements/get-part-detail`, {
        partNo: partNo,
        companyNo: stateValue.selectedRequirementDetails.companyId
    });
    return response.data.length > 0 ? response.data[0] : {};
}

function formatData(data) {
    data.lastSoldOn = GlobalTrader.StringHelper.getTrimDefaultString(data.lastSoldOn);
    data.lastSoldtoCustomer = GlobalTrader.StringHelper.getTrimDefaultString(data.lastSoldtoCustomer);
    data.lastSupplierType = GlobalTrader.StringHelper.getTrimDefaultString(data.lastSupplierType);
    data.lastDatecode = GlobalTrader.StringHelper.getTrimDefaultString(data.lastDatecode);
    data.lastDatePurchased = GlobalTrader.StringHelper.getTrimDefaultString(data.lastDatePurchased);
    data.lastCustomerRegion = GlobalTrader.StringHelper.getTrimDefaultString(data.lastCustomerRegion);
    data.custLastSoldOn = GlobalTrader.StringHelper.getTrimDefaultString(data.custLastSoldOn);
    data.custLastSoldtoCustomer = GlobalTrader.StringHelper.getTrimDefaultString(data.custLastSoldtoCustomer);
    data.custSupplierType = GlobalTrader.StringHelper.getTrimDefaultString(data.custSupplierType);
    data.custDatecode = GlobalTrader.StringHelper.getTrimDefaultString(data.custDatecode);
    data.custDatePurchased = GlobalTrader.StringHelper.getTrimDefaultString(data.custDatePurchased);
    data.customerRegion = GlobalTrader.StringHelper.getTrimDefaultString(data.customerRegion);
    data.lastPricePaidByCustDisplay = `${data.lastPricePaidByCust} ${data.paidByCustCurrencyCode}`;
    data.custLastAvgReboundPriceSoldDisplay = `${data.custLastAvgReboundPriceSold} ${data.custPaidByCustCurrencyCode}`;
    return data;
}


async function initDragDropImageSection(sourcingResultId) {
    if (!sourcingResultId) return;

    imageDocumentsSectionComponent.isReadOnly = imageDocumentsSectionComponent.isReadOnly === true || imageDocumentsSectionComponent.isReadOnly === "true";
    if (!state.BomImagesDocumentsManager) {
        state.BomImagesDocumentsManager = new MediaDocumentManager({
            documentSectionComponent: imageDocumentsSectionComponent,
            id: sourcingResultId,
            uploadDialogParams: {
                dialogSelector: imageDocumentsUploadComponent.dialogId,
                formSelector: imageDocumentsUploadComponent.formId,
                allowedFileExtensions: imageDocumentsUploadComponent.allowedFileExtensions.split(", "),
            },
            removeDocumentDialogParams: {
                dialogSelector: imageDocumentsRemoveComponent.dialogId,
                sectionName: imageDocumentsRemoveComponent.sectionName,
            },
        });
        await state.BomImagesDocumentsManager.initialize();
    }
    else {
        state.BomImagesDocumentsManager.reloadDocumentsSectionBox(sourcingResultId);
    }

}

function updateAddItemDialogButtons() {
    updateAddItemSelectedItems();
    $('#add-hubrfq-item-dialog').parent().find('.ui-dialog-buttonpane button.btn-save').prop("disabled", state.addItemSelectedItems.length === 0);
}

function setupaddNewHuberfqFormLayout() {
    $("#add-hubrfq-item-filter h5").css("font-weight", "normal");
    $("#add-hubrfq-item-filter h5").after(`
            <span>
                <span class="fw-bold me-1 required">*</span>${localizedTitles.denoteRequired}
            </span>
        `);
    $("#add-hubrfq-item-filter-content-show .filter-form")
        .append(`<div class="col-6 form-control-wrapper m-auto"></div>`);

    $("#add-hubrfq-item-filter .col-2").removeClass("col-2").addClass("col-3");
}

async function initAddItemFilter() {
    state.addItemFilter = new TableFilterComponent(
        "#add-hubrfq-item-filter",
        localizedTitles.hubRFQFilterTitle,
        {
            inputConfigs: ADD_HUBRFQ_ITEM_FILTER_INPUTS,
            showButtons: false,
            wrapperClass: "bg-none m-0 p-0",
        }
    );

    state.addItemFilter.on("controlchanged.mtf", () => {
        const hasFilterValues = checkIfAnyAddItemFilterActive();
        $("#search-hubrfq-item-btn").prop("disabled", !hasFilterValues);
    });

    await state.addItemFilter.init();

    setupaddNewHuberfqFormLayout();
}

function checkIfAnyAddItemFilterActive() {
    const filterValues = state.addItemFilter.getAllValue();

    return Object.keys(filterValues).some((key) => {
        const filterValue = filterValues[key];
        return (
            filterValue.isOn
        );
    });
}

function setupAddItemHubrfqItemsTable() {
    state.addItemTable = new AddItemTable({
        onUpdateDialogButtons: updateAddItemDialogButtons
    });
    state.addItemTable.init();
}

function updateAddItemSelectedItems() {
    if (state.addItemTable) {
        state.addItemSelectedItems = state.addItemTable.getSelectedItems();
    }
}

function setupAddItemEventListeners() {
    window.openAddHubrfqItemDialog = function () {
        $("#add-hubrfq-item-dialog").dialog("open");

    };

    $("#search-hubrfq-item-btn")?.button()?.on("click", async (event) => {
        event.stopPropagation();
        await state.addItemTable.search(state.addItemFilter.getAllValue());
    });
}


function resetAddItemDialog() {
    state.addItemSelectedItems = [];
    $("#add-hubrfq-item-table-wrapper").addClass("d-none");

    if (state.addItemFilter) {
        state.addItemFilter.reset();
    }

    if (state.addItemTable) {
        state.addItemTable.clearTable();
    }
}

function enableAddHubrfqItemBtn() {
    $("#add-btn").prop("disabled", state.data.blnReqToPoHub || state.data.inActive || state.data.isClosed);
}

async function updateBomStatus(bomId) {
    try {
        const response = await GlobalTrader.ApiClient.getAsync(`/orders/bom/${bomId}/status`);

        $('#bom-details-status').text(response.data ?? "");

        return response.data || "";
    } catch (error) {
        console.error('Error fetching BOM status:', error);
        return null;
    }
}