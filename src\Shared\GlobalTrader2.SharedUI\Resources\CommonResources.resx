﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Recently Viewed" xml:space="preserve">
    <value>Recently Viewed</value>
  </data>
  <data name="Quick Jump" xml:space="preserve">
    <value>Quick Jump</value>
  </data>
  <data name="Selection" xml:space="preserve">
    <value>Selection</value>
  </data>
  <data name="Login Page" xml:space="preserve">
    <value>Login Page</value>
  </data>
  <data name="Home" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="All Companies" xml:space="preserve">
    <value>All Companies</value>
  </data>
  <data name="Add New Company" xml:space="preserve">
    <value>Add New Company</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="SecurityUsers" xml:space="preserve">
    <value>Security Users</value>
  </data>
  <data name="ApplicationSettings" xml:space="preserve">
    <value>Application Settings</value>
  </data>
  <data name="Setup" xml:space="preserve">
    <value>Setup</value>
  </data>
  <data name="ModifySuccess" xml:space="preserve">
    <value>Your changes were saved</value>
  </data>
  <data name="Disable" xml:space="preserve">
    <value>Disable</value>
  </data>
  <data name="Enable" xml:space="preserve">
    <value>Enable</value>
  </data>
  <data name="TitleDialogDisableUser" xml:space="preserve">
    <value>Disable User</value>
  </data>
  <data name="TitleDialogEnableUser" xml:space="preserve">
    <value>Enable User</value>
  </data>
  <data name="Profile" xml:space="preserve">
    <value>Profile</value>
  </data>
  <data name="SecurityGroups" xml:space="preserve">
    <value>Security Groups</value>
  </data>
  <data name="DisableMessage" xml:space="preserve">
    <value>Are you sure you would like to disable this</value>
  </data>
  <data name="EnableMessage" xml:space="preserve">
    <value>Are you sure you would like to enable this</value>
  </data>
  <data name="SecurityUser" xml:space="preserve">
    <value>Security User</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Select</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="CompanySettings" xml:space="preserve">
    <value>Company Settings</value>
  </data>
  <data name="Terms" xml:space="preserve">
    <value>Terms</value>
  </data>
  <data name="Transfer Accounts" xml:space="preserve">
    <value>Transfer Accounts</value>
  </data>
  <data name="Teams" xml:space="preserve">
    <value>Teams</value>
  </data>
  <data name="TeamMembers" xml:space="preserve">
    <value>Team Members</value>
  </data>
  <data name="Enter the details and press" xml:space="preserve">
    <value>Enter the details and press</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="denotes a required field" xml:space="preserve">
    <value>denotes a required field</value>
  </data>
  <data name="Character count" xml:space="preserve">
    <value>Character count</value>
  </data>
  <data name="chrs max" xml:space="preserve">
    <value>chrs max</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="and" xml:space="preserve">
    <value>and</value>
  </data>
  <data name="securityGroup_Group" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="securityGroup_Members" xml:space="preserve">
    <value>Members</value>
  </data>
  <data name="securityUser_Accounts" xml:space="preserve">
    <value>Account</value>
  </data>
  <data name="securityUser_Division" xml:space="preserve">
    <value>Division</value>
  </data>
  <data name="securityUser_Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="securityUser_Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="securityUser_Team" xml:space="preserve">
    <value>Team</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="UpdatedBy" xml:space="preserve">
    <value>Updated By</value>
  </data>
  <data name="LocalCurrency" xml:space="preserve">
    <value>Local Currency</value>
  </data>
  <data name="ExchangeRateHistory" xml:space="preserve">
    <value> Exchange Rate History</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>Warehouse</value>
  </data>
  <data name="Warehouse_Division" xml:space="preserve">
    <value>Division</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="Printer" xml:space="preserve">
    <value>Printer</value>
  </data>
  <data name="AddLocalCurrencyTitle" xml:space="preserve">
    <value>Add New Local Currency</value>
  </data>
  <data name="AddLocalCurrencyMsgAdd" xml:space="preserve">
    <value>Enter the details of the Local Currency and press</value>
  </data>
  <data name="MasterCurrency" xml:space="preserve">
    <value>Master Currency</value>
  </data>
  <data name="IsInactive" xml:space="preserve">
    <value>Inactive?</value>
  </data>
  <data name="ExchangeRate" xml:space="preserve">
    <value>Exchange Rate</value>
  </data>
  <data name="Warehouse_LocalCurrency" xml:space="preserve">
    <value>Local Currency</value>
  </data>
  <data name="CommunicationLogTypes" xml:space="preserve">
    <value>Communication Log Types</value>
  </data>
  <data name="CommunicationLogTypeName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Master Country List" xml:space="preserve">
    <value>Master Country List</value>
  </data>
  <data name="Set Default" xml:space="preserve">
    <value>Set Default</value>
  </data>
  <data name="Are you sure you would like to set this" xml:space="preserve">
    <value>Are you sure you would like to set this</value>
  </data>
  <data name="as default for new POs and CRMAs" xml:space="preserve">
    <value>as default for new POs and CRMAs</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Loading</value>
  </data>
  <data name="Edit Local Currency" xml:space="preserve">
    <value>Edit Local Currency Details</value>
  </data>
  <data name="Enter the changed details for the Local Currency and press" xml:space="preserve">
    <value>Enter the changed details for the Local Currency and press</value>
  </data>
  <data name="CompanyType" xml:space="preserve">
    <value>Company Type</value>
  </data>
  <data name="Global Security Groups" xml:space="preserve">
    <value>Global Security Groups</value>
  </data>
  <data name="unauthorized" xml:space="preserve">
    <value>You are unauthorized</value>
  </data>
  <data name="SecuritySettings" xml:space="preserve">
    <value>Security Settings</value>
  </data>
  <data name="SequenceNumbers" xml:space="preserve">
    <value>Sequence Numbers</value>
  </data>
  <data name="Bulk Invoice Email Composer" xml:space="preserve">
    <value>Bulk Invoice Email Composer</value>
  </data>
  <data name="Warnings" xml:space="preserve">
    <value>Warnings</value>
  </data>
  <data name="Warning Name" xml:space="preserve">
    <value>Warning Name</value>
  </data>
  <data name="Warning Message" xml:space="preserve">
    <value>Warning Message</value>
  </data>
  <data name="Apply To Category" xml:space="preserve">
    <value>Apply To Category</value>
  </data>
  <data name="Apply To" xml:space="preserve">
    <value>Apply To</value>
  </data>
  <data name="Warning Messages" xml:space="preserve">
    <value>Warning Messages</value>
  </data>
  <data name="View Terms" xml:space="preserve">
    <value>View Terms</value>
  </data>
  <data name="View Restricted Manufacturer" xml:space="preserve">
    <value>View Restricted Manufacturer</value>
  </data>
  <data name="View Global Product" xml:space="preserve">
    <value>View Global Product</value>
  </data>
  <data name="View Company Detail" xml:space="preserve">
    <value>View Company Detail</value>
  </data>
  <data name="PpvBomQualification" xml:space="preserve">
    <value>PPV/ BOM Qualification</value>
  </data>
  <data name="ClientInvoiceHeaderImage" xml:space="preserve">
    <value>Client Invoice Header Image</value>
  </data>
  <data name="Item" xml:space="preserve">
    <value>Item</value>
  </data>
  <data name="NextNumber" xml:space="preserve">
    <value>Next Number</value>
  </data>
  <data name="No results found" xml:space="preserve">
    <value>No results found</value>
  </data>
  <data name="Global Security Settings" xml:space="preserve">
    <value>Global Security Settings</value>
  </data>
  <data name="EditMyProfile" xml:space="preserve">
    <value>Edit My Profile</value>
  </data>
  <data name="Edit My Profile" xml:space="preserve">
    <value>Edit My Profile</value>
  </data>
  <data name="MailMessages" xml:space="preserve">
    <value>Mail Messages</value>
  </data>
  <data name="MailMessageGroups" xml:space="preserve">
    <value>Mail Message Groups</value>
  </data>
  <data name="ToDoList" xml:space="preserve">
    <value>ToDoList</value>
  </data>
  <data name="Packages" xml:space="preserve">
    <value>Packages</value>
  </data>
  <data name="Restricted Manufacturer" xml:space="preserve">
    <value>Restricted Manufacturer</value>
  </data>
  <data name="Add/ Bulk Add" xml:space="preserve">
    <value>Add/ Bulk Add</value>
  </data>
  <data name="Edit/ Bulk Edit" xml:space="preserve">
    <value>Edit/ Bulk Edit</value>
  </data>
  <data name="Inactivate/ Bulk Inactivate" xml:space="preserve">
    <value>Inactivate/ Bulk Inactivate</value>
  </data>
  <data name="Back" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="Client Invoice Header" xml:space="preserve">
    <value>Client Invoice Header</value>
  </data>
  <data name="MasterAppSettings" xml:space="preserve">
    <value>Master Application Settings</value>
  </data>
  <data name="DocumentMB" xml:space="preserve">
    <value>MB</value>
  </data>
  <data name="ADEmail" xml:space="preserve">
    <value>AD Email</value>
  </data>
  <data name="ADUsername" xml:space="preserve">
    <value>AD Username</value>
  </data>
  <data name="BoxTitle" xml:space="preserve">
    <value>Profile</value>
  </data>
  <data name="By" xml:space="preserve">
    <value>by</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="Extension" xml:space="preserve">
    <value>Extension</value>
  </data>
  <data name="Fax" xml:space="preserve">
    <value>Fax</value>
  </data>
  <data name="GroupAccess" xml:space="preserve">
    <value>Group Access</value>
  </data>
  <data name="JobTitle" xml:space="preserve">
    <value>Job Title</value>
  </data>
  <data name="LastUpdated" xml:space="preserve">
    <value>Last updated</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="Mobile" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Team</value>
  </data>
  <data name="Telephone" xml:space="preserve">
    <value>Telephone</value>
  </data>
  <data name="Divisions" xml:space="preserve">
    <value>Divisions</value>
  </data>
  <data name="Email Campaign" xml:space="preserve">
    <value>Email Campaign</value>
  </data>
  <data name="Task Date From" xml:space="preserve">
    <value>Task Date From</value>
  </data>
  <data name="Task Date To" xml:space="preserve">
    <value>Task Date To</value>
  </data>
  <data name="Task Reminder Date" xml:space="preserve">
    <value>Task Reminder Date</value>
  </data>
  <data name="Task Title" xml:space="preserve">
    <value>Task Title</value>
  </data>
  <data name="Task Type" xml:space="preserve">
    <value>Task Type</value>
  </data>
  <data name="Task Status" xml:space="preserve">
    <value>Task Status</value>
  </data>
  <data name="Customer Name" xml:space="preserve">
    <value>Customer Name</value>
  </data>
  <data name="Salesperson" xml:space="preserve">
    <value>Salesperson</value>
  </data>
  <data name="LoginTimeout" xml:space="preserve">
    <value>Login timeout</value>
  </data>
  <data name="ShowMessageAlert" xml:space="preserve">
    <value>Show Message Alert?</value>
  </data>
  <data name="DefaultLanguage" xml:space="preserve">
    <value>Default Language</value>
  </data>
  <data name="DefaultHomepageTab" xml:space="preserve">
    <value>Default Homepage Tab</value>
  </data>
  <data name="BackgroundImage" xml:space="preserve">
    <value>Background Image</value>
  </data>
  <data name="SaveSearchesBydefault" xml:space="preserve">
    <value>Save searches by default?</value>
  </data>
  <data name="DefaultBrowsePageView" xml:space="preserve">
    <value>Default Browse Page View</value>
  </data>
  <data name="DefaultBrowsePageSize" xml:space="preserve">
    <value>Default Browse Page Size</value>
  </data>
  <data name="RecentlyViewedPages" xml:space="preserve">
    <value>Recently viewed pages</value>
  </data>
  <data name="ReceiveEmail" xml:space="preserve">
    <value>Receive Email?</value>
  </data>
  <data name="ToDoListType" xml:space="preserve">
    <value>To Do List Type</value>
  </data>
  <data name="Sales Person" xml:space="preserve">
    <value>Sales Person</value>
  </data>
  <data name="Max Document Size" xml:space="preserve">
    <value>Max Document Size</value>
  </data>
  <data name="EditPreferences" xml:space="preserve">
    <value>Edit Preferences Details</value>
  </data>
  <data name="Preferences" xml:space="preserve">
    <value>Preferences</value>
  </data>
  <data name="Please enter a numeric value greater than or equal to" xml:space="preserve">
    <value>Please enter a numeric value greater than or equal to</value>
  </data>
  <data name="Please enter a numeric value less than or equal to" xml:space="preserve">
    <value>Please enter a numeric value less than or equal to</value>
  </data>
  <data name="Countries" xml:space="preserve">
    <value>Countries</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="Starts with" xml:space="preserve">
    <value>Starts with</value>
  </data>
  <data name="Ends with" xml:space="preserve">
    <value>Ends with</value>
  </data>
  <data name="Contains" xml:space="preserve">
    <value>Contains</value>
  </data>
  <data name="MarkComplete" xml:space="preserve">
    <value>Mark Complete</value>
  </data>
  <data name="MarkIncomplete" xml:space="preserve">
    <value>Mark Incomplete</value>
  </data>
  <data name="MarkItemComplete" xml:space="preserve">
    <value>Mark Item(s) Complete</value>
  </data>
  <data name="MarkItemIncomplete" xml:space="preserve">
    <value>Mark Item(s) Incomplete</value>
  </data>
  <data name="Lock to save your search, unlock to clear it" xml:space="preserve">
    <value>Lock to save your search, unlock to clear it</value>
  </data>
  <data name="Your search was cancelled" xml:space="preserve">
    <value>Your search was cancelled</value>
  </data>
  <data name="MasterTaxes" xml:space="preserve">
    <value>Master Taxes</value>
  </data>
  <data name="Please enter a date from today or in the future." xml:space="preserve">
    <value>Please enter a date from today or in the future.</value>
  </data>
  <data name="Checkbox" xml:space="preserve">
    <value>Checkbox</value>
  </data>
  <data name="Stock Log Reasons" xml:space="preserve">
    <value>Stock Log Reasons</value>
  </data>
  <data name="StockLogReason" xml:space="preserve">
    <value>Stock Log Reason</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>Products</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="From" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="Subject" xml:space="preserve">
    <value>Subject</value>
  </data>
  <data name="Mail Groups" xml:space="preserve">
    <value>Mail Groups</value>
  </data>
  <data name="Reminder" xml:space="preserve">
    <value>Reminder</value>
  </data>
  <data name="Due Date" xml:space="preserve">
    <value>Due Date</value>
  </data>
  <data name="Open Item" xml:space="preserve">
    <value>Open Item</value>
  </data>
  <data name="Dismiss" xml:space="preserve">
    <value>Dismiss</value>
  </data>
  <data name="Snooze" xml:space="preserve">
    <value>Snooze</value>
  </data>
  <data name="Client" xml:space="preserve">
    <value>Client</value>
  </data>
  <data name="Send" xml:space="preserve">
    <value>Send</value>
  </data>
  <data name="Sourcing Links" xml:space="preserve">
    <value>Sourcing Links</value>
  </data>
  <data name="Url" xml:space="preserve">
    <value>Url</value>
  </data>
  <data name="Master Currency List" xml:space="preserve">
    <value>Master Currency List</value>
  </data>
  <data name="DocumentSizes" xml:space="preserve">
    <value>Document File Size</value>
  </data>
  <data name="Export To Excel" xml:space="preserve">
    <value>Export To Excel</value>
  </data>
  <data name="To Do List" xml:space="preserve">
    <value>To Do List</value>
  </data>
  <data name="Printed Documents" xml:space="preserve">
    <value>Printed Documents</value>
  </data>
  <data name="Shipping Methods" xml:space="preserve">
    <value>Shipping Methods</value>
  </data>
  <data name="Tax" xml:space="preserve">
    <value>Tax</value>
  </data>
  <data name="CurrentRate" xml:space="preserve">
    <value>Current Rate</value>
  </data>
  <data name="CurrentRate2" xml:space="preserve">
    <value>Current Rate 2</value>
  </data>
  <data name="TaxCode" xml:space="preserve">
    <value>Tax Code</value>
  </data>
  <data name="PurchaseTaxCode" xml:space="preserve">
    <value>Purchase Tax Code</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="AdminNotes" xml:space="preserve">
    <value>Admin Notes</value>
  </data>
  <data name="RateHistory" xml:space="preserve">
    <value>Rate History</value>
  </data>
  <data name="EditRates" xml:space="preserve">
    <value>Edit Rates</value>
  </data>
  <data name="DeleteFutureRate" xml:space="preserve">
    <value>Delete Future Rate</value>
  </data>
  <data name="Currencies" xml:space="preserve">
    <value>Currencies</value>
  </data>
  <data name="ManageHeader" xml:space="preserve">
    <value>Manage Header</value>
  </data>
  <data name="Remove Header" xml:space="preserve">
    <value>Remove Header</value>
  </data>
  <data name="Task Category" xml:space="preserve">
    <value>Task Category</value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="You have new message(s) from {0}" xml:space="preserve">
    <value>You have new message(s) from {0}</value>
    <comment>You have new message from {0}</comment>
  </data>
  <data name="You have {0} new message(s)" xml:space="preserve">
    <value>You have {0} new message(s)</value>
  </data>
  <data name="New Message(s)" xml:space="preserve">
    <value>New Message(s)</value>
  </data>
  <data name="Star Rating" xml:space="preserve">
    <value>Star Rating</value>
  </data>
  <data name="TaxName" xml:space="preserve">
    <value>Tax Name</value>
  </data>
  <data name="Rate" xml:space="preserve">
    <value>Rate</value>
  </data>
  <data name="Rate2" xml:space="preserve">
    <value>Rate 2</value>
  </data>
  <data name="AS6081" xml:space="preserve">
    <value>AS6081</value>
  </data>
  <data name="Certificate" xml:space="preserve">
    <value>Certificate</value>
  </data>
  <data name="CloseReason" xml:space="preserve">
    <value>Close Reasons</value>
  </data>
  <data name="CountingMethod" xml:space="preserve">
    <value>Counting Methods</value>
  </data>
  <data name="ECCN" xml:space="preserve">
    <value>ECCN</value>
  </data>
  <data name="EntertainmentType" xml:space="preserve">
    <value>Entertainment Type</value>
  </data>
  <data name="Incoterm" xml:space="preserve">
    <value>Incoterms</value>
  </data>
  <data name="IndustryType" xml:space="preserve">
    <value>Industry Types</value>
  </data>
  <data name="MasterCountry" xml:space="preserve">
    <value>Master Country List</value>
  </data>
  <data name="MasterLogin" xml:space="preserve">
    <value>Master Login</value>
  </data>
  <data name="MasterStatus" xml:space="preserve">
    <value>Master Status</value>
  </data>
  <data name="Global Products" xml:space="preserve">
    <value>Global Products</value>
  </data>
  <data name="RootCauseCode" xml:space="preserve">
    <value>Root Cause Code</value>
  </data>
  <data name="MasterCurrencyList" xml:space="preserve">
    <value>Master Currency List</value>
  </data>
  <data name="CompanyTypes" xml:space="preserve">
    <value>Company Types</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>First Name</value>
  </data>
  <data name="Surname" xml:space="preserve">
    <value>Surname</value>
  </data>
  <data name="Tel" xml:space="preserve">
    <value>Tel</value>
  </data>
  <data name="Ext" xml:space="preserve">
    <value>Ext</value>
  </data>
  <data name="SendShipmentNotification?" xml:space="preserve">
    <value>Send Shipment Notification?</value>
  </data>
  <data name="Tel2" xml:space="preserve">
    <value>Tel 2</value>
  </data>
  <data name="MobileTel" xml:space="preserve">
    <value> Mobile Tel</value>
  </data>
  <data name="TextOnlyEmail?" xml:space="preserve">
    <value>Text Only Email?</value>
  </data>
  <data name="Nickname" xml:space="preserve">
    <value>Nickname</value>
  </data>
  <data name="CompanyAddress" xml:space="preserve">
    <value>Company Address</value>
  </data>
  <data name="FinanceContact?" xml:space="preserve">
    <value>Finance Contact?</value>
  </data>
  <data name="Contact_FirstName" xml:space="preserve">
    <value>First Name</value>
  </data>
  <data name="Contact_LastName" xml:space="preserve">
    <value>Last Name</value>
  </data>
  <data name="Contact_Tel" xml:space="preserve">
    <value>Tel</value>
  </data>
  <data name="Contact_Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="Contact_Company" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="Contact_Salesman" xml:space="preserve">
    <value>Salesman</value>
  </data>
  <data name="Contact_ClientName" xml:space="preserve">
    <value>Client Name</value>
  </data>
  <data name="my-tab" xml:space="preserve">
    <value>My</value>
  </data>
  <data name="team-tab" xml:space="preserve">
    <value>Team</value>
  </data>
  <data name="division-tab" xml:space="preserve">
    <value>Divsion</value>
  </data>
  <data name="company-tab" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="Contact_ContactName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Contact_Client" xml:space="preserve">
    <value>Client</value>
  </data>
  <data name="Contact_Title" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="ROHSCompliant" xml:space="preserve">
    <value>RoHS Compliant</value>
  </data>
  <data name="ROHSNonCompliant" xml:space="preserve">
    <value>RoHS Non-compliant</value>
  </data>
  <data name="ROHSExempt" xml:space="preserve">
    <value>RoHS Exempt</value>
  </data>
  <data name="ROHS2" xml:space="preserve">
    <value>ROHS2</value>
  </data>
  <data name="ROHS56" xml:space="preserve">
    <value>RoHS 5/6</value>
  </data>
  <data name="ROHS66" xml:space="preserve">
    <value>RoHS 6/6</value>
  </data>
  <data name="CustomerRequirements" xml:space="preserve">
    <value>CustomerRequirements</value>
    <comment>Customer Requirements</comment>
  </data>
  <data name="Manufacturer Name" xml:space="preserve">
    <value>Manufacturer Name</value>
  </data>
  <data name="Conflict Resource" xml:space="preserve">
    <value>Conflict Resource</value>
  </data>
  <data name="Group Name" xml:space="preserve">
    <value>Group Name</value>
  </data>
  <data name="Group Code" xml:space="preserve">
    <value>Group Code</value>
  </data>
  <data name="API Imported Data" xml:space="preserve">
    <value>API Imported Data</value>
  </data>
  <data name="Manufacturer_Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Manufacturer_Code" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="Manufacturer_GroupName" xml:space="preserve">
    <value>Group Name</value>
  </data>
  <data name="Manufacturer_GroupCode" xml:space="preserve">
    <value>Group Code</value>
  </data>
  <data name="ContactLog" xml:space="preserve">
    <value>Contact Log</value>
  </data>
  <data name="EnteredBy" xml:space="preserve">
    <value>Entered By</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Taxes" xml:space="preserve">
    <value>Taxes</value>
  </data>
  <data name="Document" xml:space="preserve">
    <value>Document</value>
  </data>
  <data name="DD/MM/YYYY" xml:space="preserve">
    <value>DD/MM/YYYY</value>
  </data>
  <data name="CommunicationLogTypeNo" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="AddedBy" xml:space="preserve">
    <value>Added by</value>
  </data>
  <data name="LogDateFrom" xml:space="preserve">
    <value>Log Date From</value>
  </data>
  <data name="LogDateTo" xml:space="preserve">
    <value>Log Date To</value>
  </data>
  <data name="RelatedCompanies" xml:space="preserve">
    <value>Related Companies</value>
  </data>
  <data name="UploadedDocuments" xml:space="preserve">
    <value>Uploaded Documents</value>
  </data>
  <data name="Hide" xml:space="preserve">
    <value>Hide Filter</value>
  </data>
  <data name="Show" xml:space="preserve">
    <value>Show Filter</value>
  </data>
  <data name="PleaseChooseDateAndTimeLaterThanCurrentValues" xml:space="preserve">
    <value>Please select a date and time later than the current values</value>
  </data>
  <data name="Part Of" xml:space="preserve">
    <value>Part Of</value>
  </data>
  <data name="Insurance File No" xml:space="preserve">
    <value>Insurance File No</value>
  </data>
  <data name="Insured Amount" xml:space="preserve">
    <value>Insured Amount</value>
  </data>
  <data name="Approved Customer" xml:space="preserve">
    <value>Approved Customer</value>
  </data>
  <data name="Approved Supplier" xml:space="preserve">
    <value>Approved Supplier</value>
  </data>
  <data name="This Company is On Stop" xml:space="preserve">
    <value>This Company is On Stop</value>
  </data>
  <data name="Main" xml:space="preserve">
    <value>Main</value>
  </data>
  <data name="Detail" xml:space="preserve">
    <value>Detail</value>
  </data>
  <data name="Customer API" xml:space="preserve">
    <value>Customer API</value>
  </data>
  <data name="Main Company Information" xml:space="preserve">
    <value>Main Company Information</value>
  </data>
  <data name="Manufacturers Supplied" xml:space="preserve">
    <value>Manufacturers Supplied</value>
  </data>
  <data name="Addresses" xml:space="preserve">
    <value>Addresses</value>
  </data>
  <data name="Prospects Qualification" xml:space="preserve">
    <value>Prospects Qualification</value>
  </data>
  <data name="PDF Documents" xml:space="preserve">
    <value>PDF Documents</value>
  </data>
  <data name="Global Sales Access" xml:space="preserve">
    <value>Global Sales Access</value>
  </data>
  <data name="This Company is Sanctioned" xml:space="preserve">
    <value>This Company is Sanctioned</value>
  </data>
  <data name="Prospect Type" xml:space="preserve">
    <value>Prospect Type</value>
  </data>
  <data name="MFR Board Level" xml:space="preserve">
    <value>MFR Board Level</value>
  </data>
  <data name="Final Assembly" xml:space="preserve">
    <value>Final Assembly</value>
  </data>
  <data name="End Customer or CEM" xml:space="preserve">
    <value>End Customer or CEM</value>
  </data>
  <data name="Industry Type" xml:space="preserve">
    <value>Industry Type</value>
  </data>
  <data name="Credit Information" xml:space="preserve">
    <value>Credit Information</value>
  </data>
  <data name="Electronic Spend" xml:space="preserve">
    <value>Electronic Spend</value>
  </data>
  <data name="Frequency of Purchase" xml:space="preserve">
    <value>Frequency of Purchase</value>
  </data>
  <data name="Commodities" xml:space="preserve">
    <value>Commodities</value>
  </data>
  <data name="Turnover" xml:space="preserve">
    <value>Turnover</value>
  </data>
  <data name="Finance" xml:space="preserve">
    <value>Finance</value>
  </data>
  <data name="ROHSUnknown" xml:space="preserve">
    <value>RoHS Unknown</value>
  </data>
  <data name="ROHSNotApplicable" xml:space="preserve">
    <value>RoHS Not Applicable</value>
  </data>
  <data name="Percentage complete" xml:space="preserve">
    <value>Percentage complete</value>
  </data>
  <data name="\&quot;Credit Limit Potential\&quot; and \&quot;Health Rating\&quot; are calculated as 5% each, and the rest as 10%" xml:space="preserve">
    <value>\"Credit Limit Potential\" and \"Health Rating\" are calculated as 5% each, and the rest as 10%</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="Contact First Name" xml:space="preserve">
    <value>Contact First Name</value>
  </data>
  <data name="Contact Last Name" xml:space="preserve">
    <value>Contact Last Name</value>
  </data>
  <data name="Address Name" xml:space="preserve">
    <value>Address Name</value>
  </data>
  <data name="Address (line 1)" xml:space="preserve">
    <value>Address (line 1)</value>
  </data>
  <data name="Address (line 2)" xml:space="preserve">
    <value>Address (line 2)</value>
  </data>
  <data name="Address (line 3)" xml:space="preserve">
    <value>Address (line 3)</value>
  </data>
  <data name="Town/City" xml:space="preserve">
    <value>Town/City</value>
  </data>
  <data name="County" xml:space="preserve">
    <value>County</value>
  </data>
  <data name="State" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="Postcode" xml:space="preserve">
    <value>Postcode</value>
  </data>
  <data name="0800 Number" xml:space="preserve">
    <value>0800 Number</value>
  </data>
  <data name="Website" xml:space="preserve">
    <value>Website</value>
  </data>
  <data name="VAT No" xml:space="preserve">
    <value>VAT No</value>
  </data>
  <data name="Company Reg. No" xml:space="preserve">
    <value>Company Reg. No</value>
  </data>
  <data name="Review Status" xml:space="preserve">
    <value>Review Status</value>
  </data>
  <data name="Quality/scope of supply" xml:space="preserve">
    <value>Quality/scope of supply</value>
  </data>
  <data name="EORI No" xml:space="preserve">
    <value>EORI No</value>
  </data>
  <data name="Unselected Companies" xml:space="preserve">
    <value>Unselected Companies</value>
  </data>
  <data name="Selected Companies" xml:space="preserve">
    <value>Selected Companies</value>
  </data>
  <data name="NoResult" xml:space="preserve">
    <value>NoResult</value>
  </data>
  <data name="Sales Information" xml:space="preserve">
    <value>Sales Information</value>
  </data>
  <data name="Manage Members" xml:space="preserve">
    <value>Manage Members</value>
  </data>
  <data name="CustomerRequirement" xml:space="preserve">
    <value>Customer Requirements</value>
  </data>
  <data name="Quotes" xml:space="preserve">
    <value>Quotes</value>
  </data>
  <data name="SalesOrder" xml:space="preserve">
    <value>Sales Orders</value>
  </data>
  <data name="Invoices" xml:space="preserve">
    <value>Invoices</value>
  </data>
  <data name="PurchaseOrder" xml:space="preserve">
    <value>Purchase Orders</value>
  </data>
  <data name="PurchaseRequisition" xml:space="preserve">
    <value>Purchase Requisitions</value>
  </data>
  <data name="CustomerRMA" xml:space="preserve">
    <value>Customer RMAs</value>
  </data>
  <data name="SupplierRMA" xml:space="preserve">
    <value>Supplier RMAs</value>
  </data>
  <data name="CreditNote" xml:space="preserve">
    <value>Credit Notes</value>
  </data>
  <data name="DebitNote" xml:space="preserve">
    <value>Debit Notes</value>
  </data>
  <data name="InternalPurchaseOrder" xml:space="preserve">
    <value>Internal Purchase Order</value>
  </data>
  <data name="HUBRFQ" xml:space="preserve">
    <value>HUBRFQ</value>
  </data>
  <data name="Transactions" xml:space="preserve">
    <value>Transactions</value>
  </data>
  <data name="Value" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="QuickJumpErrMsg" xml:space="preserve">
    <value>Sorry, that document cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="QuickJumpInvalidMsg" xml:space="preserve">
    <value>Please enter a valid numeric value</value>
  </data>
  <data name="LINK ACCOUNTS" xml:space="preserve">
    <value>LINK ACCOUNTS</value>
  </data>
  <data name="Activate" xml:space="preserve">
    <value>Activate</value>
  </data>
  <data name="Inactivate" xml:space="preserve">
    <value>Inactivate</value>
  </data>
  <data name="No Matches" xml:space="preserve">
    <value>No Matches</value>
  </data>
  <data name="quickBrowseCompanyName" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="quickBrowseViewLevel" xml:space="preserve">
    <value>View Level</value>
  </data>
  <data name="quickBrowseName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="quickBrowseType" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="quickBrowseCity" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="quickBrowseCountry" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="quickBrowseTel" xml:space="preserve">
    <value>Tel</value>
  </data>
  <data name="quickBrowseState" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="quickBrowseCounty" xml:space="preserve">
    <value>County</value>
  </data>
  <data name="quickBrowseManufacturerSupplied" xml:space="preserve">
    <value>Manufacturer Supplied</value>
  </data>
  <data name="quickBrowseGroupCodeName" xml:space="preserve">
    <value>Group Code Name</value>
  </data>
  <data name="quickBrowseCompanyType" xml:space="preserve">
    <value>Company Type</value>
  </data>
  <data name="quickBrowseVATID" xml:space="preserve">
    <value>VAT ID</value>
  </data>
  <data name="quickBrowseCertificateCategory" xml:space="preserve">
    <value>Certificate Category</value>
  </data>
  <data name="quickBrowseCertificateNo" xml:space="preserve">
    <value>Certificate No</value>
  </data>
  <data name="quickBrowseSupplierRating" xml:space="preserve">
    <value>Supplier Rating</value>
  </data>
  <data name="quickBrowseCustomerRating" xml:space="preserve">
    <value>Customer Rating</value>
  </data>
  <data name="quickBrowseCustomerNo" xml:space="preserve">
    <value>Customer No</value>
  </data>
  <data name="quickBrowseZipCode" xml:space="preserve">
    <value>Zip Code</value>
  </data>
  <data name="quickBrowseClientName" xml:space="preserve">
    <value>Client Name</value>
  </data>
  <data name="quickBrowseRegion" xml:space="preserve">
    <value>Region</value>
  </data>
  <data name="quickBrowseEmail" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="quickBrowseIndustryType" xml:space="preserve">
    <value>Industry Type</value>
  </data>
  <data name="quickBrowseCompanyStatus" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="quickBrowseSalesperson" xml:space="preserve">
    <value>Salesperson</value>
  </data>
  <data name="ViewTask" xml:space="preserve">
    <value>View Task</value>
  </data>
  <data name="AddTask" xml:space="preserve">
    <value>Add Task</value>
  </data>
  <data name="QuickBrowse" xml:space="preserve">
    <value>QuickBrowse</value>
  </data>
  <data name="Customer Group Code" xml:space="preserve">
    <value>Customer Group Code</value>
  </data>
  <data name="Purchase Requisitions" xml:space="preserve">
    <value>Purchase Requisitions</value>
  </data>
  <data name="Sourcing Results" xml:space="preserve">
    <value>Sourcing Results</value>
  </data>
  <data name="filters Applied" xml:space="preserve">
    <value>filters Applied</value>
  </data>
  <data name="Requirement" xml:space="preserve">
    <value>Requirement</value>
  </data>
  <data name="No filters applied" xml:space="preserve">
    <value>No filters applied</value>
  </data>
  <data name="quickBrowsePartNo" xml:space="preserve">
    <value>Part No</value>
  </data>
  <data name="quickBrowseCompany" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="quickBrowseContact" xml:space="preserve">
    <value>Contact Name</value>
  </data>
  <data name="quickBrowseSalesOrder" xml:space="preserve">
    <value>Sales Order</value>
  </data>
  <data name="quickBrowseDateOrderedFrom" xml:space="preserve">
    <value>Date Ordered From</value>
  </data>
  <data name="quickBrowseDateOrderedTo" xml:space="preserve">
    <value>Date Ordered To</value>
  </data>
  <data name="Active" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Inactive</value>
  </data>
  <data name="MaxImportRowsMessage" xml:space="preserve">
    <value>Maximum limit should not exceed {0} rows.</value>
  </data>
  <data name="Alternative" xml:space="preserve">
    <value>Alternative</value>
  </data>
  <data name="PossibleAlternative" xml:space="preserve">
    <value>Possible Alternative</value>
  </data>
  <data name="FirmAlternative" xml:space="preserve">
    <value>Possible Alternative</value>
  </data>
  <data name="Export Approval Status" xml:space="preserve">
    <value>Export Approval Status</value>
  </data>
  <data name="quickBrowseRecentOnly" xml:space="preserve">
    <value>Recent Only?</value>
  </data>
  <data name="quickBrowseIncludeClosed" xml:space="preserve">
    <value>Include Closed?</value>
  </data>
  <data name="quickBrowseCustomerPO" xml:space="preserve">
    <value>Customer PO</value>
  </data>
  <data name="quickBrowseContract" xml:space="preserve">
    <value>Contract No</value>
  </data>
  <data name="quickBrowseIncludeOrderSent" xml:space="preserve">
    <value>SOs sent to customer only</value>
  </data>
  <data name="quickBrowseAS6081" xml:space="preserve">
    <value>AS6081 testing required?</value>
  </data>
  <data name="quickBrowseDatePromisedFrom" xml:space="preserve">
    <value>Date Promised From</value>
  </data>
  <data name="quickBrowseDatePromisedTo" xml:space="preserve">
    <value>Date Promised To</value>
  </data>
  <data name="quickBrowseSalesOrderStatus" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="quickBrowseSOCheckedStatus" xml:space="preserve">
    <value>Checked?</value>
  </data>
  <data name="BOM Export Note" xml:space="preserve">
    <value>Note: Please do not modify the information highlighted in blue (A to G).</value>
  </data>
  <data name="BOM Export If No Bid" xml:space="preserve">
    <value>- If no bid is available, update the Price column with "N/A".</value>
  </data>
  <data name="BOM Export If Multiple Sourcing" xml:space="preserve">
    <value>- If multiple sourcing entries exist for a single Requirement, copy the corresponding HUBRFQ row (blue-highlighted) and input the sourcing details accordingly.</value>
  </data>
  <data name="HUBRFQ EXPORT HUBRFQ No" xml:space="preserve">
    <value>HUBRFQ No</value>
  </data>
  <data name="HUBRFQ EXPORT REQUIREMENT" xml:space="preserve">
    <value>REQUIREMENT</value>
  </data>
  <data name="HUBRFQ EXPORT CLIENT NO." xml:space="preserve">
    <value>CLIENT NO.</value>
  </data>
  <data name="HUBRFQ EXPORT REQ. PART" xml:space="preserve">
    <value>REQ. PART</value>
  </data>
  <data name="HUBRFQ EXPORT Product" xml:space="preserve">
    <value>Product</value>
  </data>
  <data name="HUBRFQ EXPORT REQ. MANUFACTURER" xml:space="preserve">
    <value>REQ. MANUFACTURER</value>
  </data>
  <data name="HUBRFQ EXPORT REQ. QTY" xml:space="preserve">
    <value>REQ. QTY</value>
  </data>
  <data name="HUBRFQ EXPORT CUSTOMER REF NO." xml:space="preserve">
    <value>CUSTOMER REF NO.</value>
  </data>
  <data name="HUBRFQ EXPORT *SUPPLIER NAME" xml:space="preserve">
    <value>*SUPPLIER NAME</value>
  </data>
  <data name="HUBRFQ EXPORT *SUPPLIER PART NO." xml:space="preserve">
    <value>*SUPPLIER PART NO.</value>
  </data>
  <data name="HUBRFQ EXPORT *Supplier Cost" xml:space="preserve">
    <value>*Supplier Cost</value>
  </data>
  <data name="HUBRFQ EXPORT ROHS" xml:space="preserve">
    <value>ROHS</value>
  </data>
  <data name="HUBRFQ EXPORT *MANUFACTURER" xml:space="preserve">
    <value>*MANUFACTURER</value>
  </data>
  <data name="HUBRFQ EXPORT DATE CODE" xml:space="preserve">
    <value>DATE CODE</value>
  </data>
  <data name="HUBRFQ EXPORT PACKAGE" xml:space="preserve">
    <value>PACKAGE</value>
  </data>
  <data name="HUBRFQ EXPORT *OFFERED QTY." xml:space="preserve">
    <value>*OFFERED QTY.</value>
  </data>
  <data name="HUBRFQ EXPORT OFFER STATUS" xml:space="preserve">
    <value>OFFER STATUS</value>
  </data>
  <data name="HUBRFQ EXPORT SPQ" xml:space="preserve">
    <value>SPQ</value>
  </data>
  <data name="HUBRFQ EXPORT FACTORY SEALED" xml:space="preserve">
    <value>FACTORY SEALED</value>
  </data>
  <data name="HUBRFQ EXPORT QTY IN STOCK" xml:space="preserve">
    <value>QTY IN STOCK</value>
  </data>
  <data name="HUBRFQ EXPORT MOQ" xml:space="preserve">
    <value>MOQ</value>
  </data>
  <data name="HUBRFQ EXPORT LAST TIME BUY" xml:space="preserve">
    <value>LAST TIME BUY</value>
  </data>
  <data name="HUBRFQ EXPORT *CURRENCY" xml:space="preserve">
    <value>*CURRENCY</value>
  </data>
  <data name="HUBRFQ EXPORT *BUY PRICE" xml:space="preserve">
    <value>*BUY PRICE</value>
  </data>
  <data name="HUBRFQ EXPORT *SELL PRICE" xml:space="preserve">
    <value>*SELL PRICE</value>
  </data>
  <data name="HUBRFQ EXPORT SHIPPING COST" xml:space="preserve">
    <value>SHIPPING COST</value>
  </data>
  <data name="HUBRFQ EXPORT LEADTIME" xml:space="preserve">
    <value>LEADTIME</value>
  </data>
  <data name="HUBRFQ EXPORT REGION" xml:space="preserve">
    <value>REGION</value>
  </data>
  <data name="HUBRFQ EXPORT DELIVERY DATE" xml:space="preserve">
    <value>DELIVERY DATE</value>
  </data>
  <data name="HUBRFQ EXPORT NOTES" xml:space="preserve">
    <value>NOTES</value>
  </data>
  <data name="HUBRFQ EXPORT MSL" xml:space="preserve">
    <value>MSL</value>
  </data>
  <data name="Post" xml:space="preserve">
    <value>Post</value>
  </data>
  <data name="ok" xml:space="preserve">
    <value>ok</value>
  </data>
  <data name="Already exists" xml:space="preserve">
    <value>already exists</value>
  </data>
  <data name="Unpost" xml:space="preserve">
    <value>Unpost</value>
  </data>
  <data name="Continue" xml:space="preserve">
    <value>Continue</value>
  </data>
  <data name="Sourcing" xml:space="preserve">
    <value>Sourcing</value>
  </data>
  <data name="GlobalSettings" xml:space="preserve">
    <value>Global Settings</value>
  </data>
  <data name="Test" xml:space="preserve">
    <value>Global Trader V2</value>
  </data>
  <data name="Unpost All" xml:space="preserve">
    <value>Unpost All</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Allocate" xml:space="preserve">
    <value>Allocate</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Confirm All" xml:space="preserve">
    <value>Confirm All</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>All</value>
  </data>
  <data name="Open" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="Closed" xml:space="preserve">
    <value>Closed</value>
  </data>
  <data name="Approve" xml:space="preserve">
    <value>Approve</value>
  </data>
  <data name="Reject" xml:space="preserve">
    <value>Reject</value>
  </data>
  <data name="Task(s)" xml:space="preserve">
    <value>Task(s)</value>
  </data>
  <data name="Purchase Requisition" xml:space="preserve">
    <value>Purchase Requisition</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="Note" xml:space="preserve">
    <value>Note</value>
  </data>
  <data name="Import" xml:space="preserve">
    <value>Import</value>
  </data>
  <data name="Save &amp; Check" xml:space="preserve">
    <value>Save &amp; Check</value>
  </data>
  <data name="This Company is inactive" xml:space="preserve">
    <value>This Company is inactive</value>
  </data>
  <data name="Allowed extensions" xml:space="preserve">
    <value>Allowed extensions</value>
  </data>
  <data name="allowedMaxSizeErrorMsg" xml:space="preserve">
    <value>Allowed Max size: {0} MB</value>
  </data>
  <data name="Please select a file larger than 0 KB" xml:space="preserve">
    <value>Please select a file larger than 0 KB</value>
  </data>
  <data name="This Sourcing Result has no images attached" xml:space="preserve">
    <value>This Sourcing Result has no images attached</value>
  </data>
  <data name="Medium" xml:space="preserve">
    <value>Medium</value>
  </data>
  <data name="Large" xml:space="preserve">
    <value>Large</value>
  </data>
  <data name="Delete document" xml:space="preserve">
    <value>Delete document</value>
  </data>
  <data name="Sorry, that image was not found" xml:space="preserve">
    <value>Sorry, that image was not found</value>
  </data>
  <data name="This has no file attached." xml:space="preserve">
    <value>This has no file attached.</value>
  </data>
  <data name="Lines" xml:space="preserve">
    <value>Lines</value>
  </data>
  <data name="quickBrowsePurchaseOrder" xml:space="preserve">
    <value>Purchase Order</value>
  </data>
  <data name="quickBrowseDeliveryDateFrom" xml:space="preserve">
    <value>Delivery Date From</value>
  </data>
  <data name="quickBrowseDeliveryDateTo" xml:space="preserve">
    <value>Delivery Date To</value>
  </data>
  <data name="quickBrowsePOHubOnly" xml:space="preserve">
    <value>PoHub Only</value>
  </data>
  <data name="quickBrowseExpediteDateFrom" xml:space="preserve">
    <value>Expedite Date From</value>
  </data>
  <data name="quickBrowseExpediteDateTo" xml:space="preserve">
    <value>Expedite Date To</value>
  </data>
  <data name="quickBrowseIPONo" xml:space="preserve">
    <value>IPO No</value>
  </data>
  <data name="quickBrowseBuyerName" xml:space="preserve">
    <value>Buyer</value>
  </data>
  <data name="quickBrowseSupplierApproval" xml:space="preserve">
    <value>Supplier Approval</value>
  </data>
  <data name=" quickBrowseAS6081" xml:space="preserve">
    <value>AS6081 testing required?</value>
  </data>
  <data name="quickBrowseChecked" xml:space="preserve">
    <value>Checked ?</value>
  </data>
  <data name="quickBrowseStatus" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="RequestTimeout" xml:space="preserve">
    <value>The timeout period elapsed prior to the completion of the operation or the server is not responding</value>
  </data>
</root>