﻿using GlobalTrader2.Dto.BOM;
using GlobalTrader2.Dto.Datatables;
using GlobalTrader2.Orders.UI.ViewModel.BOM.ImportSourcingResults;
using GlobalTrader2.Orders.UserCases.Orders.BOM.ImportSourcingResults.Commands.ImportDataFile;
using GlobalTrader2.Orders.UserCases.Orders.BOM.ImportSourcingResults.Commands.ImportSourcingResults;
using GlobalTrader2.Orders.UserCases.Orders.BOM.ImportSourcingResults.Commands.SaveImportSourcingData;
using GlobalTrader2.Orders.UserCases.Orders.BOM.ImportSourcingResults.Commands.UpdateIncorrectImportSourcingData;
using GlobalTrader2.Orders.UserCases.Orders.BOM.ImportSourcingResults.Queries.GetImportSourcingResultsRawData;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/orders/bom/import-sourcing-results")]
    public class HubImportSourcingResultsController : ApiBaseController
    {
        private readonly IMediator _mediator;
        public HubImportSourcingResultsController(IMediator mediator)
        {
            _mediator = mediator;
        }

        [HttpPost("import-data-file")]
        [ValidateAntiForgeryToken]
        [ApiAuthorize(false, SecurityFunction.Orders_BOMDetail_Import_Export_SourcingResult)]
        public async Task<IActionResult> ImportDataFileAsync(ImportDataFileRequest request)
        {
            var importDataFileCommand = new ImportDataFileCommand
            {
                CreatedBy = UserId,
                OriginalFileName = request.OriginalFilename,
                GeneratedFileName = request.GeneratedFilename,
                BomNo = request.BomNo,
                HasColumnHeaders = true
            };

            var response = await _mediator.Send(importDataFileCommand);

            return Ok(response);
        }

        [HttpGet("imported-raw-data")]
        public async Task<IActionResult> GetImportedRawData(int bomNo, bool showMissmatchOnly = true, int draw = 1, int pageNumber = 1, int pageSize = 20)
        {
            var getImportedRawDataQuery = new GetImportSourcingResultsRawDataQuery
            {
                LoginId = UserId,
                BOMNo = bomNo,
                showMismatchOnly = showMissmatchOnly,
                pageNumber = pageNumber,
                pageSize = pageSize
            };

            var result = await _mediator.Send(getImportedRawDataQuery);

            var response = new DatatableResponse<HubSourcingResultImportTempDetailsDto>()
            {
                Success = result.Success,
                Data = result.Data,
                RecordsTotal = result.Data?.TotalRecords ?? 0,
                RecordsFiltered = showMissmatchOnly ? result.Data?.TotalRecordsError : result.Data?.TotalRecords,
                Draw = draw
            };

            return Ok(response);
        }

        [HttpPut("incorrect-data")]
        [ValidateAntiForgeryToken]
        [ApiAuthorize(false, SecurityFunction.Orders_BOMDetail_Import_Export_SourcingResult)]
        public async Task<IActionResult> UpdateIncorrectDataAsync(UpdateIncorrectImportSourcingDataCommand command)
        {
            command.UserId = UserId;
            var response = await _mediator.Send(command);
            return Ok(response);
        }

        [HttpPost("save-data")]
        [ValidateAntiForgeryToken]
        [ApiAuthorize(false, SecurityFunction.Orders_BOMDetail_Import_Export_SourcingResult)]
        public async Task<IActionResult> SaveImportSourcingData(SaveImportSourcingDataCommand command)
        {
            command.DataList.ForEach(x => x.CreatedBy = UserId);
            return Ok(await _mediator.Send(command));
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [ApiAuthorize(false, SecurityFunction.Orders_BOMDetail_Import_Export_SourcingResult)]
        public async Task<IActionResult> ImportSourcingResult(ImportSourcingResultsCommand command)
        {
            command.UserId = UserId;
            return Ok(await _mediator.Send(command));
        }
    }
}
