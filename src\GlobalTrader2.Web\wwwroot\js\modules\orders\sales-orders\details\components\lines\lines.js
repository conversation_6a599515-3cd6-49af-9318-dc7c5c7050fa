import { SectionBox } from '../../../../../../components/base/section-box.component.js?v=#{BuildVersion}#';
import { ResizeDatatableEvents } from '../../../../../../components/base/resize-lite-datatable/resize-lite-datatable-events.constanst.js?v=#{BuildVersion}#'
import { CloseLineManager } from './components/close-line.js?v=#{BuildVersion}#'
import { LinesLevelConfig, LinesTabLevel } from './configs/lines-level.config.js?v=#{BuildVersion}#';
import { LinesTabManager } from './components/lines-tab.component.js?v=#{BuildVersion}#';
import { ConfirmLinesManager } from './components/confirm-lines.js?v=#{BuildVersion}#';
import { PostLineManager } from './components/post-line.js?v=#{BuildVersion}#';
import { EventEmitter } from '../../../../../../components/base/event-emmiter.js?v=#{BuildVersion}#'
import { EditSoLineComponent } from './components/edit-line.component.js?v=#{BuildVersion}#'
import { EditAllSoLinesComponent } from './components/edit-all-lines.js?v=#{BuildVersion}#'
import { AddNewLineManager } from './components/addNewLine/add-new-line.js?v=#{BuildVersion}#';
import { CreateIPOManager } from './components/create-ipo-so-line.js?v=#{BuildVersion}#';
import { CreateInternalPurchaseOrderFrom } from '../../constants/create-ipo-from.js?v=#{BuildVersion}#';
import { SalesOrderStatus } from '../../enums/sales-order-status-enum.js?v=#{BuildVersion}#'
import { LinesTabService } from './lines.service.js?v=#{BuildVersion}#';
import { SOGeneralInfoModel } from './models/so-general-info.model.js?v=#{BuildVersion}#';

export class LinesManager extends EventEmitter {
    constructor(salesOrderId, salesOrderLineId, onLinePostedSuccessCallback = () => { }) {
        super();
        this.onLinePostedSuccessCallback = onLinePostedSuccessCallback;
        this.$tab = $('#lines-tabs');
        this.$editButton = $('#lines-edit-btn');
        this._selectedSalesOrderLineId = salesOrderLineId;
        this._salesOrderId = salesOrderId;
        this._currentTab = LinesTabLevel.all;
        this._tabInstance = {};
        this._selectedRow = null;
        this._closeLine = new CloseLineManager()
        this._confirmLines = new ConfirmLinesManager(this._salesOrderId);
        this._postLines = new PostLineManager(() => this.onLinePostedSuccessCallback() )
        this._createIpo = new CreateIPOManager(this._salesOrderId, CreateInternalPurchaseOrderFrom.SalesOrderLine, "create-ipo-so-line-dialog","create-ipo-so-line-form");
        this._addNewLine = null;

        this.$postButton = $('#lines-post-btn');
        this.$postAllButton = $('#lines-post-all-btn');
        this.$unpostButton = $('#lines-unpost-btn');
        this.$unpostAllButton = $('#lines-unpost-all-btn')
        this.$closeButton = $('#lines-close-btn');
        this.$confirmButton = $('#lines-confirm-btn');
        this.$confirmAllButton = $('#lines-confirm-all-btn');
        this.$deleteButton = $('#lines-delete-btn');
        this.$addNewLineButton = $('#lines-add-btn');
        this.$createIpoButton = $('#lines-create-ipo-btn');
        this.$editAllButton = $('#lines-edit-all-btn');
        this._authData = null
        this._authCheck = false;
        this._mainInfoSalesOrderData = null;
        this._taxRate = null;
        this._soGeneralInfo = new SOGeneralInfoModel();
        this._soGeneralInfo.SalesOrderId = this._salesOrderId;
        this._creditData = null;
        this._editLineDialog = new EditSoLineComponent(this._soGeneralInfo);
        this._editAllLinesDialog = new EditAllSoLinesComponent();
        this._sectionBox = new SectionBox('#lines-box', {
            loadingContentId: 'lines-wrapper',
            loading: false,
        });

        this._factory = {
            [LinesTabLevel.all]: new LinesTabManager(this._salesOrderId, LinesLevelConfig.ALL),
            [LinesTabLevel.open]: new LinesTabManager(this._salesOrderId, LinesLevelConfig.OPEN),
            [LinesTabLevel.closed]: new LinesTabManager(this._salesOrderId, LinesLevelConfig.CLOSED),
        };
        this.checkBoxEnabled = false;
        this.addNewLine = new AddNewLineManager(
        {
            soGeneralInfo: this._soGeneralInfo
        });
        this.preferredWarehouseName = null;
        this.preferredWarehouseNo = null;
    }

    init(salesOrdersMainInfo, authData) {
        this._setupSectionBox();
        this._setupTabsEventListener();
        this._closeLine.setupDialog();
        this._confirmLines.setupDialog();
        this._postLines.setupDialog();
        this._createIpo.initialize();
        this._setupDialogEvents();
        this._setupOnClickEvents();
        this._setupEditSuccessEvent();
        this._setupAddSuccessEvent();

        this.updateAuthData(authData);
        this.updateSalesOrderMainInfo(salesOrdersMainInfo);
        this._enableButton(this.$addNewLineButton, salesOrdersMainInfo.statusNo !== SalesOrderStatus.Complete && (salesOrdersMainInfo.autoApproveSO || salesOrdersMainInfo.authorisedBy == null));
        this._selectTab(LinesTabLevel.all);
    }

    async refreshSectionBox() {
        await this._refreshTableAsync(this._selectedSalesOrderLineId);
        this._creditData = {
            anyPosted: this._tabInstance[this._currentTab].data.anyLinePosted,
            totalVal: this._tabInstance[this._currentTab].data.totalVal,
            authData: this._authData
        };
        this._postLines._creditData = this._creditData;
    }

    updateSalesOrderMainInfo(salesOrdersMainInfo) {
        this._mainInfoSalesOrderData = salesOrdersMainInfo;
        this._updateSOInfo(salesOrdersMainInfo);
    }

    _updateSOInfo(salesOrdersMainInfo) {
        this._soGeneralInfo.IsAS9120 = salesOrdersMainInfo.aS9120;
        this._soGeneralInfo.IsSoCompleted = salesOrdersMainInfo.statusNo === SalesOrderStatus.Complete;
        this._soGeneralInfo.IsSoAuthorised = typeof (salesOrdersMainInfo.authorisedBy) !== 'undefined' && salesOrdersMainInfo.authorisedBy !== null;
        this._soGeneralInfo.SalesOrderNumber = salesOrdersMainInfo.salesOrderNumber;
        this._soGeneralInfo.CustomerName = salesOrdersMainInfo.customerName;
        this._soGeneralInfo.CurrencyCode = salesOrdersMainInfo.currencyCode;
        this._soGeneralInfo.CurrencyNo = salesOrdersMainInfo.currencyNo;
        this._soGeneralInfo.SODateOrdered = salesOrdersMainInfo.dateOrderedDesc;
    }


    updateAuthData(authData) {
        this._authData = authData;
        this._soGeneralInfo.BalanceWithOpen = this._authData.balanceWithOpenSOValue
        this._soGeneralInfo.CreditLimit = this._authData.creditLimitValue;
        this._soGeneralInfo.InvoiceNotExported = this._authData.invoiceNotExportValue;
    }

    setPreferredWarehouse(preferredWarehouseName, preferredWarehouseNo) {
        this.preferredWarehouseName = preferredWarehouseName;
        this.preferredWarehouseNo = preferredWarehouseNo;
    }

    async _refreshTableAsync(soLineId) {
        this._sectionBox.loading(false);

        const tabInstance = this._tabInstance[this._currentTab];
        this._setAllTabsRefreshNeeded();
        tabInstance.data = await this._tabInstance[this._currentTab].getData();

        this._soGeneralInfo.TaxRate = tabInstance.data.taxRate;
        this._soGeneralInfo.TotalLinePrice = tabInstance.data.totalVal;
        this._soGeneralInfo.IsAnyLinePosted = tabInstance.data.anyLinePosted;

        this._tabInstance[this._currentTab].renderTable(tabInstance.data.items ?? [], soLineId);
        this._tabInstance[this._currentTab]._bindSaleOrderTotal();
        this._tabInstance[this._currentTab].linesResizeDatatable.deselectIpoCheckbox();
        GlobalTrader.Helper.scrollToRow(this._tabInstance[this._currentTab].linesResizeDatatable.datatable, soLineId)
        this._selectedRow = this._tabInstance[this._currentTab].linesResizeDatatable.datatable.row({ selected: true }).data();
        this._updateButtonsWhenSelectNewRow(this._selectedRow);
        this._updateConfirmAllButton();
        this._updatePostUnpostAllButton();
        this._updateEnableCheckbox();
    }

    _setAllTabsRefreshNeeded() {
        Object.entries(this._tabInstance)
            .forEach(([key, tab]) => tab.refreshNeeded = true
            );
    }

    _setupDialogEvents() {
        const dialogs = [
            this._closeLine.$dialog,
            this._confirmLines.$dialog,
            this._postLines.$dialog,
            this._createIpo.$dialog
        ];

        dialogs.forEach(dialog => {
            dialog.on("saveSuccess", async (e, soLineId) => {
                await this._refreshTableAsync(soLineId);
            });
        });

        this._postLines.$dialog.on("creditWarning", (e, message) => {
            this._tabInstance[this._currentTab].trigger("addMessage", message)
            this._tabInstance[this._currentTab].trigger("showMessage")
        })
    }

    _setupTabsEventListener() {
        this.$tab.find('button[data-bs-toggle="tab"]').on('shown.bs.tab', async (e) => {
            // The last tab message should be hidden before switching to the new one
            if (this._tabInstance[this._currentTab] !== undefined) {
                this._tabInstance[this._currentTab].trigger('hideMessage');
            }
            const currentTabLevel = $(e.currentTarget).data("view-level");
            this._currentTab = currentTabLevel;

            const currentTab = this._tabInstance[this._currentTab];
            if (currentTab !== undefined) {
                currentTab.trigger('showMessage');
                currentTab.linesResizeDatatable.deselectIpoCheckbox();
                if (currentTab.refreshNeeded) {
                    this._sectionBox.loading(false);
                    const data = await this._tabInstance[this._currentTab].getData();
                    this._tabInstance[this._currentTab].renderTable(data.items ?? []);
                    this._sectionBox.stopLoading(false);
                }
                this._selectedRow = currentTab.linesResizeDatatable.datatable.row({ selected: true }).data();
                this._updateButtonsWhenSelectNewRow(this._selectedRow);
                this._updateConfirmAllButton();
                this._updatePostUnpostAllButton();
                this._updateEnableCheckbox();
                return;
            }
            this._sectionBox.loading(false);
            const tabInstance = this._createTabInstance(this._currentTab);
            this._tabInstance[this._currentTab] = tabInstance;

            await tabInstance.init();

            this._soGeneralInfo.TaxRate = tabInstance.data.taxRate;
            this._soGeneralInfo.TotalLinePrice = tabInstance.data.totalVal;
            this._soGeneralInfo.IsAnyLinePosted = tabInstance.data.anyLinePosted;

            tabInstance.setupRowSelectionEvent();
            this._setupTableEvents(tabInstance);

            tabInstance.initTable();
            this._setupRowSelectionEvent(tabInstance);
            this._creditData = {
                anyPosted: this._tabInstance[this._currentTab].data.anyLinePosted,
                totalVal: this._tabInstance[this._currentTab].data.totalVal,
                authData: this._authData
            };
            this._postLines._creditData = this._creditData;
            this._closeLine._customerName = this._soGeneralInfo.CustomerName;

            if (this._selectedSalesOrderLineId !== null) {
                tabInstance.linesResizeDatatable.selectRowById(this._selectedSalesOrderLineId);
            }
            this._updateButtonsWhenSelectNewRow(null);
            this._updateConfirmAllButton();
            this._updatePostUnpostAllButton();
            this._updateEnableCheckbox();
        });
    }

    _selectTab(tabLevel) {
        this.$tab.find(`button[data-view-level="${tabLevel}"`).trigger('shown.bs.tab');
    }

    _setupSectionBox() {
        this._sectionBox.init();

        this._sectionBox.on('onRefreshed.msb', async () => {
            this.refreshSectionBox();
        });
    }

    _createTabInstance(tabLevel) {
        if (!this._factory[tabLevel]) {
            throw new Error(`Unknown tab level: ${tabLevel}`);
        }
        return this._factory[tabLevel];
    }

    _setupTableEvents(tabInstance) {
        tabInstance.linesResizeDatatable.on(ResizeDatatableEvents.DRAW,
            () => {
                this._sectionBox.stopLoading(false);
            }
        );
    }

    _setupRowSelectionEvent(tabInstance) {
        tabInstance.linesResizeDatatable.on(ResizeDatatableEvents.SELECT, (e, dt, type, indexes) => {
            const rowData = dt.row(indexes).data();
            this._selectedRow = rowData;
            this._updateButtonsWhenSelectNewRow(this._selectedRow);
        });

        tabInstance.linesResizeDatatable.on(ResizeDatatableEvents.DESELECT, (e, dt, type, indexes) => {
            this._updateButtonState(null, this.$editButton);
        });

        tabInstance.linesResizeDatatable.on('ipo-checked', (lineIds) => {
            this._enableButton(this.$createIpoButton, lineIds.length > 0)
            this._createIpo.setSelectedLine(lineIds);
        })
    }

    _checkSOConditions() {
        const autoApproveSO = salesOrderInfo.AutoApprove;
        const salesOrderStatus = salesOrderInfo.status;
        const isAuthorized = (this._authData.dateAuthorised != null)
        const isFullyShipped = salesOrderStatus === salesOrderInfo.complete.value
        this._authCheck = (autoApproveSO) ? true : !isAuthorized
        return this._authCheck && !isFullyShipped;
    }

    _updateButtonsWhenSelectNewRow(selectedRow) {
        const SOCondition = this._checkSOConditions();
        this._updatePostUnpostDeleteButton(selectedRow, SOCondition);
        this._updateCloseButton(selectedRow);
        this._updateConfirmButton(selectedRow);
        this._updateEditButton(selectedRow);
    }

    _updateCloseButton(selectedRow) {
        this._updateButtonState(selectedRow, this.$closeButton, 'closed')
        this._closeLine.updateSelectedRow(selectedRow);
    }

    _updateEditButton(selectedRow) {
        if (!selectedRow) {
            this._enableButton(this.$editButton, false);
        }
        else {
            this._enableButton(this.$editButton, !this._soGeneralInfo.IsSoCompleted && !selectedRow.inactive && !selectedRow.closed);
        }
    }

    _updateConfirmButton(selectedRow) {
        this._updateButtonState(selectedRow, this.$confirmButton, 'isConfirmed')
        this._confirmLines.updateSelectedRow(selectedRow)
    }

    _updatePostUnpostDeleteButton(selectedRow, SOCondition) {
        if (!selectedRow || selectedRow == null) {
            this.$postButton.prop('disabled', true);
            this.$unpostButton.prop('disabled', true);
            this.$deleteButton.prop('disabled', true);
            return
        }
        this._postLines.updateSelectedRow(selectedRow);
        const deleteLineConditions = this._postLines.checkDeleteLineConditions(selectedRow);

        this.$deleteButton.prop('disabled', !(deleteLineConditions && SOCondition));
        this.$postButton.prop('disabled', !(this._postLines.lineCanBePostedUnposted(selectedRow, true, SOCondition, this._authCheck)))
        this.$unpostButton.prop('disabled', !(this._postLines.lineCanBePostedUnposted(selectedRow, false, SOCondition, this._authCheck)))

        $('#tool-wrapper').remove();
        if (this.$postButton.prop('disabled') && selectedRow.postDisableReason != null) {
            const message = selectedRow.postDisableReason.replaceAll(",", "\n")
            const tooltipIcon = $(`<div class="d-inline-flex align-items-center" id="tool-wrapper">
                                    <img src="/img/icons/circle-info-yellow.svg" class=" rounded-circle bg-white post-disable-tooltip" height="14" data-bs-toggle="tooltip" data-bs-placement="bottom" title="${message}" /></div>`);
            this.$postButton.after(tooltipIcon)
        }
    }

    _updatePostUnpostAllButton() {
        if (!this._tabInstance[this._currentTab]) {
            this.$postAllButton.prop('disabled', true)
            this.$unpostAllButton.prop('disabled', true)
            return
        }
        const SOCondition = this._checkSOConditions();
        const datatable = this._tabInstance[this._currentTab].linesResizeDatatable.datatable;
        const allData = datatable.rows().data().toArray();

        let canPostAll = false
        let canUnpostAll = false;

        let total = 0;
        let postLineIDs = [];
        let unpostLineIDs = [];
        for (const row of allData) {
            let canPost = this._postLines.lineCanBePostedUnposted(row, true, SOCondition, this._authCheck)
            let canUnpost = this._postLines.lineCanBePostedUnposted(row, false, SOCondition, this._authCheck)
            if (canPost) {
                canPostAll = true;
                postLineIDs.push(row.lineId);
                total += row.totalRaw + row.taxRaw;
            }
            if (canUnpost) {
                canUnpostAll = true;
                unpostLineIDs.push(row.lineId)
            }
        }
        const info = {
            postLinesTotal: total,
            postIDs: postLineIDs,
            unpostIDs: unpostLineIDs,
        }
        this._postLines._postAllInfo = info;

        this.$postAllButton.prop('disabled', !canPostAll);
        this.$unpostAllButton.prop('disabled', !canUnpostAll);
    }

    _updateConfirmAllButton() {
        if (!this._tabInstance[this._currentTab]) {
            this.$confirmAllButton.prop('disabled', true);
            return;
        }

        const datatable = this._tabInstance[this._currentTab].linesResizeDatatable.datatable;
        const allData = datatable.rows().data().toArray();

        const hasUnconfirmedLines = allData.some(row => !row.isConfirmed);

        this.$confirmAllButton.prop('disabled', !hasUnconfirmedLines);
    }

    _updateButtonState(selectedRow, button, ...condition) {
        if (!selectedRow) {
            button.prop('disabled', true);
            return
        }
        if (condition.some(cond => selectedRow[cond])) {
            button.prop('disabled', true);
        }
        else {
            button.prop('disabled', false);
        }
    }

    _enableButton(button, isEnabled = true) {
        button.prop('disabled', !isEnabled);
    }

    _setupOnClickEvents() {
        this.$editButton.on('click', (e) => {
            const tabInstance = this._tabInstance[this._currentTab];
            this._editLineDialog.init();
            this._editLineDialog.open(this._selectedRow, tabInstance.detailsData, this._mainInfoSalesOrderData);
        });

        this.$addNewLineButton.on("click", async () => {
            this.addNewLine.initialize();
            this.addNewLine.openDialog();
        })

        this.$createIpoButton.on('click', async () => {
            this._createIpo.bindingPreferredWarehouse(this.preferredWarehouseName, this.preferredWarehouseNo);
            this._createIpo.openDialog();
        });
        
        this.$editAllButton.on('click', (e) => {
            this._editAllLinesDialog.open();
        });
    }

    _setupEditSuccessEvent() {
        this._editLineDialog.eventEmitter.on('saveSuccess', (soLineId, isEccnCodeChanged) => {
            this._sectionBox.loading(false);
            if (isEccnCodeChanged) {
                LinesTabService.notifyEccnSalesAsync(this._salesOrderId, soLineId);
            }
            this.trigger('editLineSuccess');
        });
    }
    _setupAddSuccessEvent() {
        this.addNewLine.$dialog.on('addNewLineSuccess', async (event, newSalesOrderLineId) => {
            this._selectedSalesOrderLineId = newSalesOrderLineId;
            if (newSalesOrderLineId > 0) {
                LinesTabService.notifyEccnSalesAsync(this._salesOrderId, newSalesOrderLineId);
            }
            this._sectionBox.loading(false);
        });
    }

    _updateEnableCheckbox() {
        const rows = this._tabInstance[this._currentTab].linesResizeDatatable.datatable.rows().data().toArray();
        this.checkBoxEnabled = rows.some(row =>
            row.isIPO && row.isChecked && row.isPosted && !row.isAllocated
        );
        this.trigger('enabledCheckBoxUpdated');
    }
}
