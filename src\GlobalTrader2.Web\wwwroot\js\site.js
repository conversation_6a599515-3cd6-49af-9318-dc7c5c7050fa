﻿$(function () {
    $("#ctl00_ddlClientByMaster_ddl").selectmenu();

    $("#sidebar_toggle_btn_1, #sidebar_toggle_btn_2").on("click", async function () {
        $("#leftSidebar").toggleClass("d-none d-flex");
        $("#toggle_icon").toggle();
        await GlobalTrader.ApiClient.putAsync(`/user-account/clients/set-left-panel-visible`, JSON.stringify(!$("#toggle_icon").is(":visible")));
    });

    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.forEach(function (tooltipTriggerEl) {
        // NOSONAR: Required to activate tooltip, side-effect only
        new bootstrap.Tooltip(tooltipTriggerEl); // NOSONAR
    });
});

//MainMenu
$(function () {
    const currentLocation = window.location.pathname;

    $('.dropbtn').each(function () {
        if ($(this).attr('href') === currentLocation) {
            $(this).addClass('active');
        }
    });
});

// Set these defaults once, typically in your main JS file or setup code
$(function () {
    if ($.validator) {
        $.validator.setDefaults({
            onfocusout: false,
            onkeyup: false,
            onclick: false,
            errorElement: "div",
            errorClass: "invalid-feedback",
            highlight: function (element) {
                $(element).addClass("is-invalid");
            },
            unhighlight: function (element) {
                $(element).removeClass("is-invalid");
            },
            errorPlacement: function (error, element) {
                const type = element.attr("data-input-type") || element.prop("tagName");
                switch (type) {
                    case 'SELECT':
                        error.insertAfter(element.parent());
                        break;
                    case 'DATE': {
                        error.insertAfter(element.parent());
                        break;
                    }
                    default:
                        error.insertAfter(element);
                        break;
                }
            },
            invalidHandler: function (event, validator) {
                $(validator.currentForm).prev(".form-error-summary").show();
            }
        });

        const requiredErrorMessage = window.localizedStrings.requiredField;
        const pleaseEnterANumberFrom = window.localizedStrings.pleaseEnterANumberFrom;
        const to = window.localizedStrings.to;
        const validateFutureTime = window.localizedStrings.validateFutureTime;
        const fileTooLargeErrorMessage = window.localizedStrings.fileTooLargeErrorMessage;
        const fileNotAllowedErrorMessage = window.localizedStrings.fileNotAllowedErrorMessage;

        $.extend($.validator.messages, {
            required: requiredErrorMessage,
            min: function (params) {
                return `${window.localizedStrings.PleaseEnterANumericValueGreaterThanOrEqualTo} ${params}`;
            },
            max: function (params) {
                return `${window.localizedStrings.PleaseEnterANumericValueLessThanOrEqualTo} ${params}`;
            }
        });
        $.validator.addMethod("noWhiteSpace", function (value, element) {
            return this.optional(element) || value.trim().length > 0;
        }, requiredErrorMessage);
        $.validator.addMethod("integerRangeCheck", function (value, element, params) {
            const numValue = Number(value);
            const minValue = Number(params.min);
            const maxValue = Number(params.max);
            return this.optional(element) || (numValue >= minValue && numValue <= maxValue);
        }, function (params) {
            const formattedMin = Number(params.min).toLocaleString();
            const formattedMax = Number(params.max).toLocaleString();
            return params.message || `${pleaseEnterANumberFrom} ${formattedMin} ${to} ${formattedMax}`;
        });
        $.validator.addMethod("requiresOtherField", function (value, element, otherFieldId) {
            return $(otherFieldId).val().trim() !== "";
        }, requiredErrorMessage)
        $.validator.addMethod("futureDate", function (value, element, params) {

            const { dateSelector, hourSelector, minuteSelector, currentDateTime } = params;
            const selectedDate = $(dateSelector).val();
            const selectedHour = parseInt($(hourSelector).val(), 10);
            const selectedMinute = parseInt($(minuteSelector).val(), 10);

            if (!selectedDate) {
                return true;
            }

            const [day, month, year] = selectedDate.split("/").map(Number);
            const selectedDateTime = new Date(year, month - 1, day, selectedHour, selectedMinute);
            const compareDateTime = currentDateTime ? new Date(currentDateTime) : new Date();

            return selectedDateTime > compareDateTime;
        }, validateFutureTime);
        $.validator.addMethod("notEmptyOrWhiteSpace", function (value, element) {
            const trimmedValue = $.trim(value);
            return trimmedValue !== "";
        }, requiredErrorMessage);
        $.validator.addMethod("notEmptyArray", function (value, element) {
            const trimmedValue = $.trim(value);
            const arr = JSON.parse(trimmedValue);
            return arr.length !== 0;
        }, requiredErrorMessage);

        $.validator.addMethod("extension", function (value, element, param) {
            if (this.optional(element)) {
                return true;
            }
            const fileName = value.toLowerCase();
            const allowedExtensions = Array.isArray(param) ? param : param.split('|');
            const fileExtension = fileName.substring(fileName.lastIndexOf(".")).toLowerCase();
            return allowedExtensions.includes(fileExtension);
        }, fileNotAllowedErrorMessage);

        $.validator.addMethod("filesizeMB", function (value, element, param) {
            // Convert param (MB) to bytes for comparison
            const maxSizeBytes = param * 1024 * 1024;
            return this.optional(element) || (element.files[0] && element.files[0].size <= maxSizeBytes);
        }, function (param) {
            return fileTooLargeErrorMessage.replace("{0}", param);
        });
        $.validator.addMethod("decimalRangeCheck", function (value, element, params) {
            let integerPartLength = params.max.split('.')[0].length;
            let paddedNum = value.padStart(integerPartLength + params.decimalPartLength + 1, '0');

            return this.optional(element) || (paddedNum >= params.min && paddedNum <= params.max);
        }, function (params) {
            const formattedMin = Number(params.min).toLocaleString(undefined, { minimumFractionDigits: 5 });
            const formattedMax = Number(params.max).toLocaleString(undefined, { minimumFractionDigits: 5 });
            return params.message || `${pleaseEnterANumberFrom} ${formattedMin} ${to} ${formattedMax}`;
        });
    }
})

$.extend($.ui.dialog.prototype.options, {
    autoOpen: false,
    modal: true,
    draggable: false,
    resizable: false
});

//Get params value
function getParameterByName(name) {
    const url = window.location.href;
    const nameRegex = name.replace(/[[]]/g, '\\$&');
    const regex = new RegExp('[?&]' + nameRegex + '(=([^&#]*)|&|#|$)');
    const results = regex.exec(url);
    if (!results) return null;
    if (!results[2]) return '';
    return decodeURIComponent(results[2].replace(/\+/g, ' '));
}

/* Disable scroll when open dialog  */
let OPENING_DIALOG = 0;
$(document).on("dialogopen", function (event, ui) {
    OPENING_DIALOG++;
    $("body").addClass("no-scroll")
    $(event.target).dialog("widget").find('.ui-dialog-titlebar-close').css('display', 'none');
});
$(document).on("dialogclose", function (event, ui) {
    OPENING_DIALOG--;

    if (OPENING_DIALOG == 0) {
        $("body").removeClass("no-scroll")
    }
});
/* End of Disable scroll when open dialog  */

// Set maxHeight when any dialog opens
$(document).on("dialogopen", ".ui-dialog-content", function () {
    updateDialogMaxHeight($(this));
});

// Update maxHeight for all visible dialogs on window resize
$(window).on("resize", function () {
    updateDialogMaxHeight($(".ui-dialog-content:visible"));
});

function initializeDatePicker(selector, options = {}) {
    $(selector).datepicker({
        dateFormat: "dd/mm/yy", // Default format        
        changeMonth: true,
        changeYear: true,
        showAnim: "slideDown",
        showOn: "both",
        buttonImage: "/img/icons/calendar.svg",
        buttonImageOnly: true,
        ...options, // Extend options if passed    
    });
}

function normalizeString(input) {
    if (input == null) {
        return "";
    }
    let normalized = input.trim();
    normalized = normalized.replace(/\r\n/g, "\n");
    return normalized;
}

function parseDate(stringDate) {
    if (!stringDate) return null;
    // parse string date with format "dd/mm/yyyy"
    const parts = stringDate.split('/');
    const day = parseInt(parts[0], 10);
    const month = parseInt(parts[1], 10) - 1;
    const year = parseInt(parts[2], 10);
    return new Date(year, month, day)
}

function toYMD(date) {
    return date.getFullYear() +
        '-' + String(date.getMonth() + 1).padStart(2, '0') +
        '-' + String(date.getDate()).padStart(2, '0');
}

//Set Datetime include hour and minute
function mergeDateTime(date, hour, minute) {
    const [day, month, year] = date.split("/");
    const localDateTime = new Date(year, month - 1, day, hour, minute);
    const utcDateTime = new Date(localDateTime.getTime() - (localDateTime.getTimezoneOffset() * 60000));
    return utcDateTime.toISOString();
}

let getCurrentUserTimerId;
async function getCurrentUser() {
    await GlobalTrader.ApiClient.getAsync('/auth/me');
}
function resetTimeoutTimer() {
    clearTimeout(getCurrentUserTimerId);
    if (window.userConfig) {
        getCurrentUserTimerId = setTimeout(getCurrentUser, (window.userConfig.idleUserSessionTimeout * 60 * 1000) + 5);
    }
}
$(document).ajaxComplete(function () {
    resetTimeoutTimer();
});
resetTimeoutTimer();

function formatCurrencyRate(data, selectedCurrencyCode) {
    return formatCurrency(data, null, selectedCurrencyCode, 5, false);
};

function formatCurrency(objAmount, locale, strCurrency, intFigures, blnCommaThousands) {
    const amount = objAmount ?? 0;

    const options = {
        minimumFractionDigits: intFigures,
        maximumFractionDigits: intFigures,
        useGrouping: blnCommaThousands
    };

    const formatted = Number(amount).toLocaleString(locale || 'en-US', options);
    return strCurrency ? `${formatted} ${strCurrency.trim()}` : formatted;
}

function updateDialogMaxHeight($dialogs) {
    const maxHeight = $(window).height();
    $dialogs.each(function () {
        $(this).dialog("option", "maxHeight", maxHeight);
    });
}