using GlobalTrader2.Aggregator.UseCases.Orders.BOM.BOMReleaseRequirementFlow.Commands;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.ReleaseRequirement.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyReleaseBom;
using GlobalTrader2.Core.Bases;
using MediatR;
using Moq;
using System.Globalization;

namespace GlobalTrader2.Aggregator.Test.Orders.BOM.BOMReleaseRequirementFlow.Commands;

public class BomReleaseRequirementFlowHandlerTest
{
    private readonly Mock<IMediator> _mockMediator;
    private readonly BomReleaseRequirementFlowHandler _handler;

    public BomReleaseRequirementFlowHandlerTest()
    {
        _mockMediator = new Mock<IMediator>();
        _handler = new BomReleaseRequirementFlowHandler(_mockMediator.Object);
    }

    [Fact]
    public async Task Handle_Should_Return_Success_When_Release_And_Notification_Succeed()
    {
       // Arrange
       var command = new BomReleaseRequirementFlowCommand
       {
           CustomerRequirementId = 1,
           BomId = 1,
           UpdatedBy = 1,
           BOMCode = "TEST001",
           BOMName = "Test BOM",
           BomCompanyName = "Test Company",
           BomCompanyNo = 1,
           RequestedBy = 1,
           Subject = "Test Release",
           LoginEmail = "<EMAIL>",
           LoginId = 1,
           ClientId = 1,
           SenderName = "Test User",
           HUBRFQStatus = "Released",
           ClientName = "Test Client",
           IsPoHub = true,
           ReqSalesPerson = 1,
           SupportTeamMemberNo = 1,
           ClientCurrencyID = 1,
           ClientCurrencyCode = "USD",
           CultureInfo = CultureInfo.InvariantCulture,
           HUBRFQHyperLink = "http://test.com"
       };

       var releaseResponse = new BaseResponse<bool> { Success = true, Data = true };
        
       _mockMediator.Setup(x => x.Send(It.IsAny<ReleaseRequirementCommand>(), It.IsAny<CancellationToken>()))
                   .ReturnsAsync(releaseResponse);

       // Act
       var result = await _handler.Handle(command, CancellationToken.None);

       // Assert
       Assert.True(result.Success);
       Assert.False(result.Data);

       _mockMediator.Verify(x => x.Send(It.IsAny<ReleaseRequirementCommand>(), It.IsAny<CancellationToken>()), Times.Once);
       // Không verify NotifyReleaseBomCommand vì handler không gọi nó
    }

    [Fact]
    public async Task Handle_Should_Return_Failure_When_Release_Fails()
    {
        // Arrange
        var command = new BomReleaseRequirementFlowCommand
        {
            CustomerRequirementId = 1,
            BomId = 1,
            UpdatedBy = 1,
            Subject = "Test Release",
            LoginEmail = "<EMAIL>",
            SenderName = "Test User",
            HUBRFQStatus = "Released",
            ClientName = "Test Client",
            ClientCurrencyCode = "USD",
            CultureInfo = CultureInfo.InvariantCulture
        };

        var releaseResponse = new BaseResponse<bool> { Success = false, Message = "Release failed" };
        
        _mockMediator.Setup(x => x.Send(It.IsAny<ReleaseRequirementCommand>(), It.IsAny<CancellationToken>()))
                    .ReturnsAsync(releaseResponse);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.True(result.Success);
        Assert.False(result.Data);
        Assert.Null(result.Message);

        _mockMediator.Verify(x => x.Send(It.IsAny<ReleaseRequirementCommand>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockMediator.Verify(x => x.Send(It.IsAny<NotifyReleaseBomCommand>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task Handle_Should_Throw_ArgumentNullException_When_Request_Is_Null()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => _handler.Handle(null!, CancellationToken.None));
    }
}
