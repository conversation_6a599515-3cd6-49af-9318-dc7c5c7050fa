using System.Globalization;

namespace GlobalTrader2.Aggregator.UseCases.Orders.BOM.BOMReleaseRequirementFlow.Commands
{
    public record BomReleaseRequirementFlowCommand : IRequest<BaseResponse<bool>>
    {
        public int CustomerRequirementId { get; set; }
        public int BomId { get; set; }
        public string? BOMCode { get; set; }
        public string? BOMName { get; set; }
        public string? BomCompanyName { get; set; }
        public int BomCompanyNo { get; set; }
        public int UpdatedBy { get; set; }
        public int RequestedBy { get; set; }
        public required string Subject { get; set; }
        public required string LoginEmail { get; set; }
        public int LoginId { get; set; }
        public int ClientId { get; set; }
        public required string SenderName { get; set; } = string.Empty;
        public required string HUBRFQStatus { get; set; } = string.Empty;
        public required string ClientName { get; set; } = string.Empty;
        public bool IsPoHub { get; set; }
        public int ReqSalesPerson { get; set; }
        public int SupportTeamMemberNo { get; set; }
        public int ClientCurrencyID { get; set; }
        public required string ClientCurrencyCode { get; set; }
        public required CultureInfo CultureInfo { get; set; }
        public string? HUBRFQHyperLink { get; set; } = string.Empty;
    }
}
