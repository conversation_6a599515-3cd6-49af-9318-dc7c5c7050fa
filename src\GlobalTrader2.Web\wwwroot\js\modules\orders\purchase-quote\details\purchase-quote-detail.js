import BindingService from "../../../../helper/binding.service.js?v=#{BuildVersion}#";
import { SectionBox } from "../../../../components/base/section-box.component.js?v=#{BuildVersion}#";
import { PRStatus } from "../constants/purchase-request-status.js?v=#{BuildVersion}#";
import { AddEditPriceRequestDialog } from "../add-edit-price-request-dialog.js?v=#{BuildVersion}#";
import { PartTableButton } from "../../../../components/button/part-table-button.js?v=#{BuildVersion}#";

const mainInfoBox = $("#main-information-box");
const hurfqLineBox = $("#hurfq-lines-box");

const priceRequestQuoteBox = $("#price-request-quote-box");
const logBox = $("#log-box");
const uploadedDocumentBox = $("#uploaded-document-box");

let state = {
    detailInfo: {},
    selectedHUBRFQLine: null,
};

$(async () => {
    setUpSectionBox();

    await getDetailById(stateValue.id);
    updateMainInformation(state.detailInfo);

    initDialog();
    await initHURFQLineTable();
    await initPriceRequestQuoteTable();
    await initLogTable();
    initForm();
    handleNotification();
    registerEvents();
});

function setUpSectionBox() {
    // Set up main information section box with onRefreshed.msb event pattern
    state.mainInformationSectionBox = new SectionBox("#main-information-box", {
        loading: false,
        loadingContentId: 'main-information-content'
    });

    state.mainInformationSectionBox.init();

    // Listen for the onRefreshed.msb custom event
    state.mainInformationSectionBox.on('onRefreshed.msb', async () => {
        mainInfoBox.section_box("option", { loading: true });
        try {
            await getDetailById(stateValue.id);
            // Bind data to UI
            updateMainInformation(state.detailInfo);
            updateButtonStates();
        } finally {
            mainInfoBox.section_box("option", { loading: false });
        }
    });

    priceRequestQuoteBox.section_box({
        loading: false,
        loadingContentId: 'price-request-quote-content',
        onRefreshClick: async () => {
            priceRequestQuoteBox.section_box("option", { loading: true });
            await state.priceRequestQuoteTable.displaySpinerWhenCallingAsync(
                GlobalTrader.Helper.reloadResizeDatatable,
                state.priceRequestQuoteTable.table,
                null
            );
            priceRequestQuoteBox.section_box("option", { loading: false });
        },
    });

    hurfqLineBox.section_box({
        loading: false,
        onRefreshClick: () => { },
    });

    uploadedDocumentBox.section_box({
        loading: false,
        onRefreshClick: () => { },
    });

    logBox.section_box({
        loading: false,
        onRefreshClick: () => { },
    });
}

async function getDetailById(id) {
    let response = await GlobalTrader.ApiClient.getAsync(
        `/orders/purchase-quote/${id}`
    );
    const data = response.data;

    state.detailInfo.pOQuoteId = data.pOQuoteId;
    state.detailInfo.divisionNo = data.divisionNo;
    state.detailInfo.divisionName = GlobalTrader.StringHelper.setCleanTextValue(data.divisionName);
    state.detailInfo.notes = GlobalTrader.StringHelper.setCleanTextValue(data.notes);
    state.detailInfo.salesPersonName = GlobalTrader.StringHelper.setCleanTextValue(data.salesPersonName);
    state.detailInfo.salesPersonNo = data.salesPersonNo;
    state.detailInfo.salesPersonName = GlobalTrader.StringHelper.setCleanTextValue(data.salesPersonName);
    state.detailInfo.lastUpdatedByInText = data.lastUpdatedByInText;
}

function updateMainInformation(data) {

    BindingService.bindToElements($('#main-information-content'), data);
}

async function initHURFQLineTable() {
    try {
        state.hurfqLineTable = new GlobalTrader.Common.SearchTablePageBase({
            sectionBoxSelector: "#hurfq-lines-box",
            tableSelector: "#hurfq-lines-table",
            tableOptions: {
                ajax: {
                    url: `/api/orders/purchase-quote/${stateValue.id}/quote-lines`,
                    error: function (xhr, error, code) {
                        showToast('danger', 'An unexpected error occurred in HURFQ Lines table');
                    }
                },
                language: {
                    emptyTable: `<i>${localizedTitles.noData}</i>`
                },
                searching: false,
                select: true,
                columnDefs: [
                    { type: 'string', targets: '_all' }
                ],
                columns: [
                    {
                        title: "Req No",
                        data: "customerRequirementNo",
                        width: "10%",
                        className: "text-break",
                    },
                    {
                        title: "Part No",
                        data: "part",
                        width: "10%",
                        className: "text-break",
                    },
                    {
                        title: "HUBRFQ",
                        data: "bomNo",
                        width: "10%",
                        className: "text-break",
                    },
                    {
                        title: "Quantity",
                        data: "quantity",
                        width: "10%",
                        className: "text-break",
                    },
                    {
                        title: "Target Price",
                        data: "targetPrice",
                        width: "10%",
                        className: "text-break",
                    },
                    {
                        title: "MSL",
                        data: "msl",
                        width: "10%",
                        className: "text-break",
                    },
                    {
                        title: "Factory Sealed",
                        data: "factorySealed",
                        width: "10%",
                        className: "text-break",
                    },
                    {
                        title: "AS9120",
                        data: "aS9120",
                        width: "10%",
                        className: "text-break",
                    }
                ],
                rowId: "purchaseRequestLineId",
                layout: {
                    bottomStart: null,
                    bottomEnd: null,
                    topStart: null,
                    topEnd: null
                }
            }
        });

        state.hurfqLineTable.onSelectedItem = function (e, dt, type, indexes) {
            GlobalTrader.Common.SearchTablePageBase.prototype.onSelectedItem.call(this, e, dt, type, indexes);

            if (type === "row") {
                updateButtonStates();
            }
            $('#hubrfq-lines-table-delete-btn')
                .prop("disabled", state.hurfqLineTable.selectedRow.prStatus === PRStatus.Imported)
        };

        state.hurfqLineTable.onDeselectedItem = function (e, dt, type, indexes) {
            GlobalTrader.Common.SearchTablePageBase.prototype.onDeselectedItem.call(this, e, dt, type, indexes);

            if (type === "row") {
                updateButtonStates();
            }
            $('#hubrfq-lines-table-delete-btn').prop("disabled", true)
        };

        state.hurfqLineTable.init();
    } catch (error) {
        console.error("Error initializing HURFQ Line Table:", error);
        throw error;
    }
}

async function initPriceRequestQuoteTable() {
    try {
        state.priceRequestQuoteTable = new GlobalTrader.Common.SearchTablePageBase({
            sectionBoxSelector: "#price-request-quote-box",
            tableSelector: "#price-request-quote-table",
            tableOptions: {
                ajax: {
                    url: `/api/orders/purchase-quote/57005/purchase-request-line-detail`,
                    error: function (xhr, error, code) {
                        showToast('danger', 'An unexpected error occurred in Price Request Quote table');
                    }
                },
                language: {
                    emptyTable: `<i>${localizedTitles.noData}</i>`
                },
                searching: false,
                select: true,
                columnDefs: [
                    { type: 'string', targets: '_all' }
                ],
                columns: [
                    {
                        title: "Supplier",
                        data: "companyName",
                        width: "20%",
                        className: "text-break",
                    },
                    {
                        title: "SPQ",
                        data: "spq",
                        width: "15%",
                        className: "text-break",
                    },
                    {
                        title: "Lead Time (Weeks)",
                        data: "leadTime",
                        width: "10%",
                        className: "text-break",
                    },
                    {
                        title: "ROHS",
                        data: "roHSStatus",
                        width: "10%",
                        className: "text-break",
                    },
                    {
                        title: "Factory Sealed",
                        data: "factorySealed",
                        width: "10%",
                        className: "text-break",
                    },
                    {
                        title: "MSL",
                        data: "msl",
                        width: "10%",
                        className: "text-break",
                    },
                    {
                        title: "Unit Price",
                        data: "currencyCode",
                        width: "10%",
                        className: "text-break",
                    },
                    {
                        title: "Base Currency?",
                        data: "currencyCode",
                        className: "text-break",
                    },
                ],
                rowId: "purchaseRequestLineDetailId",
                layout: {
                    bottomStart: null,
                    bottomEnd: null,
                    topStart: null,
                    topEnd: null
                }
            }
        });

        state.priceRequestQuoteTable.onSelectedItem = function (e, dt, type, indexes) {
            GlobalTrader.Common.SearchTablePageBase.prototype.onSelectedItem.call(this, e, dt, type, indexes);
            $("#price-request-edit-btn").prop("disabled", !state.hurfqLineTable?.selectedRow || state.hurfqLineTable.selectedRow.pqStatus === 5);
        };

        state.priceRequestQuoteTable.onDeselectedItem = function (e, dt, type, indexes) {
            GlobalTrader.Common.SearchTablePageBase.prototype.onDeselectedItem.call(this, e, dt, type, indexes);
            $("#price-request-edit-btn").prop("disabled", true);
        };

        state.priceRequestQuoteTable.init();
    } catch (error) {
        console.error("Error initializing Price Request Quote Table:", error);
        throw error;
    }
}

async function initLogTable() {
    try {
        state.logTable = new GlobalTrader.Common.SearchTablePageBase({
            sectionBoxSelector: "#log-box",
            tableSelector: "#log-table",
            tableOptions: {
                ajax: {
                    url: `/api/orders/purchase-quote/line-log`,
                    type: 'POST',
                    contentType: 'application/json',
                    data: function (d) {
                        // `d` is the default DataTables request payload (with paging, filtering, etc.)

                        // Add your custom data:
                        let customData = {
                            pOQuoteID: stateValue.id
                        };

                        return JSON.stringify(customData);
                    }
                },
                language: {
                    emptyTable: `<i>${localizedTitles.noData}</i>`
                },
                searching: false,
                select: false,
                columnDefs: [
                    { type: 'string', targets: '_all' }
                ],
                columns: [
                    {
                        title: "Id",
                        data: "purchaseQuoteLineId",
                        width: "30%",
                        className: "text-break",
                    }
                ],
                rowId: "purchaseQuoteLineId",
                layout: {
                    bottomStart: null,
                    bottomEnd: null,
                    topStart: null,
                    topEnd: null
                }
            }
        });

        state.logTable.init();
    } catch (error) {
        console.error("Error initializing Log Table:", error);
        throw error;
    }
}
async function registerEvents() {
    state.addPriceRequestBtn = new PartTableButton("#price-request-add-btn", async function () {
        if (state.hurfqLineTable.selectedRow) state.addEditPriceRequestDialog.showForm(state.hurfqLineTable.selectedRow.part, true, null, state.hurfqLineTable.selectedRow.purchaseRequestLineId);
    })

    state.editPriceRequestBtn = new PartTableButton("#price-request-edit-btn", async function () {
        if (state.hurfqLineTable.selectedRow && state.priceRequestQuoteTable.selectedRow) state.addEditPriceRequestDialog.showForm(state.hurfqLineTable.selectedRow.part, false, state.priceRequestQuoteTable.selectedRow, state.hurfqLineTable.selectedRow.purchaseRequestLineId);
    })
}

function initDialog() {
    $("#hubrfq-lines-table-delete-btn").on("click", function (event) {
        event.stopPropagation();
        $("#hubrfq-lines-table-delete-btn-dialog").dialog("open");
    });
    $("#hubrfq-lines-table-delete-btn-dialog").dialog({
        height: 'auto',
        width: 'auto',
        buttons: [
            {
                id: 'HUBRFQLinesDialogYesButton',
                text: localize.yes,
                click: async function () {
                    $('#HUBRFQLinesDialogYesButton').prop('disabled',true)
                    let response = await handleDeleteHUBRFQLine(state.hurfqLineTable.selectedRow.purchaseRequestLineId)
                    $(this).dialog("close")
                    $('#HUBRFQLinesDialogYesButton').prop('disabled', false)
                    if (response.success) {
                        state.hurfqLineTable.table.ajax.reload()
                        $('#hubrfq-lines-table-delete-btn').prop("disabled", true)
                        showToast('success', localize.deleteSuccess)
                    } else {
                        showToast('danger', localize.deleteFail)
                    }
                },
                addClass: 'btn btn-primary fw-normal',
                html: `<img src="/img/icons/xmark.svg" alt="${localize.yes}"> ${localize.yes}`
            },
            {
                text: localize.no,
                click: function () {
                    $(this).dialog("close")
                },
                addClass: 'btn btn-danger fw-normal',
                html: `<img src="/img/icons/xmark.svg" alt="${localize.no}"> ${localize.no}`
            }
        ],
        open: function (event, ui) {
            $('.ui-dialog-titlebar-close').css('display', 'none')
        }
    });
}

async function handleDeleteHUBRFQLine(id) {
    return await GlobalTrader.ApiClient
        .deleteAsync(`orders/purchase-quote/purchase-request-line/${id}/delete`)
}

function initForm() {
    state.addEditPriceRequestDialog = new AddEditPriceRequestDialog("#add-edit-price-request-dialog", {});
    state.addEditPriceRequestDialog.init();
}

function handleNotification() {
    const handleAddEditError = async function () {
        showToast('danger', window.localizedStrings.unexpectedError);
    };

    state.addEditPriceRequestDialog.$form.on('errorResponse.mf', handleAddEditError);
    state.addEditPriceRequestDialog.$form.on('errorRequest.mf', handleAddEditError);
    state.addEditPriceRequestDialog.$form.on('closedWithSuccessedResponse.mf', async function () {
        priceRequestQuoteBox.find('.section-box-refesh-button').trigger('click');
    })
}

function updateButtonStates() {
    if (state.addPriceRequestBtn) {
        $("#price-request-add-btn").prop("disabled", !state.hurfqLineTable?.selectedRow || state.hurfqLineTable.selectedRow.pqStatus === 5);
    }
} 