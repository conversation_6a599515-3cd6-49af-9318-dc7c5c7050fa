﻿using GlobalTrader2.Aggregator.UseCases.DataList.GetFilterBOMLinesList;
using GlobalTrader2.Aggregator.UseCases.Orders.BOM;
using GlobalTrader2.Aggregator.UseCases.Orders.BOM.BOMItemDetailFlow.Queries;
using GlobalTrader2.Aggregator.UseCases.Orders.BOM.BOMNoBidRequirementFlow.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.BOM.BOMReleaseAllFlow.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.BOM.BOMReleaseRequirementFlow.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.BOM.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.BOM.CreateExpediteNoteFlow.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.BOM.CustomerRequirementNoBidFlow.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyReleaseBom;
using GlobalTrader2.Aggregator.UseCases.Orders.PartWatch.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.Requirements.ExportToCSV.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.Requirements.NotifyPurchaseRequestBom.Commands;
using GlobalTrader2.Aggregator.UseCases.Orders.Requirements.RLStockNotification.Commands;
using GlobalTrader2.Core.Constants;
using GlobalTrader2.Dto.BOM;
using GlobalTrader2.Dto.DataListNugget;
using GlobalTrader2.Dto.Datatables;
using GlobalTrader2.Dto.Orders.Requirements;
using GlobalTrader2.Dto.Sourcing;
using GlobalTrader2.Orders.UI.ViewModel;
using GlobalTrader2.Orders.UI.ViewModel.BOM;
using GlobalTrader2.Orders.UI.ViewModel.HubFRQ;
using GlobalTrader2.Orders.UI.ViewModel.HubRFQ;
using GlobalTrader2.Orders.UserCases.Orders.BOM;
using GlobalTrader2.Orders.UserCases.Orders.BOM.AssignmentHistory.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMSourcingResultForRelease.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMStatus.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.BOMStatusToClosed.Commands;
using GlobalTrader2.Orders.UserCases.Orders.BOM.Commands;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CustomerRequirementRecallNoBid.Commands;
using GlobalTrader2.Orders.UserCases.Orders.BOM.DataListExport.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.ExpediteNote.Commands;
using GlobalTrader2.Orders.UserCases.Orders.BOM.ExportToExcel.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQCommunicationNote.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQHasRLStock.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQSourcingResults.Command;
using GlobalTrader2.Orders.UserCases.Orders.BOM.HUBRFQSourcingResults.Query;
using GlobalTrader2.Orders.UserCases.Orders.BOM.NotifyMessageSupplier.Commands;
using GlobalTrader2.Orders.UserCases.Orders.BOM.PurchaseQuote.Commands;
using GlobalTrader2.Orders.UserCases.Orders.BOM.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.Queries.GetLyticaManufacturer;
using GlobalTrader2.Orders.UserCases.Orders.BOM.UpdateCustRequirementByBomID.Commands;
using GlobalTrader2.Orders.UserCases.Orders.PVVBOM.Commands;
using GlobalTrader2.Orders.UserCases.Orders.PVVBOM.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.PVVBOMQuestion.Queries.Dtos;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.DeleteBomItem.Commands;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.HubrfqExport.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.UnReleaseBomItem.Commands;
using GlobalTrader2.Orders.UserCases.Orders.TempPVVBOMQuestion.Commands;
using GlobalTrader2.Orders.UserCases.Orders.TempPVVBOMQuestion.Queries.Dtos;
using GlobalTrader2.SharedUI;
using GlobalTrader2.SharedUI.Helper;
using GlobalTrader2.SharedUI.Interfaces;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Localization;
using Microsoft.Net.Http.Headers;
using System.Globalization;
using System.Net.Mime;
using SendToPurchaseHubRequest = GlobalTrader2.Orders.UI.ViewModel.BOM.SendToPurchaseHubRequest;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers;

[ApiController]
[Authorize]
[Route("api/orders/bom")]
public class HubrfqController : ApiBaseController
{
    private readonly IMediator _mediator;
    private readonly IMapper _mapper;
    private readonly SessionManager _sessionManager;
    private readonly IStringLocalizer<SharedUI.Misc> _miscLocalizer;
    private readonly IStringLocalizer<SharedUI.Reports> _reportLocalizer;
    private readonly IStringLocalizer<MessageResources> _messageLocalizer;
    private readonly IStringLocalizer<SharedUI.NotFound> _notFoundLocalizer;
    private readonly IExportService _exportService;
    private readonly IConfiguration _configuration;
    private readonly IStringLocalizer _printingLocalizer;
    private readonly string _hubrfqReleasedEmailHeader;

    public HubrfqController(
    IMediator mediator,
    SessionManager sessionManager,
    IMapper mapper,
    IStringLocalizer<SharedUI.ReportTitles> reportTitlesLocalizer,
    IStringLocalizer<SharedUI.Reports> reportLocalizer,
    IStringLocalizer<MessageResources> messageLocalizer,
    IStringLocalizer<SharedUI.Misc> miscLocalizer,
    IStringLocalizer<SharedUI.NotFound> notFoundLocalizer,
    IConfiguration configuration,
    IExportService exportService,
    IStringLocalizer<Printing> printingLocalizer) : base()
    {
        _mediator = mediator;
        _mapper = mapper;
        _sessionManager = sessionManager;
        _miscLocalizer = miscLocalizer;
        _reportLocalizer = reportLocalizer;
        _messageLocalizer = messageLocalizer;
        _notFoundLocalizer = notFoundLocalizer;
        _exportService = exportService;
        _configuration = configuration;
        _printingLocalizer = printingLocalizer;

        _hubrfqReleasedEmailHeader = _messageLocalizer["HUBRFQReleased"];
    }

    [HttpGet("search-id")]
    public async Task<IActionResult> GetHUBRFQIdByNumber([FromQuery] string bomName)
    {
        return Ok(await _mediator.Send(new GetBomIdByNameQuery()
        {
            ClientNo = ClientId,
            BomName = bomName,
        }));
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetCustomerRequirement(int id)
    {
        var result = await _mediator.Send(new GetBOMDetailsQuery
        {
            Id = id,
            LoginId = UserId
        });
        if (result.Data is not null)
        {
            result.Data.LastUpdated = LocalizerHelper.FormatDLUP(result.Data.DLUP, result.Data.UpdatedByEmployeeName, _miscLocalizer, CultureInfo.CurrentCulture);
        }
        return Ok(result);
    }

    [HttpGet("communication-note/{id}")]
    public async Task<IActionResult> GetCommunicationNotes(int id)
    {
        return Ok(await _mediator.Send(new GetAllHubrfqCommunicationNoteQuery
        {
            HUBRFQId = id,
            ClientID = ClientId
        }));
    }

    [HttpPost("send-to-purchase-hub")]
    public async Task<IActionResult> SendToPurchaseHub(SendToPurchaseHubRequest request, CancellationToken cancellationToken)
    {
        var result = await _mediator.Send(new UpdatePurchaseQuoteCommand()
        {
            BOMId = request.Id,
            UpdatedBy = UserId,
            BOMStatus = (int)BOMStatus.RPQ,
            AssignUserNo = request.AssignUserNo
        }, cancellationToken);
        if (result.Success)
        {
            List<int> tologinIds = [request.AssignUserNo];
            var mailGroupId = _sessionManager.GetInt32(SessionKey.POHubMailGroupId) ?? 0;

            // Send notification to the purchase hub
            await _mediator.Send(new CreateNotifyPurchaseRequestBomCommand
            {
                ToLogins = [.. tologinIds],
                ToGroups = [mailGroupId],
                Subject = string.Format(_messageLocalizer["PurchaseRequest"], request.Name),
                BomCode = request.Code,
                BomId = request.Id,
                BomName = request.Name,
                BomCompanyName = request.Company,
                BomCompanyNo = request.CompanyNo,
                LoginEmail = _sessionManager.GetString(SessionKey.LoginEmail) ?? string.Empty,
                LoginId = UserId,
                RecipientLoginIDsCC = request.RecipientLoginIDs ?? [],
                IsPOHub = _sessionManager.IsPOHub,
                ClientName = _sessionManager.GetString(SessionKey.ClientName) ?? string.Empty,
                UrlBase = RequestHelper.GetApplicationUrl(HttpContext.Request),
                Sender = string.Format($"{HttpContext.Session.GetString(SessionKey.LoginFirstName)} {HttpContext.Session.GetString(SessionKey.LoginLastName)}"),
            }, cancellationToken);

            // Get customer requirements
            var customerRequirementsRes = await _mediator.Send(new GetAllHUBRFQHasRLStockQuery
            {
                BomId = request.Id
            }, cancellationToken);

            // Send RLStock
            await _mediator.Send(new CreateRLStockNotificationCommand
            {
                BOMCode = request.Code,
                BomCompanyName = request.Company,
                CustomerRequirements = _mapper.Map<IList<HUBRFQHasRLStockDto>>(customerRequirementsRes.Data),
                Id = request.Id,
                IsFromBOM = true,
                LoginID = UserId,
                Subject = string.Format(_messageLocalizer["RLStockSubject1"], request.Code),
                HubRFQLink = $"{RequestHelper.GetApplicationUrl(HttpContext.Request)}/Orders/HUBRFQ/Details?bom={request.Id}",
                Sender = string.Format($"{HttpContext.Session.GetString(SessionKey.LoginFirstName)} {HttpContext.Session.GetString(SessionKey.LoginLastName)}"),
                SenderEmail = _sessionManager.GetString(SessionKey.LoginEmail) ?? string.Empty
            }, cancellationToken);

            return Ok(result);
        }
        return Ok();
    }

    [HttpPost("release-all")]
    public async Task<IActionResult> ReleaseAll(BOMReleaseAllRequest request, CancellationToken cancellationToken)
    {
        var reqSalePerson = 0;
        var supportTeamMemberNo = 0;

        if (_sessionManager.IsPOHub)
        {
            int.TryParse(request.ReqSalesPerson.TrimEnd('|'), out reqSalePerson);
            int.TryParse(request.SupportTeamMemberNo.TrimEnd('|'), out supportTeamMemberNo);
        }
        else
        {
            return Forbid("Only POHub users are authorized to release all BOM items.");
        }

        var urlPath = Navigations.BOMDetailWithId(request.Id).CtaUri;
        var urlBase = RequestHelper.GetApplicationUrl(HttpContext.Request);

        return Ok(await _mediator.Send(new BomReleaseAllFlowCommand
        {
            BomId = request.Id,
            UpdatedBy = UserId,
            LoginEmail = LoginEmail,
            Subject = $"{_hubrfqReleasedEmailHeader} ( {request.Name} )",
            BOMCode = request.Code,
            BomCompanyName = request.Company,
            BomCompanyNo = request.CompanyNo,
            RequestedBy = request.RequestToPOHubBy ?? 0,
            ReqSalesPerson = reqSalePerson,
            SupportTeamMemberNo = supportTeamMemberNo,
            BOMName = request.Name,
            ClientId = ClientId,
            LoginId = UserId,
            SenderName = string.Format(_sessionManager.LoginFullName),
            HUBRFQStatus = _hubrfqReleasedEmailHeader,
            ClientName = _sessionManager.ClientName!,
            CustomerRequirementId = request.CustomerRequirementId,
            IsPoHub = _sessionManager.IsPOHub,
            ClientCurrencyID = _sessionManager.GetInt32(SessionKey.ClientCurrencyID) ?? 0,
            ClientCurrencyCode = _sessionManager.GetString(SessionKey.ClientCurrencyCode) ?? string.Empty,
            CultureInfo = _sessionManager.GetCurrentCulture(),
            HUBRFQHyperLink = $"{urlBase}{urlPath}"
        }, cancellationToken));
    }

    [HttpPost("release-requirement")]
    public async Task<IActionResult> ReleaseRequirement(ReleaseRequirementRequest request, CancellationToken cancellationToken)
    {
        var reqSalePerson = 0;
        var supportTeamMemberNo = 0;

        if (_sessionManager.IsPOHub)
        {
            int.TryParse(request.ReqSalesPerson?.TrimEnd('|'), out reqSalePerson);
            int.TryParse(request.SupportTeamMemberNo?.TrimEnd('|'), out supportTeamMemberNo);
        }
        else
        {
            return Forbid("Only POHub users are authorized to release requirements.");
        }

        var urlPath = Navigations.BOMDetailWithId(request.BomId ?? 0).CtaUri;
        var urlBase = RequestHelper.GetApplicationUrl(HttpContext.Request);

        return Ok(await _mediator.Send(new BomReleaseRequirementFlowCommand
        {
            CustomerRequirementId = request.CustomerRequirementId ?? 0,
            BomId = request.BomId ?? 0,
            UpdatedBy = UserId,
            LoginEmail = LoginEmail,
            Subject = $"{_messageLocalizer["HUBRFQReleased"]} ( {request.BOMName} )",
            BOMCode = request.BOMCode,
            BOMName = request.BOMName,
            BomCompanyName = request.BomCompanyName,
            BomCompanyNo = request.BomCompanyNo ?? 0,
            RequestedBy = request.SalesManNo ?? 0,
            ReqSalesPerson = reqSalePerson,
            SupportTeamMemberNo = supportTeamMemberNo,
            ClientId = ClientId,
            LoginId = UserId,
            SenderName = string.Format(_sessionManager.LoginFullName),
            HUBRFQStatus = _messageLocalizer["HUBRFQReleased"],
            ClientName = _sessionManager.ClientName!,
            IsPoHub = _sessionManager.IsPOHub,
            ClientCurrencyID = _sessionManager.GetInt32(SessionKey.ClientCurrencyID) ?? 0,
            ClientCurrencyCode = _sessionManager.GetString(SessionKey.ClientCurrencyCode) ?? string.Empty,
            CultureInfo = _sessionManager.GetCurrentCulture(),
            HUBRFQHyperLink = $"{urlBase}{urlPath}"
        }, cancellationToken));
    }


    [HttpPost("list")]
    public async Task<IActionResult> GetBOMLines(GetBOMLineRequest request)
    {
        var isRequirementSearch = Enum.TryParse<BOMQueryMode>(request.HeaderOrDetail, true, out var mode)
                         && mode == BOMQueryMode.Detail;
        var isPOHub = _sessionManager.IsPOHub;

        var queryResult = await _mediator.Send(new GetBOMLinesQuery()
        {
            Status = request.Status ?? 0,// BomStatus
            AssignedUser = request.AssignedUser, //PoHubBuyer
            Manufacturer = request.Manufacturer?.Trim(), //Manufacturer
            Part = request.Part != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.Part) : null,
            IsSearchFromRequirements = isRequirementSearch,
            StartDate = request.StartDate,
            EndDate = request.EndDate,
            Division = request.Division, //Division
            IsPoHub = isPOHub,
            SalesPersonId = request.SalesPersonId, // SalesPerson
            CompanyTypeId = request.CompanyTypeId, // CompanyType
            AS6081Required = request.AS6081Required ?? 0,
            SelectedClientId = request.SelectedClientId,
            RequiredEndDate = request.RequiredEndDate,
            RequiredStartDate = request.RequiredStartDate,
            Name = request.Name?.Trim(),
            Code = request.Code?.Trim(),
            LoginId = _sessionManager.LoginID!.Value,
            DivisionId = _sessionManager.LoginDivisionID,
            ClientCurrencyCode = _sessionManager.ClientCurrencyCode!,
            TeamId = _sessionManager.LoginTeamID,
            ClientId = ClientId,
            ViewLevelList = request.ViewLevelList,
            OrderBy = request.OrderBy ?? 0,
            SortDir = request.SortDir ?? 1,
            Index = request.Index,
            Size = request.Size,
            IsGSA = _sessionManager.IsGSA,
            IsGlobalUser = _sessionManager.IsGlobalUser
        });

        var resultView = _mapper.Map<BaseResponse<IEnumerable<BOMLineViewModel>>>(queryResult);
        var totalItems = queryResult.Data!.FirstOrDefault()?.RowCnt ?? 0;

        var blnMakeYellow = (_sessionManager.IsGSA && !_sessionManager.IsGlobalUser && request.SelectedClientId.HasValue);
        foreach (var bomLine in resultView.Data!.ToList())
        {
            bomLine.BlnMakeYellow = blnMakeYellow;
            bomLine.IsPOHub = isPOHub;
        }

        var response = new DatatableResponse<IEnumerable<BOMLineViewModel>>()
        {
            Success = resultView.Success,
            Data = resultView.Data,
            RecordsTotal = totalItems,
            RecordsFiltered = totalItems,
            Draw = request.Draw
        };

        return Ok(response);
    }

    [HttpPost("bom-bid-requirement")]
    public async Task<IActionResult> CreateBomBidRequirement(BOMNoBidRequirementRequest request)
    {
        if (!_sessionManager.IsPOHub)
        {
            return Forbid("Only POHub users are authorized to no-bid all BOM items.");
        }
        var urlPath = Navigations.BOMDetailWithId(request.BomId).CtaUri;
        var urlBase = RequestHelper.GetApplicationUrl(HttpContext.Request);
        return Ok(await _mediator.Send(new CreateBOMNoBidRequirementFlowCommand
        {
            BomId = request.BomId,
            Notes = request.BidNotes,
            UpdatedBy = UserId,
            LoginEmail = LoginEmail,
            Subject = $"{_messageLocalizer["HUBRFQNoBid"]} ( {request.BomName} )",
            BOMCode = request.BomCode,
            BomCompanyName = request.BomCompanyName,
            BomCompanyNo = request.BomCompanyNo,
            BOMName = request.BomName,
            ClientId = ClientId,
            LoginId = UserId,
            SalesmanNo = request.SalesmanNo,
            SenderName = string.Format(_sessionManager.LoginFullName),
            HUBRFQStatus = _messageLocalizer["HUBRFQNoBid"],
            ClientName = _sessionManager.ClientName!,
            ClientCurrencyCode = _sessionManager.ClientCurrencyCode ?? "",
            IsPoHub = _sessionManager.IsPOHub,
            HUBRFQHyperLink = $"{urlBase}{urlPath}"
        }));
    }

    [HttpPost("add-new")]
    public async Task<IActionResult> AddNew(AddNewBomRequest request)
    {
        return Ok(await _mediator.Send(new CreateNewBomCommand
        {
            AS9120 = request.AS9120,
            AssignUserNo = request.AssignUserNo ?? 0,
            ClientID = ClientId,
            Company = request.Company,
            CompanyName = request.CompanyName?.Trim(),
            Contact = request.Contact,
            Contact2 = request.Contact2,
            CurrencyNo = request.CurrencyNo,
            CurrentSupplier = request.CurrentSupplier?.Trim(),
            GeneratedBomID = request.GeneratedBomID?.Trim(),
            GeneratedFilename = request.GeneratedFilename?.Trim(),
            Inactive = request.Inactive,
            LoginEmail = LoginEmail.Trim(),
            LoginID = UserId,
            Name = request.Name?.Trim(),
            Notes = request.Notes?.Trim(),
            OriginalFilename = request.OriginalFilename?.Trim(),
            QuoteRequired = request.QuoteRequired,
            RecipientLoginIDs = request.RecipientLoginIDs,
            Subject = "Subject"
        }));
    }

    [HttpGet("part-detail/{customerRequirementId}/sourcing-results")]
    public async Task<IActionResult> GetSourcingResults(int customerRequirementId)
    {
        var query = new GetListForBOMCustomerRequirementQuery
        {
            ClientId = ClientId,
            CustomerRequirementId = customerRequirementId,
            IsPOHub = _sessionManager.IsPOHub,
            ClientCurrencyID = _sessionManager.GetInt32(SessionKey.ClientCurrencyID) ?? 0,
            ClientCurrencyCode = _sessionManager.GetString(SessionKey.ClientCurrencyCode) ?? string.Empty,
            CultureInfo = _sessionManager.GetCurrentCulture(),
            LoginId = UserId
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    [HttpGet("{bomId}/pvv-items")]
    //[ApiAuthorize(false, SecurityFunction.Orders_CustomerRequirement_View)]
    public async Task<IActionResult> GetPvvBomItems(int bomId, CancellationToken cancellationToken)
    {
        var query = new GetAllPvvBomQuery { BomId = bomId };
        return Ok(await _mediator.Send(query, cancellationToken));
    }

    [HttpGet("{bomId}/sourcing-for-release")]
    public async Task<IActionResult> GetSourcingForRelease(int bomId, CancellationToken cancellationToken)
    {
        var query = new GetBOMSourcingResultForReleaseQuery { BomId = bomId, IsPoHub = _sessionManager.IsPOHub };
        return Ok(await _mediator.Send(query, cancellationToken));
    }

    [HttpGet("{bomId}/pvv-questions")]
    //[ApiAuthorize(false, SecurityFunction.Orders_CustomerRequirement_View)]
    public async Task<IActionResult> GetPvvBomQuestionItems(int bomId, CancellationToken cancellationToken)
    {
        var query = new GetAllPvvBomQuestionQuery
        {
            BomId = bomId,
            IsPoHub = _sessionManager.IsPOHub
        };
        return Ok(await _mediator.Send(query, cancellationToken));
    }

    [HttpGet("get-temp-pvv-questions")]
    //[ApiAuthorize(false, SecurityFunction.Orders_CustomerRequirement_View)] 
    public async Task<IActionResult> GetTempPvvBomItems([FromQuery] string? bomIdGenerated, CancellationToken cancellationToken)
    {
        var query = new GetAllTempPvvBomQuestionQuery
        {
            BomIdGenerated = bomIdGenerated,
            IsPoHub = _sessionManager.IsPOHub
        };
        return Ok(await _mediator.Send(query, cancellationToken));
    }

    [HttpDelete("delete-pvv-questions/{id}")]
    [ApiAuthorize(false, SecurityFunction.Setup_GlobalSettings_PPVBOMQualification)]
    public async Task<IActionResult> DeletePvvBomItems(int id, CancellationToken cancellationToken)
    {
        var query = new DeletePvvBomCommand
        {
            PVVBOMId = id,
        };
        return Ok(await _mediator.Send(query, cancellationToken));
    }

    [HttpPost("{bomId}/pvv-answers")]
    [ApiAuthorize(false, SecurityFunction.Setup_GlobalSettings_PPVBOMQualification)]
    public async Task<IActionResult> UpdatePvvAnswers(int bomId, [FromBody] SaveEditTempPvvAnswersRequest request, CancellationToken cancellationToken)
    {
        var command = new UpdatePvvBomCommand
        {
            BomNo = bomId,
            ClientId = _sessionManager.ClientID ?? 0,
            UpdatedBy = _sessionManager.LoginID,
            Notes = request.PVVAnswers
        };

        return Ok(await _mediator.Send(command, cancellationToken));
    }

    [HttpGet("{bomId}/get-check-data")]
    //[ApiAuthorize(false, SecurityFunction.Orders_CustomerRequirement_View)]
    public async Task<IActionResult> GetPvvBomQuestionCheckData(int bomId, CancellationToken cancellationToken)
    {
        var query = new GetAllCheckPvvBomQuery
        {
            BomId = bomId,
            IsPoHub = _sessionManager.IsPOHub
        };
        return Ok(await _mediator.Send(query, cancellationToken));
    }

    [HttpPost("temp-pvv-answers")]
    [ApiAuthorize(false, SecurityFunction.Setup_GlobalSettings_PPVBOMQualification)]
    public async Task<IActionResult> SaveEditTTempPvvAnswers([FromBody] SaveEditTempPvvAnswersRequest request, CancellationToken cancellationToken)
    {
        var command = new UpdateTempPvvAnswerCommand
        {
            BomGeneratedId = request.BomIdGenerated,
            ClientNo = _sessionManager.ClientID ?? 0,
            UpdatedBy = _sessionManager.LoginID,
            Notes = request.PVVAnswers
        };

        var result = await _mediator.Send(command, cancellationToken);

        return Ok(result);
    }

    [HttpPost("send-mail-message-supplier")]
    public async Task<IActionResult> SendMailMessageSupplier(SendMailMessageSupplierRequest request)
    {
        var resources = new List<(string key, string value)>();

        var reportTitles = _reportLocalizer.GetAllStrings();
        foreach (var reportTitle in reportTitles)
        {
            resources.Add(($"ReportTitles{reportTitle.Name}", reportTitle.Value));
        }

        var reportLocalizerLocal = _reportLocalizer.GetAllStrings();
        foreach (var report in reportLocalizerLocal)
        {
            resources.Add(($"Reports{report.Name}", report.Value));
        }

        var miscLocalizerLocal = _miscLocalizer.GetAllStrings();
        foreach (var misc in miscLocalizerLocal)
        {
            resources.Add(($"Misc{misc.Name}", misc.Value));
        }
        return Ok(await _mediator.Send(new CreateNotifyMessageSupplierCommand
        {
            CurrencyCode = request.CurrencyCode,
            Id = request.Id,
            LoginFullName = _sessionManager.LoginFullName,
            LoginID = UserId,
            Message = request.Message.Trim(),
            ReportNo = request.ReportNo,
            Subject = request.Subject.Trim(),
            ToCompanyIdsArray = request.ToCompanyIdsArray,
            ToLoginsArray = request.ToLoginsArray,
            LoginEmail = _sessionManager.GetString(SessionKey.LoginEmail) ?? string.Empty,
            ClientId = ClientId,
            Resources = resources,
            ReplyTos = _configuration.GetValue<string>(AppSettingKeys.SendToSupplier)
        }));
    }

    [HttpGet("{BOMNo}/items")]
    public async Task<IActionResult> GetBOMItem(int BOMNo)
    {
        return Ok(await _mediator.Send(new GetBomItemsQuery
        {
            IsPoHub = _sessionManager.GetBool(SessionKey.IsPOHub),
            BOMNo = BOMNo,
            ClientId = _sessionManager.GetInt32(SessionKey.ClientID),
            LoginId = _sessionManager.GetInt32(SessionKey.LoginID),
            ClientCurrencyID = _sessionManager.GetInt32(SessionKey.ClientCurrencyID),
            ClientCurrencyCode = _sessionManager.GetString(SessionKey.ClientCurrencyCode) ?? string.Empty
        }));
    }
    [HttpPut("update-bom-status-to-closed")]
    public async Task<IActionResult> UpdateBOMStatusToClosed(UpdateBomStatusToClosedRequest request)
    {
        return Ok(await _mediator.Send(new UpdateBomStatusToClosedCommand
        {
            BomId = request.Id,
            BomStatus = (int)BOMStatus.Closed,
            UpdateBy = UserId
        }));
    }


    [HttpDelete("delete-temp-pvv-questions/{idGenerated}")]
    [ApiAuthorize(false, SecurityFunction.Setup_GlobalSettings_PPVBOMQualification)]
    public async Task<IActionResult> DeleteTempPvvBomItems(string idGenerated, CancellationToken cancellationToken)
    {
        var query = new DeletePvvAnswerTempCommand
        {
            IdGenerated = idGenerated,
        };
        return Ok(await _mediator.Send(query, cancellationToken));
    }
    [HttpPost("export-to-csv")]
    public async Task<IActionResult> ExportToCSV(ExportToCsvRequest request)
    {
        var resources = new List<(string key, string value)>();
        var reportTitles = _reportLocalizer.GetAllStrings();
        foreach (var reportTitle in reportTitles)
        {
            resources.Add(($"ReportTitles{reportTitle.Name}", reportTitle.Value));
        }

        var reportLocalizerLocal = _reportLocalizer.GetAllStrings();
        foreach (var report in reportLocalizerLocal)
        {
            resources.Add(($"Reports{report.Name}", report.Value));
        }

        var miscLocalizerLocal = _miscLocalizer.GetAllStrings();
        foreach (var misc in miscLocalizerLocal)
        {
            resources.Add(($"Misc{misc.Name}", misc.Value));
        }

        var notFoundLocalizerLocal = _notFoundLocalizer.GetAllStrings();
        foreach (var notFound in notFoundLocalizerLocal)
        {
            resources.Add(($"NotFound{notFound.Name}", notFound.Value));
        }

        var data = await _mediator.Send(new CreateExportToCsvCommand
        {
            Id = request.Id,
            ClientID = ClientId,
            CurrencyCode = request.CurrencyCode,
            Export = "E",
            Report = Core.Enums.Report.RequirementWithBOM,
            Resources = resources,
            LoginFullName = _sessionManager.LoginFullName
        });
        Response.Headers.TryAdd("Content-Disposition", $"attachment; filename=\"{data.Data!.FileName}\"");
        return File(data.Data!.File, "text/csv");
    }

    [HttpDelete("delete-bom-item")]
    public async Task<IActionResult> DeleteBomItemAsync(DeleteBomItemRequest request)
    {
        return Ok(await _mediator.Send(new DeleteBomItemCommand
        {
            BomId = request.BomId,
            RequirementId = request.RequirementId,
            LoginId = UserId,
            ClientId = ClientId
        }));
    }

    [HttpPost("apply-partwatch")]
    [ApiAuthorize(isDataOtherClient: false, SecurityFunction.Orders_CustomerRequirement_MainInformation_Edit)]
    public async Task<IActionResult> ApplyPartWatchAsync([FromBody] ApplyPartWatchRequest request)
    {
        var command = new ApplyPartWatchCommand
        {
            BOMId = request.BOMId,
            ReqIds = request.ReqIds,
            LoginId = UserId,
            ClientId = ClientId,
            UpdateByName = $"{HttpContext.Session.GetString(SessionKey.LoginFirstName)} {HttpContext.Session.GetString(SessionKey.LoginLastName)}",
            SenderEmail = _sessionManager.GetString(SessionKey.LoginEmail),
            Subject = _messageLocalizer["PartWatchMatchSubject"]
        };

        var result = await _mediator.Send(command);

        if (result.Success)
        {
            return Ok(result);
        }

        return BadRequest(result);
    }

    [HttpPost("expedite-notes")]
    public async Task<IActionResult> CreateHUBRFQExpediteNote([FromBody] CreateHubRfqExpediteNoteRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var command = new CreateExpediteNoteCommand()
        {
            HUBRFQId = request.HUBRFQId,
            EmailSendTo = request.EmailSendTo,
            ExpediteNotes = request.ExpediteNotes,
            UpdatedBy = UserId
        };

        var result = await _mediator.Send(command);

        if (result.Success)
        {
            return Ok(result);
        }
        return BadRequest(result);
    }

    [HttpPost("export-to-excel")]
    [ApiAuthorize(isDataOtherClient: false, SecurityFunction.Orders_BOMDetail_Import_Export_SourcingResult)]
    public async Task<IActionResult> ExportToExcelAsync([FromBody] ExportToExcelRequest request, CancellationToken cancellationToken)
    {
        var query = new ExportToExcelQuery
        {
            Id = request.Id,
            ClientId = ClientId
        };

        var result = await _mediator.Send(query, cancellationToken);

        var fileBytes = _exportService.ExportToExcelHUBRFQ(result.Data ?? []);

        Response.Headers.TryAdd("Content-Disposition", $"attachment; filename=\"HUBRFQ_SourcingResultTemplate.xlsx\"");

        return File(fileBytes, "text/csv");
    }

    [HttpPost("unrelease-bom-item")]
    public async Task<IActionResult> UnReleaseBomItemAsync(UnReleaseBomItemRequest request)
    {
        if (!_sessionManager.IsPOHub)
        {
            return Forbid("Only POHub users are authorized to unrelease BOM items.");
        }

        var command = new UnReleaseBomItemCommand
        {
            BomId = request.BomId,
            RequirementId = request.RequirementId
        };
        var result = await _mediator.Send(command);
        return Ok(result);
    }

    [HttpGet("item-details/{id}")]
    public async Task<IActionResult> GetBomItemDetails(int id)
    {
        var query = new GetBOMItemDetailFlowQuery(
            id,
            _sessionManager.ClientID ?? 0,
            _sessionManager.IsPOHub,
            _sessionManager.ClientCurrencyID ?? 0,
            _sessionManager.ClientCurrencyCode ?? "USD",
            CultureInfo.CurrentCulture
        );

        var response = await _mediator.Send(query);
        return Ok(response);
    }

    [HttpGet("lytica-manufacturer")]
    public async Task<IActionResult> GetLyticaManufacturer([FromQuery] string rsManufacturerName, [FromQuery] int customerRequirementId)
    {
        var query = new GetLyticaManufacturerQuery(customerRequirementId, rsManufacturerName);
        var response = await _mediator.Send(query);
        return Ok(response);
    }

    [HttpPost("delete-part-watch")]
    public async Task<IActionResult> DeletePartWatch(DeletePartWatchHUBRFQCommand request)
    {
        request.ClientID = ClientId;
        request.LoginID = UserId;
        var response = await _mediator.Send(request);
        return Ok(response);
    }

    [HttpGet("{bomId}/assignment-history")]
    public async Task<IActionResult> GetAssignmentHistory(int bomId)
    {
        var result = await _mediator.Send(new GetBomAssignmentHistoryQuery
        {
            BomId = bomId
        });
        return Ok(result);
    }

    [HttpPut("no-bid")]
    public async Task<IActionResult> UpdateCustomerRequirementNoBid([FromBody] UpdateCustomerRequirementNoBidRequest request)
    {
        if (!_sessionManager.IsPOHub)
        {
            return Forbid("Only POHub users are authorized to no-bid BOM items.");
        }

        var urlPath = Navigations.BOMDetailWithId(request.BomId).CtaUri;
        var urlBase = RequestHelper.GetApplicationUrl(HttpContext.Request);
        var command = new UpdateCustomerRequirementNoBidFlowCommand
        {
            CustomerRequirementId = request.CustomerRequirementId,
            UpdatedBy = UserId,
            BomId = request.BomId,
            Notes = request.NoBidNotes,
            LoginEmail = LoginEmail,
            Sender = _sessionManager.LoginFullName,
            LoginId = UserId,
            BomCode = request.BomCode,
            BomName = request.BomName,
            BomCompanyName = request.BomCompanyName,
            BomCompanyNo = request.BomCompanyNo,
            SalesManName = request.SalesManName,
            SalesManNo = request.SalesManNo,
            Subject = $"{_messageLocalizer["HUBRFQNoBid"]} ( {request.BomName} )",
            StatusMessage = _messageLocalizer["HUBRFQNoBid"],
            ClientId = ClientId,
            ClientName = _sessionManager.GetString(SessionKey.ClientName) ?? string.Empty,
            ClientCurrencyCode = _sessionManager.ClientCurrencyCode ?? "",
            IsPoHub = _sessionManager.IsPOHub,
            HUBRFQHyperLink = $"{urlBase}{urlPath}"
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    [HttpPut("recall-no-bid")]
    public async Task<IActionResult> RecallCustomerRequirementNoBid([FromBody] RecallCustomerRequirementNoBidRequest request)
    {
        var command = new RecallCustomerRequirementNoBidCommand
        {
            CustomerRequirementId = request.CustomerRequirementId,
            UpdatedBy = UserId
        };

        var result = await _mediator.Send(command);
        return Ok(result);
    }

    [HttpPut("update-bom")]
    public async Task<IActionResult> UpdateBom([FromBody] UpdateBomRequest request, CancellationToken cancellationToken)
    {
        var bomResponse = await _mediator.Send(new BOMDetailsQuery()
        {
            BOMId = request.BOMId,
        }, cancellationToken);

        var bom = bomResponse.Data!.FirstOrDefault();

        if (bom == null)
        {
            var errorResponse = new BaseResponse<int>
            {
                Success = false,
                Message = "BOM not found."
            };
            return BadRequest(errorResponse);
        }

        var command = new UpdateBomCommand
        {
            BOMId = request.BOMId,
            BOMName = $"{request.Name}-{_sessionManager.ClientID}",
            Notes = request.Notes,
            Inactive = request.Inactive,
            UpdatedBy = _sessionManager.LoginID,
            ContactNo = request.Contact,
            CurrencyNo = request.CurrencyNo,
            CurrentSupplier = request.CurrentSupplier,
            QuoteRequired = request.QuoteRequired,
            AS9120 = request.AS9120,
            Contact2Id = request.Contact2,
            ClientNo = _sessionManager.ClientID ?? 0,
            BOMCode = bom.BOMCode,
            CompanyId = bom.CompanyId
        };

        return Ok(await _mediator.Send(command, cancellationToken));
    }

    [HttpPut("release-sourcing-result")]
    public async Task<IActionResult> ReleaseSourcingResult(ReleaseSourcingResultCommand request, CancellationToken cancellationToken)
    {
        if (!IsPOHub)
        {
            return StatusCode(StatusCodes.Status403Forbidden);
        }

        var reqSalePerson = 0;
        var supportTeamMemberNo = 0;

        if (_sessionManager.IsPOHub)
        {
            int.TryParse(request.ReqSalesPerson?.TrimEnd('|'), out reqSalePerson);
            int.TryParse(request.SupportTeamMemberNo?.TrimEnd('|'), out supportTeamMemberNo);
        }

        request.UpdatedBy = UserId;
        var response = await _mediator.Send(request);

        var urlPath = Navigations.BOMDetailWithId(request.BomId).CtaUri;
        var urlBase = RequestHelper.GetApplicationUrl(HttpContext.Request);

        var notifyCommand = new NotifyReleaseBomCommand
        {
            BOMId = request.BomId,
            ToLogins = [request.SalesManNo, reqSalePerson, supportTeamMemberNo],
            BOMName = request.BOMName ?? string.Empty,
            SenderLoginNo = UserId,
            SenderName = string.Format(_sessionManager.LoginFullName),
            SenderEmail = LoginEmail,
            Subject = $"{_hubrfqReleasedEmailHeader} ( {request.BOMName} )",
            IsReleasedAll = false,
            HUBRFQStatus = _hubrfqReleasedEmailHeader,
            Code = $"{request.BOMCode}( {_sessionManager.ClientName!} )",
            ClientId = ClientId,
            ClientCurrencyCode = _sessionManager.GetString(SessionKey.ClientCurrencyCode) ?? string.Empty,
            CultureInfo = _sessionManager.GetCurrentCulture(),
            ClientCurrencyID = _sessionManager.GetInt32(SessionKey.ClientCurrencyID) ?? 0,
            IsPoHub = _sessionManager.IsPOHub,
            LoginId = UserId,
            HUBRFQHyperlink = $"{urlBase}{urlPath}",
            BomCompanyNo = request.BomCompanyNo,
            RequirementID = request.RequirementId,
        };

        await _mediator.Send(notifyCommand, cancellationToken);
        return Ok(response);
    }

    [HttpPut("bom-customer-requirement")]
    public async Task<IActionResult> UpdateCustomerRequirementByBomId([FromBody] UpdateCustRequirementByBomIDRequest request)
    {
        var command = new UpdateCustRequirementByBomIDCommand
        {
            BomId = request.BomId,
            UpdatedBy = UserId,
            ClientNo = ClientId,
            ReqIds = request.ReqIds,
            BOMStatus = (int)BOMStatus.Open
        };

        var response = await _mediator.Send(command);
        return Ok(response);
    }

    [HttpPost("communication-note")]
    public async Task<IActionResult> CreateCommunicationNote([FromBody] CreateExpediteNoteFlowCommandRequest request)
    {
        var response = await _mediator.Send(new CreateExpediteNoteFlowCommand()
        {
            AddNotes = request.AddNotes,
            HubRfqId = request.HubRfqId,
            UpdatedBy = UserId,
            Subject = _printingLocalizer["CommunicationNoteSubject"],
            CompanyName = request.CompanyName,
            CompanyNo = request.CompanyNo,
            Contact2No = request.Contact2No,
            ContactName = request.ContactName,
            HubRfqCode = request.HubRfqCode,
            HUBRFQName = request.HubRfqName,
            IsPOHub = _sessionManager.IsPOHub,
            LoginFullName = _sessionManager.LoginFullName,
            ReqSalesPerson = request.ReqSalesPerson,
            RequestedBy = request.RequestedBy,
            UpdateByPH = request.UpdateByPH,
            LoginEmail = _sessionManager.GetString(SessionKey.LoginEmail),
            UrlBase = $"{RequestHelper.GetApplicationUrl(HttpContext.Request)}{Navigations.BOMDetail(request.HubRfqName).CtaUri}?BOM={request.HubRfqId}"
        });
        return Ok(response);
    }

    [HttpPost("customer-requirement-without-bom")]
    public async Task<IActionResult> GetCustomerRequirementsWithoutBom([FromBody] CustomerRequirementsWithoutBomRequest request, CancellationToken cancellation)
    {
        var query = new GetCustomerRequirementsWithoutBomQuery()
        {
            ClientId = ClientId,
            OrderBy = request.OrderBy ?? 0,
            SortDir = request.SortDir ?? (int)GlobalTrader2.Dto.DataListNugget.SortColumnDirection.ASC,
            PageIndex = request.Index,
            PageSize = request.Size,
            PartSearch = request.PartSearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.PartSearch) : null,
            CompanySearch = request.CompanySearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.CompanySearch) : null,
            CustomerRequirementNoHi = request.CustomerRequirementNoHi,
            CustomerRequirementNoLo = request.CustomerRequirementNoLo,
            IncludeClosed = false,
            ReceivedDateFrom = request.ReceivedDateFrom,
            ReceivedDateTo = request.ReceivedDateTo,
            BomName = request.BomName != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.BomName) : null,
            BomId = request.BomId
        };

        var result = await _mediator.Send(query, cancellation);

        var totalItems = result.Data?.FirstOrDefault()?.RowCnt ?? 0;

        foreach (var item in result.Data ?? Enumerable.Empty<CustomerRequirementWithoutBomDto>())
        {
            item.FormatedPrice = Core.Helpers.Functions.FormatCurrency(item.Price ?? 0, CultureInfo.CurrentCulture, item.CurrencyCode ?? string.Empty, 5, false);
        }

        var response = new DatatableResponse<IEnumerable<CustomerRequirementWithoutBomDto>>()
        {
            Success = result.Success,
            Data = result.Data,
            RecordsTotal = totalItems,
            RecordsFiltered = totalItems,
            Draw = request.Draw
        };

        return Ok(response);
    }

    [HttpPost("export-excel")]
    public async Task<IActionResult> ExportDataListAsync([FromBody] ExportDataListRequest request, CancellationToken cancellationToken)
    {
        var isSearchFromRequirements = Enum.TryParse<BOMQueryMode>(request.HeaderOrDetail, true, out var mode)
                         && mode == BOMQueryMode.Detail;

        var viewLevel = (ViewLevelList)request.ViewLevelList;
        string filename = $"report_u{_sessionManager.LoginID}r{(int)DataListNuggetExport.HUBRFQ}.xlsx";
        int? teamId = viewLevel == ViewLevelList.Team ? _sessionManager.LoginTeamID : null;
        int? divisionId = viewLevel == ViewLevelList.Division ? _sessionManager.LoginDivisionID : null;
        int? loginId = viewLevel == ViewLevelList.My ? _sessionManager.LoginID : null;
        int? sortDir = request.SortDir ?? (int)GlobalTrader2.Dto.DataListNugget.SortColumnDirection.ASC;
        int? selectedClientNo = _sessionManager.IsPOHub ? request.SelectedClientId : null;
        if (!isSearchFromRequirements)
        {
            var bomExportQuery = new GetBomDataListExportQuery
            {
                ClientId = _sessionManager.ClientID,
                TeamId = teamId,
                DivisionId = divisionId,
                LoginId = loginId,
                OrderBy = request.OrderBy,
                SortDir = sortDir,
                BomCode = request.Code,
                BomName = request.Name,
                IsPOHub = _sessionManager.IsPOHub,
                SelectedClientNo = selectedClientNo,
                BomStatus = request.Status,
                IsAssignToMe = 0,
                AssignedUser = request.AssignedUser,
                DivisionNo = request.Division,
                SalesPerson = request.SalesPersonId,
                StartDate = request.StartDate,
                EndDate = request.EndDate,
                CompanyTypeId = request.CompanyTypeId,
                AS6081Required = request.AS6081Required,
                SelectedLoginId = _sessionManager.LoginID,
                Manufacturer = request.Manufacturer,
                Part = request.Part,
            };

            var bomResult = await _mediator.Send(bomExportQuery, cancellationToken);
            var fileBytes = _exportService.ExportExcel(bomResult.Data);

            Response.Headers.TryAdd(HeaderNames.ContentDisposition, $"attachment; filename=\"{filename}\"");
            return File(fileBytes, MediaTypeNames.Text.Csv);
        }
        else
        {
            var customerReqExportQuery = new GetCustomerRequirementHubrfqExportQuery
            {
                ClientId = _sessionManager.IsPOHub ? null : _sessionManager.ClientID,
                TeamId = teamId,
                DivisionId = divisionId,
                LoginId = loginId,
                OrderBy = request.OrderBy,
                SortDir = sortDir,
                BomCode = request.Code,
                BomName = request.Name,
                IsPOHub = _sessionManager.IsPOHub,
                SelectedClientNo = selectedClientNo,
                BomStatus = request.Status ?? 0,
                IsAssignToMe = null,
                AssignedUser = request.AssignedUser,
                Manufacturer = request.Manufacturer,
                Part = request.Part,
                IntDivision = request.Division,
                StartDate = request.StartDate,
                EndDate = request.EndDate,
                SalesPerson = request.SalesPersonId
            };

            var customerReqResult = await _mediator.Send(customerReqExportQuery, cancellationToken);
            var fileBytes = _exportService.ExportExcel(customerReqResult.Data);

            Response.Headers.TryAdd(HeaderNames.ContentDisposition, $"attachment; filename=\"{filename}\"");
            return File(fileBytes, MediaTypeNames.Text.Csv);
        }
    }
    [HttpPost("assign-list")]
    public async Task<IActionResult> GetAssignHUBRFQ(GetBOMAssignRequest request)
    {
        var isRequirementSearch = Enum.TryParse<BOMQueryMode>(request.HeaderOrDetail, true, out var mode)
                         && mode == BOMQueryMode.Detail;
        var isPOHub = _sessionManager.IsPOHub;

        var queryResult = await _mediator.Send(new BOMDLNDetailQuery()
        {
            BOMStatus = request.Status ?? 0,// BomStatus
            AssignedUser = request.AssignedUser, //PoHubBuyer
            Manufacturer = null, //Manufacturer
            IsSearchFromRequirements = isRequirementSearch,
            StartDate = null,
            EndDate = null,
            IntDivision = request.Division, //Division
            IsPOHub = isPOHub,
            SalesPerson = request.SalesPersonId, // SalesPerson
            CompanyTypeId = request.CompanyTypeId, // CompanyType
            AS6081Required = null,
            SelectedClientNo = request.SelectedClientId,
            RequiredEndDate = null,
            RequiredStartDate = null,
            BOMName = request.Name?.Trim(),
            BOMCode = request.Code?.Trim(),
            LoginId = (request.ViewLevelList == (int)ViewLevelList.My) ? _sessionManager.LoginID : null,
            DivisionId = (request.ViewLevelList == (int)ViewLevelList.Division) ? _sessionManager.LoginDivisionID : null,
            TeamId = (request.ViewLevelList == (int)ViewLevelList.Team) ? _sessionManager.LoginTeamID : null,
            ClientId = ClientId,
            OrderBy = request.OrderBy ?? 0,
            SortDir = request.SortDir ?? 1,
            PageIndex = request.Index,
            PageSize = request.Size,
            IsAS6081Tab = request.IsAS6081Tab ?? false,
            IsAssignToMe = 1,
            MyPageSize = request.Size
        });

        var resultView = _mapper.Map<BaseResponse<IEnumerable<BOMAssignViewModel>>>(queryResult);
        var totalItems = queryResult.Data!.FirstOrDefault()?.RowCnt ?? 0;

        var response = new DatatableResponse<IEnumerable<BOMAssignViewModel>>()
        {
            Success = resultView.Success,
            Data = resultView.Data,
            RecordsTotal = totalItems,
            RecordsFiltered = totalItems,
            Draw = request.Draw
        };

        return Ok(response);
    }

    [HttpGet("ihs-manufacturer")]
    public async Task<IActionResult> GetIHSManufacturer([FromQuery] string manufacturerName, [FromQuery] int customerRequirementId)
    {
        var query = new GetIhsManufacturerQuery
        {
            ManufacturerName = manufacturerName,
            CustomerRequirementId = customerRequirementId
        };
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    [HttpPost("sourcing-results-add")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> AddSourcingResultAsync([FromBody] AddSourcingResultHUBRFQRequestDto requestDto)
    {
        var query = new AddNewSourcingResultsHUBRFQItemCommand
        {
            CustomerRequirementNo = requestDto.CustomerRequirementNo,
            Part = requestDto.Part,
            ManufacturerNo = requestDto.ManufacturerNo,
            DateCode = requestDto.DateCode,
            PackageNo = requestDto.PackageNo,
            ProductNo = requestDto.ProductNo,
            Quantity = requestDto.Quantity,
            Price = requestDto.Price,
            OfferStatusNo = requestDto.OfferStatusNo,
            ROHS = requestDto.ROHS,
            ROHSStatus = requestDto.ROHSStatus,
            SupplierNo = requestDto.SupplierNo,
            SuplierPrice = requestDto.SupPrice,
            EstimatedShippingCost = requestDto.EstimatedShippingCostValue,
            DeliveryDate = requestDto.DeliveryDate,
            SPQ = requestDto.SPQ,
            LeadTime = requestDto.LeadTime,
            FactorySealed = requestDto.FactorySealed,
            MSLLevelNo = requestDto.MslLevelNo,
            SupplierTotalQSA = requestDto.SupplierTotalQSA,
            SupplierMOQ = requestDto.SupplierMOQ,
            SupplierLTB = requestDto.SupplierLTB,
            RegionNo = requestDto.RegionNo,
            CurrencyNo = requestDto.CurrencyNo,
            SupplierWarranty = requestDto.SupplierWarranty,
            IsTestingRecommended = requestDto.IsTestingRecommended,
            IHSCountryOfOriginNo = requestDto.IHSCountryOfOriginNo,
            TypeOfSupplier = requestDto.TypeOfSupplier,
            ReasonForSupplier = requestDto.ReasonForSupplier,
            RiskOfSupplier = requestDto.RiskOfSupplier,
            CountryNo = requestDto.CountryNo,
            Notes = requestDto.Notes,
            SellPriceLessReason = requestDto.SellPriceLessReason
        };
        var result = await _mediator.Send(query);

        return Ok(result);
    }

    [HttpGet("sourcing-results/{sourcingResultId}")]
    public async Task<IActionResult> GetSourcingResultByIdAsync(int sourcingResultId)
    {
        var query = new GetSourcingResultDetailsQuery
        {
            SourcingResultId = sourcingResultId
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    [HttpPut("sourcing-results/{sourcingResultId}")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EditSourcingResultAsync(int sourcingResultId, [FromBody] UpdateSourcingResultDto requestDto)
    {
        var querySourcingResultById = new GetSourcingResultDetailsQuery
        {
            SourcingResultId = sourcingResultId
        };

        var resultSourcingResultById = await _mediator.Send(querySourcingResultById);

        var query = new UpdateSourcingResultCommand
        {
            SourcingResultId = sourcingResultId,
            SupplierNo = resultSourcingResultById.Data?.SupplierNo,
            CurrencyNo = requestDto.CurrencyNo,
            DateCode = requestDto.DateCode,
            ManufacturerNo = requestDto.ManufacturerNo,
            MslLevelNo = requestDto.MSLLevelNo,
            Notes = requestDto.Notes,
            OfferStatusNo = requestDto.OfferStatusNo,
            PackageNo = requestDto.PackageNo,
            Part = requestDto.Part,
            PartWatchMatchHUBIPO = resultSourcingResultById.Data?.PartWatchMatch,
            Price = requestDto.Price,
            ProductNo = requestDto.ProductNo,
            Quantity = requestDto.Quantity,
            ROHS = requestDto.ROHS,
            UpdatedBy = UserId,
            MSL = requestDto.MSL,
            CountryNo = requestDto.CountryNo,
            DeliveryDate = requestDto.DeliveryDate,
            EstimatedShippingCostValue = requestDto.EstimatedShippingCostValue,
            FactorySealed = requestDto.FactorySealed,
            IHSCountryOfOriginNo = requestDto.IHSCountryOfOriginNo,
            IsTestingRecommended = requestDto.IsTestingRecommended,
            LeadTime = requestDto.LeadTime,
            LinkMultiCurrencyNo = requestDto.LinkMultiCurrencyNo,
            PriorityNo = requestDto.PriorityNo,
            PUHUB = requestDto.PUHUB,
            ReasonForSupplier = requestDto.ReasonForSupplier,
            RegionNo = requestDto.RegionNo,
            RiskOfSupplier = requestDto.RiskOfSupplier,
            ROHSStatus = requestDto.ROHSStatus,
            SellPriceLessReason = requestDto.SellPriceLessReason,
            SPQ = requestDto.SPQ,
            SuplierPrice = requestDto.SupPrice,
            SupplierLTB = requestDto.SupplierLTB,
            SupplierMOQ = requestDto.SupplierMOQ,
            SupplierTotalQSA = requestDto.SupplierTotalQSA,
            SupplierWarranty = requestDto.SupplierWarranty,
            TypeOfSupplier = requestDto.TypeOfSupplier
        };

        var result = await _mediator.Send(query);
        return Ok(result);
    }

    [HttpGet("{id}/status")]
    public async Task<IActionResult> GetBomStatus(int id)
    {
        var result = await _mediator.Send(new GetBomStatusQuery(id));
        return Ok(result);
    }
}
