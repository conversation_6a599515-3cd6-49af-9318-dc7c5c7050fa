﻿using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrderQuote.Command;

namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseOrderQuote.Command;

public class UpdatePurchaseRequestLineDetailValidator : AbstractValidator<UpdatePurchaseRequestLineDetailCommand>
{
    public UpdatePurchaseRequestLineDetailValidator()
    {
        RuleFor(x => x.SPQ)
   .MaximumLength(10);

        RuleFor(x => x.LeadTime)
            .MaximumLength(50);

        RuleFor(x => x.ROHSStatus)
            .MaximumLength(50);

        RuleFor(x => x.FactorySealed)
            .MaximumLength(50);

        RuleFor(x => x.ManufacturerName)
            .MaximumLength(100);

        RuleFor(x => x.DateCode)
            .MaximumLength(100);

        RuleFor(x => x.PackageType)
            .MaximumLength(100);

        RuleFor(x => x.ProductType)
            .MaximumLength(100);

        RuleFor(x => x.MOQ)
            .MaximumLength(100);

        RuleFor(x => x.TotalQSA)
            .MaximumLength(20);

        RuleFor(x => x.LTB)
            .MaximumLength(100);

        RuleFor(x => x.Notes)
            .MaximumLength(500);

    }
}
