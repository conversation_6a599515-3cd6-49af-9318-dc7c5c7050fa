﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/* 
=========================================================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-232569]		An.TranTan			18-Mar-2025		Create			Get BOM Sourcing raw data for correction process
[US-232569]		An.TranTan			25-Mar-2025		Update			Correct validation logic for currency, shipping cost & QtyInStock
[US-232569]		An.TranTan			27-Mar-2025		Update			Change logic detect HUBRFQ item release status by using POHubReleaseBy
[US-232569]		An.TranTan			23-Apr-2025		Update			Correct logic validate 
                                                                    + Allow empty DeliveryDate
                                                                    + Cover case currency = null
[US-246212]		Trang.Pham			16-Jul-2025		Create			Create v2 to get BOM Sourcing raw data by Created By and BOMNo 
[US-265423]		Trang.Pham			31-Jul-2025		Update			Update validation rule and message
==========================================================================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_get_BOMRawData_ForCorrection_v2]
    @UserId INT = 0,
    @BOMNo INT = 0,
    @PageNumber INT = 1,       -- Page number (default is 1)  
    @PageSize INT = 10,        -- Number of rows per page (default is 10)  
    @ShowMismatchOnly BIT = 0 
AS BEGIN
    SET NOCOUNT ON;
    --Variables
    DECLARE @TotalCount INT = 0
            ,@TotalRecordsError INT = 0;
    -----------------------------------------

    IF OBJECT_ID('tempdb..#tbBomImportSourcingTemp') IS NOT NULL
        DROP TABLE #tbBomImportSourcingTemp
    SELECT *
        , CAST(NULL AS INT) AS CustomerRequirementNo
        , CAST(NULL AS NVARCHAR(3000)) AS RequirementMessage
        , CAST(NULL AS NVARCHAR(3000)) AS CustomerRefNoMessage
        , CAST(NULL AS NVARCHAR(3000)) AS SupplierMessage
        , CAST(NULL AS NVARCHAR(3000)) AS SupplierPartMessage
        , CAST(NULL AS NVARCHAR(3000)) AS SupplierCostMessage
        , CAST(NULL AS NVARCHAR(3000)) AS ROHSMessage
        , CAST(NULL AS NVARCHAR(3000)) AS MFRMessage
        , CAST(NULL AS NVARCHAR(3000)) AS DateCodeMessage
        , CAST(NULL AS NVARCHAR(3000)) AS PackageMessage
        , CAST(NULL AS NVARCHAR(3000)) AS OfferedQuantityMessage
        , CAST(NULL AS NVARCHAR(3000)) AS OfferStatusMessage
        , CAST(NULL AS NVARCHAR(3000)) AS SPQMessage
        , CAST(NULL AS NVARCHAR(3000)) AS FactorySealedMessage
        , CAST(NULL AS NVARCHAR(3000)) AS QtyInStockMessage
        , CAST(NULL AS NVARCHAR(3000)) AS MOQMessage
        , CAST(NULL AS NVARCHAR(3000)) AS LastTimeBuyMessage
        , CAST(NULL AS NVARCHAR(3000)) AS CurrencyMessage
        , CAST(NULL AS NVARCHAR(3000)) AS BuyPriceMessage
        , CAST(NULL AS NVARCHAR(3000)) AS SellPriceMessage
        , CAST(NULL AS NVARCHAR(3000)) AS ShippingCostMessage
        , CAST(NULL AS NVARCHAR(3000)) AS LeadTimeMessage
        , CAST(NULL AS NVARCHAR(3000)) AS RegionMessage
        , CAST(NULL AS NVARCHAR(3000)) AS DeliveryDateMessage
        , CAST(NULL AS NVARCHAR(3000)) AS NotesMessage
        , CAST(NULL AS NVARCHAR(3000)) AS MSLMessage
        , CAST(0 AS BIT) AS IsError
    INTO #tbBomImportSourcingTemp
    FROM BorisGlobalTraderImports.dbo.tbBomImportSourcingTemp WITH(NOLOCK) WHERE CreatedBy = @UserId AND BOMNo = @BOMNo;

    --get requirement id base on HUBRFQ ID and Requirement Number
    UPDATE temp
    SET temp.CustomerRequirementNo = cr.CustomerRequirementId
    FROM #tbBomImportSourcingTemp temp
    JOIN tbCustomerRequirement cr WITH(NOLOCK) 
        ON cr.CustomerRequirementNumber = TRY_CAST(temp.CustomerRequirementNumber AS INT) AND cr.BOMNo = temp.BOMNo;

    --Check validation message
    --Requirement
    UPDATE temp
    SET temp.RequirementMessage = CASE WHEN RTRIM(ISNULL(temp.CustomerRequirementNumber,'')) = '' THEN 'Requirement cannot be blank'
                                    WHEN ISNULL(temp.CustomerRequirementNo,0) = 0 THEN 'Requirement is invalid'
                                    WHEN ISNULL(cr.POHubReleaseBy, 0) > 0 THEN 'This requirement has been released'
                                    WHEN ISNULL(cr.Closed, 0) > 0 THEN 'This requirement has been closed'
                                    END,
        temp.IsError = 1
    FROM #tbBomImportSourcingTemp temp
    LEFT JOIN tbCustomerRequirement cr WITH(NOLOCK) on cr.CustomerRequirementId = temp.CustomerRequirementNo
    WHERE RTRIM(ISNULL(temp.CustomerRequirementNumber,'')) = ''
        OR ISNULL(temp.CustomerRequirementNo,0) = 0
        OR ISNULL(cr.POHubReleaseBy, 0) > 0
        OR ISNULL(cr.Closed, 0) > 0 ;

    --Customer Ref No.
    UPDATE temp
    SET temp.CustomerRefNoMessage = 'Customer Ref No. must be less than 200 characters'
        ,temp.IsError = 1
    FROM #tbBomImportSourcingTemp temp
    WHERE LEN(temp.CustomerRefNo) > 200

    --Supplier
    UPDATE temp
    SET temp.SupplierMessage = 'This supplier does not exist on the DMCC client'
        ,temp.IsError = 1
    FROM #tbBomImportSourcingTemp temp
    WHERE NOT EXISTS(select top 1 1 from dbo.tbCompany co with (nolock)
                                    where co.Inactive = 0 
                                    AND co.ClientNo = 114 
                                    AND co.CompanyName = temp.SupplierName) 
    
    --supplier part
    UPDATE temp
    SET temp.SupplierPartMessage = 'Supplier Part is required'
        ,temp.IsError = 1
    FROM #tbBomImportSourcingTemp temp
    WHERE LEN(ISNULL(temp.SupplierPart,'')) = 0

    --supplier cost
    UPDATE temp
    SET temp.SupplierCostMessage = 'Supplier Cost is required and accepts non-negative numeric values'
        ,temp.IsError = 1
    FROM #tbBomImportSourcingTemp temp
    WHERE ISNULL(temp.SupplierCost,'') <> dbo.stripNumeric(temp.SupplierCost)

    --manufacturer
    UPDATE temp
    SET temp.MFRMessage = 'This manufacturer does not exist or being inactive'
        ,temp.IsError = 1
    FROM #tbBomImportSourcingTemp temp
    WHERE NOT EXISTS(
                    select top 1 1 from tbManufacturer m with(nolock) 
                    where m.ManufacturerName = temp.Manufacturer
                    and m.Inactive = 0)
    --date code
    UPDATE temp
    SET temp.DateCodeMessage = 'DateCode only accepts 5 characters'
        ,temp.IsError = 1
    FROM #tbBomImportSourcingTemp temp
    WHERE LEN(temp.DateCode) > 5

    --offered quantity
    UPDATE temp
    SET temp.OfferedQuantityMessage = CASE WHEN ISNULL(temp.OfferedQuantity,'') <> dbo.stripNumeric(temp.OfferedQuantity) THEN 'Offered Quantity is required and accepts non-negative numeric values'
                                            WHEN TRY_CAST(temp.OfferedQuantity AS BIGINT) IS NULL THEN 'Offer Quantity accepts integer values only'
                                            WHEN TRY_CAST(temp.OfferedQuantity AS BIGINT) > 2000000000 THEN 'Offer Quantity must be less than or equal to 2,000,000,000'
                                            END
        ,temp.IsError = 1
    FROM #tbBomImportSourcingTemp temp
    WHERE ISNULL(temp.OfferedQuantity,'') <> dbo.stripNumeric(temp.OfferedQuantity)
        OR TRY_CAST(temp.OfferedQuantity AS BIGINT) IS NULL
        OR TRY_CAST(temp.OfferedQuantity AS BIGINT) > 2000000000

    --QtyInStock
    UPDATE temp
    SET temp.QtyInStockMessage = CASE WHEN ISNULL(temp.QtyInStock,'') <> '' AND dbo.stripNumeric(temp.QtyInStock) <> temp.QtyInStock THEN 'Qty In Stock accepts non-negative numeric values'
                                    WHEN ISNULL(temp.QtyInStock,'') <> '' AND TRY_CAST(temp.QtyInStock AS BIGINT) IS NULL THEN 'Qty In Stock accepts integer values only'
                                    WHEN TRY_CAST(temp.QtyInStock AS BIGINT) > 2000000000 THEN 'Qty In Stock must be less than or equal to 2,000,000,000'
                                    END
        ,temp.IsError = 1
    FROM #tbBomImportSourcingTemp temp
    WHERE (ISNULL(temp.QtyInStock,'') <> '' AND dbo.stripNumeric(temp.QtyInStock) <> temp.QtyInStock)
        OR (ISNULL(temp.QtyInStock,'') <> '' AND TRY_CAST(temp.QtyInStock AS BIGINT) IS NULL)
        OR TRY_CAST(temp.QtyInStock AS BIGINT) > 2000000000 

    --currency
    UPDATE temp
    SET temp.CurrencyMessage = 'Currency code accepts 3 characters'
        ,temp.IsError = 1
    FROM #tbBomImportSourcingTemp temp
    WHERE LEN(ISNULL(temp.Currency,'')) <> 3

    --buy price
    UPDATE temp
    SET temp.BuyPriceMessage = 'Buy Price is required and accepts non-negative numeric values'
        ,temp.IsError = 1
    FROM #tbBomImportSourcingTemp temp
    WHERE ISNULL(temp.BuyPrice,'') <> dbo.stripNumeric(temp.BuyPrice)

    --sell price
    UPDATE temp
    SET temp.SellPriceMessage = 'Sell Price is required and accepts non-negative numeric values'
        ,temp.IsError = 1
    FROM #tbBomImportSourcingTemp temp
    WHERE ISNULL(temp.SellPrice,'') <> dbo.stripNumeric(temp.SellPrice)

    --shipping cost
    UPDATE temp
    SET temp.ShippingCostMessage = 'Shipping Cost accepts non-negative values'
        ,temp.IsError = 1
    FROM #tbBomImportSourcingTemp temp
    WHERE ISNULL(temp.ShippingCost,'') <> '' AND dbo.stripNumeric(temp.ShippingCost) <> temp.ShippingCost

    --delivery date
    UPDATE temp
    SET temp.DeliveryDateMessage = 'Delivery Date is invalid. Accept format: dd/mm/yyyy'
        ,temp.IsError = 1
    FROM #tbBomImportSourcingTemp temp
    WHERE ISNULL(temp.DeliveryDate,'') <> '' AND TRY_PARSE(temp.DeliveryDate AS DATE USING 'en-GB') IS NULL 

    --ROHS
    --Package
    --OfferStatus
    --SPQ
    --MOQ
    --FactorySealed
    --LastTimeBuy
    --LeadTime
    --Region
    --MSL
    --Notes

    ;WITH cteRawData AS(
        SELECT 
            ROW_NUMBER() OVER (ORDER BY BomImportSourcingId) AS RowNum
            , BomImportSourcingId
            , CustomerRequirementNumber AS Requirement
            , RequirementMessage
            , CustomerRefNo
            , CustomerRefNoMessage
            , SupplierName AS Supplier
            , SupplierMessage
            , SupplierPart
            , SupplierPartMessage
            , SupplierCost
            , SupplierCostMessage
            , ROHS
            , ROHSMessage
            , Manufacturer AS MFR
            , MFRMessage
            , DateCode
            , DateCodeMessage
            , Package
            , PackageMessage
            , OfferedQuantity
            , OfferedQuantityMessage
            , OfferStatus
            , OfferStatusMessage
            , SPQ
            , SPQMessage
            , FactorySealed
            , FactorySealedMessage
            , QtyInStock
            , QtyInStockMessage
            , MOQ
            , MOQMessage
            , LastTimeBuy
            , LastTimeBuyMessage
            , Currency
            , CurrencyMessage
            , BuyPrice
            , BuyPriceMessage
            , SellPrice
            , SellPriceMessage
            , ShippingCost
            , ShippingCostMessage
            , LeadTime
            , LeadTimeMessage
            , Region
            , RegionMessage
            , DeliveryDate
            , DeliveryDateMessage
            , Notes
            , NotesMessage
            , MSL
            , MSLMessage
        FROM #tbBomImportSourcingTemp
        WHERE @ShowMismatchOnly = 0 OR IsError = 1
    ) SELECT *  
    FROM cteRawData  
    WHERE RowNum BETWEEN (@PageNumber - 1) * @PageSize + 1 AND @PageNumber * @PageSize
    ORDER BY RowNum;
    
    SELECT COUNT(*) AS TotalCount 
    FROM #tbBomImportSourcingTemp;

    SELECT COUNT(*) AS TotalRecordsError 
    FROM #tbBomImportSourcingTemp
    WHERE IsError = 1

    --get incorrect MFR and supplier
    IF OBJECT_ID('tempdb..#tbIncorrectData') IS NOT NULL
        DROP TABLE #tbIncorrectData
    CREATE TABLE #tbIncorrectData(
        [ID] INT IDENTITY(1,1) NOT NULL,
        [Value] NVARCHAR(MAX),
        [Count] INT,
        [Type] NVARCHAR(20)
    )
    ;WITH cteIncorrectMfr AS(
        SELECT temp.Manufacturer, COUNT(ISNULL(temp.Manufacturer,'')) AS MfrCount 
        FROM #tbBomImportSourcingTemp temp
        WHERE temp.MFRMessage IS NOT NULL
        GROUP BY temp.Manufacturer
    )INSERT INTO #tbIncorrectData ([Value],[Count],[Type])
    SELECT
        CASE WHEN ISNULL(cte.Manufacturer,'') = '' THEN '_Blank_' ELSE cte.Manufacturer END
        ,cte.MfrCount
        ,'MFR'
    FROM cteIncorrectMfr cte

    ;WITH cteIncorrectSupplier AS(
        SELECT temp.SupplierName, COUNT(ISNULL(temp.SupplierName,'')) AS SupplierCount
        FROM #tbBomImportSourcingTemp temp
        WHERE temp.SupplierMessage IS NOT NULL
        GROUP BY temp.SupplierName
    )INSERT INTO #tbIncorrectData ([Value],[Count],[Type])
    SELECT
        CASE WHEN ISNULL(cte.SupplierName,'') = '' THEN '_Blank_' ELSE cte.SupplierName END
        ,cte.SupplierCount
        ,'SUPPLIER'
    FROM cteIncorrectSupplier cte

    IF EXISTS (SELECT TOP 1 1 FROM #tbIncorrectData)
    BEGIN
        SELECT * FROM #tbIncorrectData
    END
    ELSE BEGIN
        SELECT
            0 AS ID
            ,NULL AS 'Value'
            ,NULL AS 'Type'
            ,0 AS 'Count'
    END

    DROP TABLE #tbBomImportSourcingTemp;
    DROP TABLE #tbIncorrectData;
END
