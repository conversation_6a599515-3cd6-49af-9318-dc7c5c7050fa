using MediatR;
using GlobalTrader2.Core.Bases;

namespace GlobalTrader2.Orders.UserCases.Orders.PrintEmailLog.Commands
{
    public record CreatePrintEmailLogCommand : IRequest<BaseResponse<int>>
    {
        public string? SectionName { get; set; }
        public string? SubSectionName { get; set; }
        public string? ActionName { get; set; }
        public int DocumentNo { get; set; }
        public string? Detail { get; set; }
        public int? UpdatedBy { get; set; }
    }
}
