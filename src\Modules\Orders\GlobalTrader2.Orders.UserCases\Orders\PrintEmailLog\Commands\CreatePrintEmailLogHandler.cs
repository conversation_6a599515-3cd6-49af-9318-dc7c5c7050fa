﻿using GlobalTrader2.Core.StoreName;

namespace GlobalTrader2.Orders.UserCases.Orders.PrintEmailLog.Commands
{
    public class CreatePrintEmailLogHandler : IRequestHandler<CreatePrintEmailLogCommand, BaseResponse<int>>
    {
        private readonly IBaseRepository<object> _repository;
        public CreatePrintEmailLogHandler(IBaseRepository<object> repository)
        {
            _repository = repository;
        }

        public async Task<BaseResponse<int>> Handle(CreatePrintEmailLogCommand request, CancellationToken cancellationToken)
        {
            var output = new SqlParameter("@PrintDocumentLogId", System.Data.SqlDbType.Int) { Direction = System.Data.ParameterDirection.Output };
            SqlParameter[] param =
            [
                new SqlParameter("@SectionName", request.SectionName ?? (object)DBNull.Value),
                new SqlParameter("@SubSectionName", request.SubSectionName ?? (object)DBNull.Value),
                new SqlParameter("@ActionName", request.ActionName ?? (object)DBNull.Value),
                new SqlParameter("@DocumentNo", request.DocumentNo),
                new SqlParameter("@Detail", request.Detail ?? (object)DBNull.Value),
                new SqlParameter("@UpdatedBy", request.UpdatedBy ?? (object)DBNull.Value),
                output
            ];

            var queryStr = $"{StoredProcedures.Insert_PrintEmailLog} @SectionName, @SubSectionName, @ActionName, @DocumentNo, @Detail, @UpdatedBy, @PrintDocumentLogId OUTPUT";
            await _repository.ExecuteSqlRawAsync(queryStr, param);

            int newId = (output.Value != DBNull.Value && output.Value != null)
                ? Convert.ToInt32(output.Value.ToString())
                : 0;

            return new BaseResponse<int>
            {
                Success = newId > 0,
                Data = newId
            };
        }
    }
}
