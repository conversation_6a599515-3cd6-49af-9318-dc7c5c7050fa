import { LiteFormDialog } from "../../../../components/base/lite-form-dialog.js?v=#{BuildVersion}#";

export class CloseHubRfqHandler {
    constructor(id) {
        this.id = id;
        this.form = null;
        this.init();
    }

    init() {
        this.form = new LiteFormDialog("#hubrfq-close-dialog", {
            width: '400px',
            closeWhenSuccess: true,
            formData: () => ({
                Id: this.id
            }),
            url: "/api/orders/bom/update-bom-status-to-closed",
            method: "PUT",
            buttons: [
                { name: "save", icon: "check", alt: "yes", display: 'Yes' },
                { name: "cancel", icon: "xmark", alt: "no", display: 'No' }
            ],
        });

        this.form.$form.on('closedWithSuccessedResponse.mf', async () => {
            $('#close-btn').prop('disabled', true);
        });
    }
}
