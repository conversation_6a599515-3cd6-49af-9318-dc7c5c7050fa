export const getPageUrl = function (enumPage) {
    switch (enumPage) {
        case ListEnum.Orders_CustomerRequirementDetail: return "Orders/CustomerRequirement/Details";
        case ListEnum.Orders_QuoteDetail: return "Orders/Quotes/Details";
        case ListEnum.Orders_SalesOrderDetail: return "Orders/SalesOrders/Details";
        case ListEnum.Orders_InvoiceDetail: return "Orders/Invoice/Details";
        case ListEnum.Orders_ClientInvoiceDetail: return "Orders/ClientInvoice/Details";
        case ListEnum.Orders_PurchaseOrderDetail: return "Orders/PurchaseOrder/Details";
        case ListEnum.Orders_PurchaseRequisitionDetail: return "Orders/PurchaseRequisition/Details";
        case ListEnum.Orders_CustomerRMADetail: return "Orders/CustomerRMA/Details";
        case ListEnum.Orders_SupplierRMADetail: return "Orders/SupplierRMA/Details";
        case ListEnum.Orders_CreditNoteDetail: return "Orders/CreditNote/Details";
        case ListEnum.Orders_DebitNoteDetail: return "Orders/DebitNote/Details";
        case ListEnum.Warehouse_GoodsInDetail: return "Warehouses/GoodsIn/Details";
        case ListEnum.Warehouse_SupplierInvoiceDetail: return "Warehouses/SupplierInvoice/Details";
        case ListEnum.Orders_InternalPurchaseOrderDetail: return "Orders/InternalPurchaseOrder/Details";
        case ListEnum.Contact_ContactDetail: return "Contact/Contacts/Details";
        case ListEnum.Contact_CompanyDetail: return "Contact/AllCompanies/Details";
        case ListEnum.Contact_ManufacturerDetail: return "Contact/Manufacturers/Details";
        case ListEnum.Orders_QuoteAdd: return `Orders/Quotes/Add`;
        case ListEnum.Orders_CustomerReqPrint: return `Orders/CusReqPrint`;
        case ListEnum.Print: return `Orders/Print`;
        case ListEnum.All_Document: return `alldocument`;
        case ListEnum.All_IHSDocument: return `IHSPDFDocument`;
    }
    return "";
}

export const ListEnum = Object.freeze({
    Orders_CustomerRequirementDetail: 2000202,
    Orders_QuoteDetail: 2000302,
    Orders_SalesOrderDetail: 2000402,
    Orders_InvoiceDetail: 2000502,
    Orders_PurchaseOrderDetail: 2000602,
    Orders_PurchaseRequisitionDetail: 2000702,
    Orders_CustomerRMADetail: 2000802,
    Orders_SupplierRMADetail: 2000902,
    Orders_CreditNoteDetail: 2001002,
    Orders_DebitNoteDetail: 2001102,
    Warehouse_GoodsInDetail: 3000802,
    Orders_ClientInvoiceDetail: 2000506,
    Warehouse_SupplierInvoiceDetail: 3000806,
    Orders_InternalPurchaseOrderDetail: 30005024,
    Contact_ContactDetail: 1000202,
    Contact_CompanyDetail: 1000105,
    Contact_ManufacturerDetail: 1000302,
    Orders_QuoteAdd: 2000303,
    Orders_CustomerReqPrint: 2001117,
    All_Document: 9000017,
    All_IHSDocument: 9000018,
    Print: 13000001,
})

export const QueryString = Object.freeze({
    CreditID: 'crd',
    CRMAID: 'crma',
    CustomerRequirementID: 'req',
    DebitID: 'deb',
    GoodsInID: 'gi',
    InvoiceID: 'inv',
    PurchaseOrderID: 'po',
    PurchaseRequisitionID: 'prq',
    QuoteID: 'qt',
    QuoteLineID: 'qtl',
    SalesOrderID: 'so',
    SalesOrderLineID: 'sol',
    SRMAID: 'srma',
    SupplierInvoiceID: 'si',
    POQuoteID: 'pqt',
    InternalPurchaseOrderID: 'ipo',
    ClientInvoiceID: 'ci',
    ContactID: "con",
    CompanyListType: "clt",
    CompanyID: "cm",
    ManufacturerId: "mfr",
    Tab: "tab",
    CompanyName: "cmn",
    LineIDs: "lns",
    DocNo: "DocNo",
    ActionType: "ActionType",
    DocId: "DocId",
    PrintObject: "pro",
    GenericID: "id",
    Section: "section",
    IHSDocument: 'ihs',
    IFrom: 'iFrom',
});

export const getUrl = function (url) {
    if (!url) return url;
    if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
    }
    if (!url.startsWith('/')) {
        return `/${url}`;
    }
    return url;
}

export const openPopup = function (strURL, windowName, width, windowFeatures) {
    let url = getUrl(strURL);
    if (!width) {
        width = 770;
    }
    if (!windowFeatures) {
        windowFeatures = `left=20,top=20,width=${width},height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes`;
    }
    const targetWin = window.open(url, windowName, windowFeatures);
    if (targetWin) {
        try {
            if (targetWin.location.href === 'about:blank') {
                targetWin.location.href = url;
            }
        } catch (e) {
            console.warn("Could not check or set popup location, possibly due to cross-origin restrictions.", e);
        }
        targetWin.focus();
    } else {
        alert("Popup window could not be opened. Please check your browser's popup blocker settings.");
    }
}
