﻿using GlobalTrader2.Dto.Base;
using GlobalTrader2.Dto.Converters.DateTime;
using GlobalTrader2.SharedUI.JsonConverters.String;
using System.Text.Json.Serialization;

namespace GlobalTrader2.Orders.UI.ViewModel.BOM
{
    public class UpdatePurchaseRequestLineDetailRequest
    {
        [JsonRequired]
        public int PurchaseRequestLineDetailId { get; set; }
        public int? PurchaseRequestLineNo { get; set; }
        [JsonRequired]
        public int CompanyNo { get; set; }
        [JsonRequired]
        public double Price { get; set; }
        [JsonConverter(typeof(TrimStringJsonConverter))]
        public string? SPQ { get; set; }
        [JsonConverter(typeof(TrimStringJsonConverter))]
        public string? LeadTime { get; set; }
        [JsonConverter(typeof(TrimStringJsonConverter))]
        public string? ROHSStatus { get; set; }
        [JsonConverter(typeof(TrimStringJsonConverter))]
        public string? FactorySealed { get; set; }
        [JsonConverter(typeof(TrimStringJsonConverter))]
        public string? ManufacturerName { get; set; }
        [JsonConverter(typeof(TrimStringJsonConverter))]
        public string? DateCode { get; set; }
        [JsonConverter(typeof(TrimStringJsonConverter))]
        public string? PackageType { get; set; }
        [JsonConverter(typeof(TrimStringJsonConverter))]
        public string? ProductType { get; set; }
        [JsonConverter(typeof(TrimStringJsonConverter))]
        public string? MOQ { get; set; }
        [JsonConverter(typeof(TrimStringJsonConverter))]
        public string? TotalQSA { get; set; }
        [JsonConverter(typeof(TrimStringJsonConverter))]
        public string? LTB { get; set; }
        [JsonConverter(typeof(TrimStringJsonConverter))]
        public string? Notes { get; set; }
        [JsonRequired]
        public int CurrencyNo { get; set; }
        public int? MSLLevelNo { get; set; }
    }
}
