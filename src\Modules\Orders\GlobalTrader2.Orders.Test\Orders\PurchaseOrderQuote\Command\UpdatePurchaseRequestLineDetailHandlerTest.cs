using GlobalTrader2.Contacts.UseCases.Companies.Commands;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrderQuote.Command;

namespace GlobalTrader2.Orders.Test.Orders.PurchaseOrderQuote.Command
{
    public class UpdatePurchaseRequestLineDetailHandlerTest
    {
        private readonly Mock<IBaseRepository<object>> _repository;
        private readonly IFixture _fixture;
        private readonly UpdatePurchaseRequestLineDetailHandler _handler;

        public UpdatePurchaseRequestLineDetailHandlerTest()
        {
            _repository = new Mock<IBaseRepository<object>>();
            _fixture = new Fixture();
            _handler = new UpdatePurchaseRequestLineDetailHandler(_repository.Object);
        }

        [Fact]
        public async Task Handle_WhenValidRequest_ShouldReturnSuccess()
        {
            // Arrange
            var command = _fixture.Create<UpdatePurchaseRequestLineDetailCommand>();
            _repository.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                      .Callback<string, object[]>((sql, parameters) =>
                      {
                          var outputParam = parameters.OfType<SqlParameter>().FirstOrDefault(p => p.Direction == ParameterDirection.Output);
                              outputParam!.Value = 123;
                      })
                      .ReturnsAsync(1);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
        }

        [Fact]
        public async Task Handle_WhenRepositoryFails_ShouldReturnFailure()
        {
            // Arrange
            var command = _fixture.Create<UpdatePurchaseRequestLineDetailCommand>();
            _repository.Setup(x => x.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                      .Callback<string, object[]>((sql, parameters) =>
                      {
                          var outputParam = parameters.OfType<SqlParameter>().FirstOrDefault(p => p.Direction == ParameterDirection.Output);
                              outputParam!.Value = 0;
                      })
                      .ReturnsAsync(1);

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
        }
    }
}
