﻿using GlobalTrader2.Core;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Orders.UserCases.Orders.PrintEmailLog.Commands;
using Microsoft.Data.SqlClient;
using Moq;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.PrintEmailLog.Commands
{
    public class CreatePrintEmailLogHandlerTests
    {
        private readonly Mock<IBaseRepository<object>> _repositoryMock;
        private readonly CreatePrintEmailLogHandler _handler;
        private readonly IFixture _fixture;
        private readonly CreatePrintEmailLogCommand _command;

        public CreatePrintEmailLogHandlerTests()
        {
            _fixture = new Fixture();

            _repositoryMock = new Mock<IBaseRepository<object>>();
            _handler = new CreatePrintEmailLogHandler(_repositoryMock.Object);

            _command = _fixture.Create<CreatePrintEmailLogCommand>();
        }

        [Fact]
        public async Task Handle_WhenValidRequest_ShouldReturnSuccessResponse()
        {
            // Arrange
            _repositoryMock
                .Setup(repo => repo.ExecuteSqlRawAsync(
                        It.IsAny<string>(),
                        It.IsAny<object[]>()
                    ))
                .Callback<string, object[]>((query, parameters) =>
                {
                    var sqlParameters = parameters.Cast<SqlParameter>().ToArray();
                    var outputParam = sqlParameters.First(p => p.ParameterName == "@PrintDocumentLogId");
                    outputParam.Value = 123;
                })
                .Returns(Task.FromResult(1));

            // Act
            var result = await _handler.Handle(_command, CancellationToken.None);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(123, result.Data);

            _repositoryMock.Verify(repo =>
                repo.ExecuteSqlRawAsync(It.IsAny<string>(), It.IsAny<SqlParameter[]>()), Times.Once);
        }

        [Fact]
        public async Task Handle_WhenInsertDbFail_ShouldReturnFailureResponse()
        {
            // Arrange
            _repositoryMock
                .Setup(repo => repo.SqlQueryRawReturnValueAsync(
                    It.IsAny<string>(),
                    It.IsAny<object[]>()
                ))
                .Callback<string, object[]>((query, parameters) =>
                {
                    var sqlParameters = parameters.Cast<SqlParameter>().ToArray();
                    var outputParam = sqlParameters.First(p => p.ParameterName == "@TermsId");
                    outputParam.Value = DBNull.Value;
                })
                .Returns(Task.FromResult(new object()));

            // Act
            var result = await _handler.Handle(_command, CancellationToken.None);

            // Assert
            result.Success.Should().BeFalse();
            result.Data.Should().Be(0);
        }
    }
}