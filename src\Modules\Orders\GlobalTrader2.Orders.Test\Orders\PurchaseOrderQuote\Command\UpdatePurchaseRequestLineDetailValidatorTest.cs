using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrderQuote.Command;

namespace GlobalTrader2.Orders.Test.Orders.PurchaseOrderQuote.Command
{
    public class UpdatePurchaseRequestLineDetailValidatorTest
    {
        private readonly UpdatePurchaseRequestLineDetailValidator _validator;

        public UpdatePurchaseRequestLineDetailValidatorTest()
        {
            _validator = new UpdatePurchaseRequestLineDetailValidator();
        }

        [Fact]
        public async Task Validate_WhenSPQTooLong_ShouldHaveValidationError()
        {
            // Arrange
            var command = new UpdatePurchaseRequestLineDetailCommand
            {
                SPQ = "12345678901" // 11 characters, max is 10
            };

            // Act
            var result = await _validator.TestValidateAsync(command);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.SPQ);
        }

        [Fact]
        public async Task Validate_WhenLeadTimeTooLong_ShouldHaveValidationError()
        {
            // Arrange
            var command = new UpdatePurchaseRequestLineDetailCommand
            {
                LeadTime = new string('a', 51) // 51 characters, max is 50
            };

            // Act
            var result = await _validator.TestValidateAsync(command);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.LeadTime);
        }

        [Fact]
        public async Task Validate_WhenNotesTooLong_ShouldHaveValidationError()
        {
            // Arrange
            var command = new UpdatePurchaseRequestLineDetailCommand
            {
                Notes = new string('a', 501) // 501 characters, max is 500
            };

            // Act
            var result = await _validator.TestValidateAsync(command);

            // Assert
            result.ShouldHaveValidationErrorFor(x => x.Notes);
        }

        [Fact]
        public async Task Validate_WhenValidData_ShouldNotHaveValidationErrors()
        {
            // Arrange
            var command = new UpdatePurchaseRequestLineDetailCommand
            {
                SPQ = "123",
                LeadTime = "2 weeks",
                Notes = "Valid note"
            };

            // Act
            var result = await _validator.TestValidateAsync(command);

            // Assert
            result.ShouldNotHaveValidationErrorFor(x => x.SPQ);
            result.ShouldNotHaveValidationErrorFor(x => x.LeadTime);
            result.ShouldNotHaveValidationErrorFor(x => x.Notes);
        }
    }
}
