
namespace GlobalTrader2.Orders.UserCases.Orders.Requirements.ReleaseRequirement.Commands;

public class ReleaseRequirementHandler : IRequestHandler<ReleaseRequirementCommand, BaseResponse<bool>>
{
    private readonly IBaseRepository<object> _repository;

    public ReleaseRequirementHandler(IBaseRepository<object> repository)
    {
        _repository = repository;
    }

    public async Task<BaseResponse<bool>> Handle(ReleaseRequirementCommand request, CancellationToken cancellationToken)
    {
        var result = new BaseResponse<bool> { Success = false };
        var output = new SqlParameter("@RowsAffected", SqlDbType.Int) { Direction = ParameterDirection.Output };
        SqlParameter[] parameters =
        [
            new SqlParameter("@CustomerRequirementId", SqlDbType.Int) { Value = request.CustomerRequirementId },
            new SqlParameter("@UpdatedBy", SqlDbType.Int) { Value = request.LoginId },
            output,
            new SqlParameter("@BomId", SqlDbType.Int) { Value = request.BomId }
        ];

        string procedureName = StoredProcedures.Update_CustomerRequirement_Release;
        var queryStr = $"{procedureName} @CustomerRequirementId, @UpdatedBy, @RowsAffected OUTPUT, @BomId";

        await _repository.ExecuteSqlRawAsync(queryStr, parameters);

        var rowsAffected = (int)output.Value;
        result.Success = rowsAffected > 0;
        result.Data = result.Success;

        return result;

    }
}
