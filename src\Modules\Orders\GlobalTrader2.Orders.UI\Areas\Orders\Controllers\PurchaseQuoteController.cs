﻿using GlobalTrader2.Aggregator.UseCases.Helper;
using GlobalTrader2.Dto.Datatables;
using GlobalTrader2.Dto.Orders.PurchaseQuote;
using GlobalTrader2.Orders.UI.ViewModel.PurchaseQuote;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrderQuote;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrderQuote.Queries;
using GlobalTrader2.SharedUI;
using Microsoft.Extensions.Localization;
using GlobalTrader2.Orders.UI.ViewModel.BOM;
using GlobalTrader2.PurchaseRequest.UseCases.Commands.Create;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrderQuote.Command;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers;

[ApiController]
[Authorize]
[Route("api/orders/purchase-quote")]
public class PurchaseQuoteController(IMediator mediator, SessionManager sessionManager, IMapper mapper, IStringLocalizer<Misc> miscLocalizer, SecurityManager securityManager) : ApiBaseController
{
    private readonly IMediator _mediator = mediator;
    private readonly SessionManager _sessionManager = sessionManager;
    private readonly IMapper _mapper = mapper;
    private readonly IStringLocalizer<Misc> _miscLocalizer = miscLocalizer;

    [HttpPost("list")]
    public async Task<IActionResult> GetPurchaseQuote(GetPurchaseQuoteRequest request, CancellationToken cancellationToken)
    {
        request.ClientId = ClientId;
        request.LoginId = (request.ViewLevelList == (int)ViewLevelList.My) ? UserId : null;
        request.TeamId = (request.ViewLevelList == (int)ViewLevelList.Team) ? _sessionManager.GetInt32(SessionKey.LoginTeamID) : null;
        request.DivisionId = (request.ViewLevelList == (int)ViewLevelList.Division) ? _sessionManager.GetInt32(SessionKey.LoginDivisionID) : null;

        var query = new GetPOQuoteLineDataListNuggetQuery
        {
            PageIndex = request.Index,
            PageSize = request.Size,
            OrderBy = request.OrderBy,
            SortDir = request.SortDir,
            ClientId = request.ClientId,
            LoginId = request.LoginId,
            TeamId = request.TeamId,
            DivisionId = request.DivisionId,
            PartSearch = request.PartSearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.PartSearch) : null,
            CmSearch = request.CMSearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.CMSearch) : null,
            PoQuoteNoHi = request.POQuoteNoHi,
            PoQuoteNoLo = request.POQuoteNoLo,
            DateQuotedFrom = request.DatePOQuotedFrom,
            DateQuotedTo = request.DatePOQuotedTo,
            IncludeClosed = request.IncludeClosed ?? false,
            RecentOnly = request.RecentOnly ?? false,
            SalesmanSearch = request.Salesman,
        };

        var result = await _mediator.Send(query, cancellationToken);
        var resultView = _mapper.Map<BaseResponse<IEnumerable<PurchaseQuoteViewModel>>>(result);

        var totalItems = result.Data?.FirstOrDefault()?.RowCnt ?? 0;

        var response = new DatatableResponse<IEnumerable<PurchaseQuoteViewModel>>()
        {
            Success = resultView.Success,
            Data = resultView.Data,
            RecordsTotal = totalItems,
            RecordsFiltered = totalItems,
            Draw = request.Draw
        };

        return Ok(response);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetPurchaseOrder(int id)
    {
        var result = await _mediator.Send(new GetPOQuoteDetailsQuery(id));

        if(result.Data != null)
        {
            result.Data.LastUpdatedByInText = await FormatDLUP(_mediator, _miscLocalizer, null, result.Data.DLUP);
        }

        return Ok(result);
    }

    [HttpGet("{pOQuoteId}/quote-lines")]
    public async Task<IActionResult> GetPurchaseOrderQuoteLines(int pOQuoteId)
    {
        if (!IsPOHub)
        {
            return Forbid();
        }

        var result = await _mediator.Send(new GetPOQuoteLinesQuery()
        {
            POQuoteId = pOQuoteId
        });

        return Ok(result);
    }

    [HttpGet("{pOQuoteId}/purchase-request-line-detail")]
    public async Task<IActionResult> PurchaseRequestLineDetai(int pOQuoteId)
    {
        if (!IsPOHub)
        {
            return Forbid();
        }

        var result = await _mediator.Send(new GetPurchaseRequestLineDetailsQuery()
        {
            PurchaseRequirementLineNo = pOQuoteId
        });

        var poLineResult = _mapper.Map<BaseResponse<IEnumerable<PurchaseOrderRequestLineDetailResponse>>>(result);
        var poLineData = poLineResult.Data!.ToList();

        foreach (var item in poLineData)
        {
            var amount = await _mediator.ConvertValueToBaseCurrencyAsync(item.Price, item.CurrencyNo!.Value, item.DLUP);
            item.BasePrice = Functions.FormatCurrency(amount, _sessionManager.ClientCurrencyCode);
            item.StrPrice = Functions.FormatCurrency(item.Price, item.CurrencyCode);
        }

        poLineResult.Data = poLineData;
        return Ok(poLineResult);
    }

    [HttpPost("line-log")]
    public async Task<IActionResult> GetLog(GetPOQuoteLineLogRequest request)
    {
        if (!IsPOHub)
        {
            return Forbid();
        }

        if (!request.POQuoteID.HasValue && !request.BomID.HasValue)
        {
            return BadRequest("Either POQuoteID or BomID must be not null");
        }

        if (request.BomID.HasValue)
        {
            var bomLogResult = await _mediator.Send(new GetPOQuoteLineLogBomQuery(request.BomID!.Value));

            return Ok(_mapper.Map<BaseResponse<IEnumerable<POLineLogResponse>>>(bomLogResult));
        }



        var quoteLineResult = await _mediator.Send(new GetPOQuoteLineLogQuery()
        {
            ID = request.POQuoteID!.Value
        });

        return Ok(_mapper.Map<BaseResponse<IEnumerable<POLineLogResponse>>>(quoteLineResult));
    }

    [HttpDelete("purchase-request-line/{id}/delete")]
    public async Task<IActionResult> DeleteRequestLine(int id)
    {
        var request = new DeletePurchaseRequestLineCommand { Id = id };
        var response = await _mediator.Send(request);
        return Ok(response);
    }

    [HttpGet("{companyId}/get-default-purchasing-info")]
    public async Task<IActionResult> GetDefaultPurchasingInfo(int companyId)
    {
        var result = await _mediator.Send(new GetDefaultPurchasingInfoQuery()
        {
            CompanyId = companyId
        });

        return Ok(result);
    }

    [HttpPost("create-purchase-request-line")]
    public async Task<IActionResult> CreatePurchaseRequestLine([FromBody] CreatePurchaseRequestLineDetailRequest request)
    {
        if (!IsPOHub)
        {
            return Forbid();
        }

        var command = new CreatePurchaseRequestLineDetailCommand
        {
            PurchaseRequestLineNo = request.PurchaseRequestLineNo,
            CompanyNo = request.CompanyNo,
            Price = request.Price,
            SPQ = request.SPQ,
            LeadTime = request.LeadTime,
            ROHSStatus = request.ROHSStatus,
            FactorySealed = request.FactorySealed,
            ManufacturerName = request.ManufacturerName,
            DateCode = request.DateCode,
            PackageType = request.PackageType,
            ProductType = request.ProductType,
            MOQ = request.MOQ,
            TotalQSA = request.TotalQSA,
            LTB = request.LTB,
            Notes = request.Notes,
            UpdatedBy = UserId,
            CurrencyNo = request.CurrencyNo,
            MSLLevelNo = request.MSLLevelNo,
            IsPoHub = true
        };

        var response = await _mediator.Send(command);
        return Ok(response);
    }


    [HttpPut("update-purchase-request-line")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> UpdatePurchaseRequestLine([FromBody] UpdatePurchaseRequestLineDetailRequest request)
    {
        if (!IsPOHub)
        {
            return Forbid();
        }

        var command = new UpdatePurchaseRequestLineDetailCommand
        {
            PurchaseRequestLineNo = request.PurchaseRequestLineNo,
            CompanyNo = request.CompanyNo,
            Price = request.Price,
            SPQ = request.SPQ,
            LeadTime = request.LeadTime,
            ROHSStatus = request.ROHSStatus,
            FactorySealed = request.FactorySealed,
            ManufacturerName = request.ManufacturerName,
            DateCode = request.DateCode,
            PackageType = request.PackageType,
            ProductType = request.ProductType,
            MOQ = request.MOQ,
            TotalQSA = request.TotalQSA,
            LTB = request.LTB,
            Notes = request.Notes,
            UpdatedBy = UserId,
            CurrencyNo = request.CurrencyNo,
            MSLLevelNo = request.MSLLevelNo,
            PurchaseRequestLineDetailId = request.PurchaseRequestLineDetailId
        };

        var response = await _mediator.Send(command);
        return Ok(response);
    }
}
